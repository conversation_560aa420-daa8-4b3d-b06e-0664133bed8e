{"logs": [{"outputFile": "com.amlacameroon.sandbox-mergeReleaseResources-42:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,346,347,348,349,566,569", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,25343,25459,25585,25711,38415,38587", "endLines": "2,17,18,19,346,347,348,349,568,573", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,25454,25580,25706,25834,38582,38934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b88e4f1102d8b08c17e772a2c7829ea1\\transformed\\appcompat-1.5.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,38,40,41,42,43,45,47,48,49,50,51,53,55,57,59,61,63,64,69,71,73,74,75,77,79,80,81,82,87,99,142,145,188,203,215,217,219,221,224,228,231,232,233,236,237,238,239,240,241,244,245,247,249,251,253,257,259,260,261,262,264,268,270,272,273,274,275,276,277,308,309,310,320,321,322,334", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1760,1851,1954,2057,2162,2269,2378,2487,2596,2705,2814,2921,3024,3143,3298,3453,3558,3679,3780,3927,4068,4171,4290,4397,4500,4655,4826,4975,5140,5297,5448,5567,5918,6067,6216,6328,6475,6628,6775,6850,6939,7026,7551,8643,11401,11586,14356,15489,16341,16464,16587,16700,16883,17138,17339,17428,17539,17772,17873,17968,18091,18220,18337,18514,18613,18748,18891,19026,19145,19346,19465,19558,19669,19725,19832,20027,20138,20271,20366,20457,20548,20641,20758,23191,23262,23345,23968,24025,24083,24707", "endLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,37,39,40,41,42,44,46,47,48,49,50,52,54,56,58,60,62,63,68,70,72,73,74,76,78,79,80,81,82,87,141,144,187,190,205,216,218,220,223,227,230,231,232,235,236,237,238,239,240,243,244,246,248,250,252,256,258,259,260,261,263,267,269,271,272,273,274,275,276,278,308,309,319,320,321,333,345", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1846,1949,2052,2157,2264,2373,2482,2591,2700,2809,2916,3019,3138,3293,3448,3553,3674,3775,3922,4063,4166,4285,4392,4495,4650,4821,4970,5135,5292,5443,5562,5913,6062,6211,6323,6470,6623,6770,6845,6934,7021,7122,7649,11396,11581,14351,14548,15683,16459,16582,16695,16878,17133,17334,17423,17534,17767,17868,17963,18086,18215,18332,18509,18608,18743,18886,19021,19140,19341,19460,19553,19664,19720,19827,20022,20133,20266,20361,20452,20543,20636,20753,20892,23257,23340,23963,24020,24078,24702,25338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e58339227eea27976199f5cf8fe7c88\\transformed\\material-1.8.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,28,31,34,37,40,43,46,49,52,55,56,59,64,75,81,91,101,111,121,131,141,151,161,171,181,191,201,211,221,231,237,243,249,255,259,263,264,265,266,270,273,276,279,282,283,286,289,293,297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,1960,2173,2432,2703,2921,3153,3389,3639,3852,4061,4292,4493,4609,4779,5100,6129,6586,7137,7692,8248,8809,9361,9912,10464,11017,11566,12119,12675,13230,13776,14330,14885,15177,15471,15771,16071,16400,16741,16879,17023,17179,17572,17790,18012,18238,18454,18564,18734,18924,19165,19424", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,51,54,55,58,63,74,80,90,100,110,120,130,140,150,160,170,180,190,200,210,220,230,236,242,248,254,258,262,263,264,265,269,272,275,278,281,282,285,288,292,296,299", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,1955,2168,2427,2698,2916,3148,3384,3634,3847,4056,4287,4488,4604,4774,5095,6124,6581,7132,7687,8243,8804,9356,9907,10459,11012,11561,12114,12670,13225,13771,14325,14880,15172,15466,15766,16066,16395,16736,16874,17018,17174,17567,17785,18007,18233,18449,18559,18729,18919,19160,19419,19596"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,83,84,85,86,88,89,90,93,96,191,194,197,200,206,209,212,279,282,283,286,291,302,350,360,370,380,390,400,410,420,430,440,450,460,470,480,490,500,506,512,518,539,543,547,548,549,550,554,557,560,563,574,575,578,581,585,589", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,7127,7220,7327,7432,7654,7779,7900,8113,8372,14553,14771,15003,15239,15688,15901,16110,20897,21098,21214,21384,21705,22734,25839,26390,26945,27501,28062,28614,29165,29717,30270,30819,31372,31928,32483,33029,33583,34138,34430,34724,35024,36032,36361,36702,36840,36984,37140,37533,37751,37973,38199,38939,39049,39219,39409,39650,39909", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,83,84,85,86,88,89,92,95,98,193,196,199,202,208,211,214,281,282,285,290,301,307,359,369,379,389,399,409,419,429,439,449,459,469,479,489,499,505,511,517,523,542,546,547,548,549,553,556,559,562,565,574,577,580,584,588,591", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,7215,7322,7427,7546,7774,7895,8108,8367,8638,14766,14998,15234,15484,15896,16105,16336,21093,21209,21379,21700,22729,23186,26385,26940,27496,28057,28609,29160,29712,30265,30814,31367,31923,32478,33024,33578,34133,34425,34719,35019,35319,36356,36697,36835,36979,37135,37528,37746,37968,38194,38410,39044,39214,39404,39645,39904,40081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a7c7fb7b5574366d60fd07efaae1ff10\\transformed\\jetified-camera-camera2-1.0.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "13", "endOffsets": "207"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1603", "endLines": "22", "endColumns": "13", "endOffsets": "1755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d8eb4647bfff8495bf1c0a39c269c2b\\transformed\\jetified-WD300Android\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,7,12", "startColumns": "4,4,4", "startOffsets": "55,281,525", "endLines": "6,11,16", "endColumns": "12,12,12", "endOffsets": "276,520,758"}, "to": {"startLines": "524,529,534", "startColumns": "4,4,4", "startOffsets": "35324,35550,35794", "endLines": "528,533,538", "endColumns": "12,12,12", "endOffsets": "35545,35789,36027"}}]}]}