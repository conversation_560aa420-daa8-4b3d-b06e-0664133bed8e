/**
 * Code generated by WINDEV Mobile - DO NOT MODIFY!
 * WINDEV Mobile object: Collection
 * Android class: SDKInt
 * Version of wdjava64.dll: 30.0.302.8
 */


package com.amlacameroon.sandbox.wdgen;


import com.amlacameroon.sandbox.*;
import fr.pcsoft.wdjava.core.types.*;
import fr.pcsoft.wdjava.core.*;
import fr.pcsoft.wdjava.core.application.*;
import fr.pcsoft.wdjava.api.*;
import fr.pcsoft.wdjava.core.erreur.*;
/*Imports trouvés dans le code WL*/
import java.util.LinkedHashSet;
import com.google.gson.Gson;
import java.util.HashMap;
import android.util.Base64;
import java.io.InputStream;
import java.io.IOException;
import android.widget.Toast;
import android.content.Context;
import ai.tech5.finger.utils.T5FingerCapturedListener;
import ai.tech5.finger.utils.T5FingerCaptureController;
import ai.tech5.finger.utils.SegmentationMode;
import ai.tech5.finger.utils.ImageType;
import ai.tech5.finger.utils.ImageConfiguration;
import ai.tech5.finger.utils.FingerCaptureResult;
import ai.tech5.finger.utils.CaptureSpeed;
import ai.tech5.finger.utils.Finger;
import ai.tech5.finger.utils.CaptureMode;
import org.json.JSONObject;
import org.json.JSONException;
import org.json.JSONArray;
import java.util.*;
import java.util.Arrays;
import java.util.Iterator;
import java.nio.file.Files;
import java.io.IOException;
import java.io.File;
import android.os.Build;
import android.util.Base64;
import Tech5.OmniMatch.Common;
import Tech5.OmniMatch.MatcherCommon;
import Tech5.OmniMatch.JNI.OmniMatchException;
import ai.tech5.omnimatchutil.OmniMatchUtil;
import ai.tech5.omnimatchutil.Listener;
import ai.tech5.tlvdecode.TLVDecodeException;
import ai.tech5.tlvdecode.Result;
import ai.tech5.tlvdecode.CryptographDecodeUtil;
import ai.tech5.cryptograph.reader.api.ScannerConfig;
import ai.tech5.cryptograph.reader.api.CryptographReaderAction;
import ai.tech5.cryptograph.reader.api.CryptographReader;
import android.widget.Toast;
import android.content.Context;
import org.json.JSONObject;
import org.json.JSONException;
import org.json.JSONArray;
import java.util.*;
import java.util.Arrays;
import java.util.Iterator;
import java.nio.file.Files;
import java.io.IOException;
import java.io.File;
import android.os.Build;
import android.util.Base64;
import Tech5.OmniMatch.Common;
import Tech5.OmniMatch.MatcherCommon;
import Tech5.OmniMatch.JNI.OmniMatchException;
import ai.tech5.omnimatchutil.OmniMatchUtil;
import ai.tech5.omnimatchutil.Listener;
import ai.tech5.tlvdecode.TLVDecodeException;
import ai.tech5.tlvdecode.Result;
import ai.tech5.tlvdecode.CryptographDecodeUtil;
import androidx.annotation.Nullable;
import android.widget.Toast;
import ai.tech5.omnimatchutil.OmniMatchInitCompletion;
import ai.tech5.omnimatchutil.Listener;
import ai.tech5.cryptograph.reader.api.ScannerConfig;
import ai.tech5.cryptograph.reader.api.CryptographReaderAction;
import ai.tech5.cryptograph.reader.api.CryptographReader;
import android.widget.Toast;
import android.content.Context;
import java.util.LinkedHashSet;
import com.google.gson.Gson;
import java.util.HashMap;
import android.util.Base64;
import java.io.InputStream;
import java.io.IOException;
import android.widget.Toast;
import android.content.Context;
import ai.tech5.finger.utils.T5FingerCapturedListener;
import ai.tech5.finger.utils.T5FingerCaptureController;
import ai.tech5.finger.utils.SegmentationMode;
import ai.tech5.finger.utils.ImageType;
import ai.tech5.finger.utils.ImageConfiguration;
import ai.tech5.finger.utils.FingerCaptureResult;
import ai.tech5.finger.utils.CaptureSpeed;
import ai.tech5.finger.utils.Finger;
import ai.tech5.finger.utils.CaptureMode;
import android.util.Base64;
import ai.tech5.pheonix.capture.controller.FaceCaptureListener;
import ai.tech5.pheonix.capture.controller.FaceCaptureController;
import ai.tech5.pheonix.capture.controller.AirsnapFaceThresholds;
import com.phoenixcapture.camerakit.FaceBox;
import com.phoenixcapture.camerakit.AirsnapFace;
import java.io.FileOutputStream;
import java.io.File;
import org.json.JSONObject;
import org.json.JSONException;
import org.json.JSONArray;
import java.util.*;
import java.util.Arrays;
import java.util.Iterator;
import java.nio.file.Files;
import java.io.IOException;
import java.io.File;
import android.os.Build;
import android.util.Base64;
import Tech5.OmniMatch.Common;
import Tech5.OmniMatch.MatcherCommon;
import Tech5.OmniMatch.JNI.OmniMatchException;
import ai.tech5.omnimatchutil.OmniMatchUtil;
import ai.tech5.omnimatchutil.Listener;
import ai.tech5.tlvdecode.TLVDecodeException;
import ai.tech5.tlvdecode.Result;
import ai.tech5.tlvdecode.CryptographDecodeUtil;
import java.util.LinkedHashSet;
import com.google.gson.Gson;
import java.util.HashMap;
import android.util.Base64;
import java.io.InputStream;
import java.io.IOException;
import android.widget.Toast;
import android.content.Context;
import ai.tech5.finger.utils.T5FingerCapturedListener;
import ai.tech5.finger.utils.T5FingerCaptureController;
import ai.tech5.finger.utils.SegmentationMode;
import ai.tech5.finger.utils.ImageType;
import ai.tech5.finger.utils.ImageConfiguration;
import ai.tech5.finger.utils.FingerCaptureResult;
import ai.tech5.finger.utils.CaptureSpeed;
import ai.tech5.finger.utils.Finger;
import ai.tech5.finger.utils.CaptureMode;
import java.util.LinkedHashSet;
import com.google.gson.Gson;
import java.util.HashMap;
import android.util.Base64;
import java.io.InputStream;
import java.io.IOException;
import android.widget.Toast;
import android.content.Context;
import ai.tech5.finger.utils.T5FingerCapturedListener;
import ai.tech5.finger.utils.T5FingerCaptureController;
import ai.tech5.finger.utils.SegmentationMode;
import ai.tech5.finger.utils.ImageType;
import ai.tech5.finger.utils.ImageConfiguration;
import ai.tech5.finger.utils.FingerCaptureResult;
import ai.tech5.finger.utils.CaptureSpeed;
import ai.tech5.finger.utils.Finger;
import ai.tech5.finger.utils.CaptureMode;
import android.util.Base64;
import ai.tech5.pheonix.capture.controller.FaceCaptureListener;
import ai.tech5.pheonix.capture.controller.FaceCaptureController;
import ai.tech5.pheonix.capture.controller.AirsnapFaceThresholds;
import com.phoenixcapture.camerakit.FaceBox;
import com.phoenixcapture.camerakit.AirsnapFace;
import java.io.FileOutputStream;
import java.io.File;
import android.util.Base64;
import ai.tech5.pheonix.capture.controller.FaceCaptureListener;
import ai.tech5.pheonix.capture.controller.FaceCaptureController;
import ai.tech5.pheonix.capture.controller.AirsnapFaceThresholds;
import com.phoenixcapture.camerakit.FaceBox;
import com.phoenixcapture.camerakit.AirsnapFace;
import java.io.FileOutputStream;
import java.io.File;
import androidx.annotation.Nullable;
import androidx.annotation.NonNull;
import android.util.Log;
import ai.tech5.omnimatchutil.OmniMatchMatchCompletion;
import Tech5.OmniMatch.JNI.CoreNative;
import ai.tech5.omnimatchutil.Listener;
import android.graphics.BitmapFactory;
import android.graphics.Bitmap;
import android.os.Environment;
import ai.tech5.tlvdecode.Data;
import org.json.JSONObject;
import org.json.JSONException;
import org.json.JSONArray;
import ai.tech5.omnimatchutil.OmniMatchUtil;
import Tech5.OmniMatch.JNI.OmniMatchException;
import Tech5.OmniMatch.Common;
import com.google.protobuf.InvalidProtocolBufferException;
import java.io.UnsupportedEncodingException;
import java.io.File;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.io.InputStream;
import java.io.IOException;
import androidx.fragment.app.Fragment;
import androidx.annotation.NonNull;
import android.widget.Toast;
import android.view.ViewGroup;
import android.view.View;
import android.view.LayoutInflater;
import android.util.Log;
import android.os.Bundle;
import ai.tech5.sdk.abis.cryptograph.T5CryptoClient;
import java.util.*;
import java.util.Arrays;
import java.util.Iterator;
import java.nio.file.Files;
import java.io.IOException;
import java.io.File;
import android.os.Build;
import android.util.Base64;
import ai.tech5.tlvdecode.TLVDecodeException;
import ai.tech5.tlvdecode.Result;
import ai.tech5.tlvdecode.CryptographDecodeUtil;
import org.json.JSONObject;
import org.json.JSONException;
import org.json.JSONArray;
import ai.tech5.omnimatchutil.OmniMatchUtilException;
import ai.tech5.omnimatchutil.OmniMatchUtil;
import Tech5.OmniMatch.MatcherCommon;
import Tech5.OmniMatch.JNI.OmniMatchException;
import Tech5.OmniMatch.Common;
import java.io.UnsupportedEncodingException;
import java.io.File;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.io.InputStream;
import java.io.IOException;
import androidx.fragment.app.Fragment;
import androidx.annotation.NonNull;
import android.widget.Toast;
import android.view.ViewGroup;
import android.view.View;
import android.view.LayoutInflater;
import android.util.Log;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.widget.Toast;
import ai.tech5.omnimatchutil.OmniMatchInitCompletion;
import ai.tech5.omnimatchutil.Listener;
import java.util.*;
import java.util.Arrays;
import java.util.Iterator;
import java.nio.file.Files;
import java.io.IOException;
import java.io.File;
import android.os.Build;
import android.util.Base64;
import ai.tech5.tlvdecode.TLVDecodeException;
import ai.tech5.tlvdecode.Result;
import ai.tech5.tlvdecode.CryptographDecodeUtil;
import android.graphics.BitmapFactory;
import android.graphics.Bitmap;
import android.os.Environment;
import ai.tech5.tlvdecode.Data;
import org.json.JSONObject;
import org.json.JSONException;
import org.json.JSONArray;
import ai.tech5.omnimatchutil.OmniMatchUtilException;
import ai.tech5.omnimatchutil.OmniMatchUtil;
import Tech5.OmniMatch.MatcherCommon;
import Tech5.OmniMatch.JNI.OmniMatchException;
import Tech5.OmniMatch.Common;
import java.io.UnsupportedEncodingException;
import java.io.File;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.io.InputStream;
import java.io.IOException;
import androidx.fragment.app.Fragment;
import androidx.annotation.NonNull;
import android.widget.Toast;
import android.view.ViewGroup;
import android.view.View;
import android.view.LayoutInflater;
import android.util.Log;
import android.os.Bundle;
import ai.tech5.sdk.abis.cryptograph.T5CryptoClient;
import java.util.*;
import java.util.Arrays;
import java.util.Iterator;
import java.nio.file.Files;
import java.io.IOException;
import java.io.File;
import android.os.Build;
import android.util.Base64;
import ai.tech5.tlvdecode.TLVDecodeException;
import ai.tech5.tlvdecode.Result;
import ai.tech5.tlvdecode.CryptographDecodeUtil;
import android.content.Context;
import ai.tech5.sdk.abis.T5AirSnap.T5AirSnap;
/*Fin Imports trouvés dans le code WL*/



public class GWDCPSDKInt extends WDCollProcAndroid
{

public WDProjet getProjet()
{
return GWDPintegrationSdk.getInstance();
}

public IWDEnsembleElement getEnsemble()
{
return GWDPintegrationSdk.getInstance();
}

protected String getNomCollection()
{
return "SDKInt";
}
private final static GWDCPSDKInt ms_instance = new GWDCPSDKInt();
public final static GWDCPSDKInt getInstance()
{
return ms_instance;
}

// Code de déclaration de SDKInt
static public void init()
// 
{
// 
ms_instance.initDeclarationCollection();



try
{

}
finally
{
finDeclarationCollection();

}
}




// Code de terminaison de SDKInt
static public void term()
// 
{
// 
ms_instance.initTerminaisonCollection();



try
{

}
finally
{
finTerminaisonCollection();

}
}



// Nombre de Procédures : 22
//import com.windev.android.*;

public static void testSdk()
{
	//  JavaCode is string = ""
	//	JavaCode += "import android.content.Context;"   
	//	JavaCode += "import com.windev.android.*;"    
	//	JavaCode += "public class GetContext {" 
	//	JavaCode += "  public static Context getAppContext() {"  
	//	JavaCode += "    return WDApp.getContext();"    
	//	JavaCode += "  }"    
	//	JavaCode += "}"       
	//	
	//	AndroidContext is int = JNIExecute(JavaCode, "GetContext.getAppContext()", True)        
	//	Use the Android context as needed    // For example, display a toast message:    JNIExecute("android.widget.Toast.makeText("+ AndroidContext +", \"Hello from Android Context!\", android.widget.Toast.LENGTH_SHORT).show();")ENDPROCEDURE

	T5AirSnap     m_cellSdk = new T5AirSnap(getApplicationContext());
	String version =     m_cellSdk.getVersion();
	 
	appelProcedureWL_String("printProc",version);
	//System.out.println(version);
	
}
	

// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// testSdk ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure testSdk()
//
//
//

// Résumé : <indiquez ici ce que fait la procédure>
// Syntax:
// [ <Result> = ] printJsonRet (<sRetourRequette> is string)
// 
// Parameters:
// sRetourRequette (Unicode string): <indiquez ici le rôle de el>
// Exemple :
// <Indiquez ici un exemple d'utilisation>
// 
// Return value:
// Unicode string: // 	Aucune
// procédure printJsonRet(sRetourRequette est une chaine)

static public WDObjet fWD_printJsonRet( WDObjet vWD_sRetourRequette )
{
// procédure printJsonRet(sRetourRequette est une chaine)
ms_instance.initExecProcGlobale("printJsonRet");



try
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_fi = new WDChaineU();



vWD_sRetourRequette = WDParametre.traiterParametre(vWD_sRetourRequette, 1, 16, 0x0);



try
{
// 	run.edit_retour_API = sRetourRequette
GWDPintegrationSdk.getInstance().getrun().mWD_edit_retour_API.setValeur(vWD_sRetourRequette);

// 	retourapi = StringToJSON(sRetourRequette)
GWDPintegrationSdk.getInstance().vWD_retourapi.setValeur(WDAPIJSON.chaineVersJSON(vWD_sRetourRequette));

// <compile if Configuration="Application Android">
// 	fi est une chaine = retourapi.originalResult.compressedImage.content

vWD_fi.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("originalResult").get("compressedImage").get("content"));


// 	DecompressFaceImage(fi)
DecompressFaceImage(vWD_fi.getString());


// 	ToastDisplay("Crypto text returned succesfully")
WDAPIToast.toastAffiche(new WDChaineU("Crypto text returned succesfully"));

// RETURN "                                                                                                                                                                                                      "
return new WDChaineU("                                                                                                                                                                                                      ");

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return eCatch.getValeurRetour();
}
}
finally
{
finExecProcGlobale();

}
}


// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure CryptographDecode()
//
//


//import androidx.navigation.fragment.NavHostFragment;


//import ai.tech5.finger.databinding.FragmentSecondBinding;



public static void CryptographDecode(String image, String key)
{

//	
//	byte[] imageBytes =  Base64.decode(image,Base64.NO_WRAP);
//	
////	if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {    
////		try {        
////			Files.write(new File(getApplicationContext().getExternalFilesDir(null)+File.separator+System.currentTimeMillis()+"crypto.png").toPath(), imageBytes);    
////		} catch (IOException e) {           
////			
////		}
////	}
//	//String key = "0c5204df931ae3e7e75b9fd99d8098a7adf29fc1dfd931150133ea222fe0b0e616ecd232cb12a6f5391f6ee20062386d7e1e105d9d8cf4911d9f8fe949cbe9cf6301b4bcb208abbf204ee13377aa35091e9edbac97c41500b8c917e6bee4a6fa36659e6369fd6e658fb517e8518575f6c4703922332b9ba79cda52ede2b9e0be"
//	
//	CryptographDecodeUtil cryptographDecodeUtil = new CryptographDecodeUtil();
//	
//	Result result = null;
//	
//	try {
//		result = cryptographDecodeUtil.decodeCryptogrpah(getApplicationContext(), key, imageBytes);
//	} catch (TLVDecodeException e) {
//		Toast.makeText(getApplicationContext(), "In exception.... ", Toast.LENGTH_LONG).show();
//		e.printStackTrace();
//	}
//	//result code 13 = no image sent  
	//Toast.makeText(getApplicationContext(), "code 1 : "+result.getCryptographDecodeResultCode(), Toast.LENGTH_LONG).show();
	//Toast.makeText(getApplicationContext(), "code 2 : "+result.getCryptographClientInitResultCode(), Toast.LENGTH_LONG).show();
//	
//	if(result != null && !result.getFingerprints().isEmpty()){
//		//JSONArray fingerDataArray = new JSONArray();
//		//Parcourir le Hashmap avec la boucle For
//		try {
//			for (Map.Entry m : result.getFingerprints().entrySet()) {
//				
//				JSONObject fingr = new JSONObject();
//				fingr.put("pos", ""+m.getKey());
//				fingr.put("val", m.getValue().getContent());
//				fingerDataArray.put(fingr);
//				//System.out.println("ID: "+m.getKey()+", Nom: "+m.getValue());
//				//appelProcedureWL_String("printProc", new String("Index: "+m.getKey()+", contenu : "+m.getValue()));
//				//Toast.makeText(getApplicationContext(), "ID: "+m.getKey()+", Nom: "+m.getValue(), Toast.LENGTH_LONG).show();
//			}
//			appelProcedureWL_String("printProc", fingerDataArray.toString());
//			//appelProcedureWL_String("printProc", new String(result.getFaceTemplate()));
//		} catch (JSONException e) 
//		{
//			//e.printStackTrace();
//			Toast.makeText(getApplicationContext(),  e.getMessage(), Toast.LENGTH_LONG).show();
//			//appelProcedureWL_String("getQueryResultAndEcho", "Error :" + e.getMessage());
//			//Log.i(TAG, "Error :" + e.getMessage());
//		}
//		//FingerTemplateCreationAndMatching().createNISTT5FingerTemplate();
//	}else{
//		Toast.makeText(getApplicationContext(), "Pas de contenu dans FP", Toast.LENGTH_LONG).show();
//	}
//	
//	if(result != null && result.getDemographics() != null){
//		//Toast.makeText(getApplicationContext(), "in result different to null... ", Toast.LENGTH_LONG).show();
//		//Toast.makeText(getApplicationContext(), new String(result.getDemographics().getContent()), Toast.LENGTH_LONG).show();
//		appelProcedureWL_String("printProc", new String(result.getDemographics().getContent()));
//		//result.getFingerprints() to get template from crypto
//		//Map<Integer, Data> getFingerprints() 
//		
//	}else{
//		appelProcedureWL_String("printProc","Result is null");
//	}
	
//	//byte[] fingerImage = Base64.decode(imageString,Base64.NO_WRAP);
//	byte[] fingerImage10 = readFileFromAsset("2023072613357654_1690374969303_finger_10.png");
//	byte[] fingerImage9 = readFileFromAsset("2023072613357654_1690374969301_finger_9.png");
//	byte[] fingerImage8 = readFileFromAsset("2023072613357654_1690374969295_finger_8.png");
//	byte[] fingerImage7 = readFileFromAsset("2023072613357654_1690374969298_finger_7.png");
//	byte[] fingerImage6 = readFileFromAsset("2023072613357654_1690374988844_finger_6.png");
//	byte[] fingerImage5 = readFileFromAsset("2023072613357654_1690375011049_finger_5.png");
//	byte[] fingerImage4 = readFileFromAsset("2023072613357654_1690375011042_finger_4.png");
//	byte[] fingerImage3 = readFileFromAsset("2023072613357654_1690375011052_finger_3.png");
//	byte[] fingerImage2 = readFileFromAsset("2023072613357654_1690375011046_finger_2.png");
//	byte[] fingerImage1 = readFileFromAsset("2023072613357654_1690375032050_finger_1.png");
//	
//	OmniMatchUtil omniMatchUtil = new OmniMatchUtil();
//	byte[] template1 = omniMatchUtil.createNISTT5FingerTemplate(fingerImage10, Common.ImageFormat.PNG);
//	
//	byte[] template2 = omniMatchUtil.createNISTT5FingerTemplate(fingerImage10, Common.ImageFormat.PNG);
//	//appelProcedureWL_String("printProc", "Templates created succesfully");
//	if (template1 != null && template1.length > 0 && template2 != null && template2.length > 0) {
//		float score = omniMatchUtil.matchFingerMinexNistT5Templates(template1, template2);
//		appelProcedureWL_String("printProc", "score " + score);
//		//Toast.makeText(getApplicationContext(),"score " + score, Toast.LENGTH_LONG).show(); 
//		//Log.d("TAG", "score " + score);
//	} else {
//		//Toast.makeText(getApplicationContext(),"Template creation failed", Toast.LENGTH_LONG).show();
//		appelProcedureWL_String("printProc", "Template creation failed");
//		Log.d("TAG", "Template creation failed");
//	}
	
//	
//	try {
//		
//		//byte[] template = omniMatchUtil.createNNLiteFingerTemplate(fingerImage, 1, Common.ImageFormat.PNG);
////		//WSQ PNG JPG
////		byte[] rightThumbImage = readFileFromAssets("2000000000000001_LI.WSQ");
////		byte[] rightIndexImage = readFileFromAssets("2000000000000001_LI.WSQ");
//		
//		HashMap<Integer, byte[]> images = new HashMap<>();
//		images.put(10, fingerImage10);
//		images.put(9, fingerImage9);
//		images.put(8, fingerImage8);
//		images.put(7, fingerImage7);
//		images.put(6, fingerImage6);
//		images.put(5, fingerImage5);
//		images.put(4, fingerImage4);
//		images.put(3, fingerImage3);
//		images.put(2, fingerImage2);
//		images.put(1, fingerImage1);
//		//Map<Integer, byte[]> createNNLiteFingerTemplates(HashMap<Integer, byte[]> fingerImages, Common.ImageFormat fingersImageFormat)
//		Map<Integer, byte[]> templates = omniMatchUtil.createNNLiteFingerTemplates(images, Common.ImageFormat.PNG);
//		
////		HashMap<Integer, byte[]> templates = new HashMap<>();
////		templates.put(1, template);
//		
//		MatcherCommon.Record record = omniMatchUtil.createRecord(templates);
//		//MatcherCommon.Record recordCrypto = omniMatchUtil.createRecord(result.getFingerprints());
//		
//		//get templates from cryptograph decode result
//		Map<Integer, Data> fingerprintsDataMap = result.getFingerprints();
//		
//		Map<Integer, byte[]> fingerprintsByteArrayMap = new HashMap<>();
//		
//		for (Map.Entry<Integer, Data> entry : fingerprintsDataMap.entrySet()) {
//			fingerprintsByteArrayMap.put(entry.getKey(),entry.getValue().getContent());
//		}
//		
//		//create record using templates from crytograph
//		MatcherCommon.Record record2 = omniMatchUtil.createRecord(fingerprintsByteArrayMap);		
//		
//		MatcherCommon.RecordResult recordResult = omniMatchUtil.verifyRecord(record, record2);
//		
//		float score = recordResult.getCandidate().getScores().getFinger().getLogFAR();
//		// score >= 4 can be considered as match
//		
//		//Log.d("TAG", "finger score : " + score);
//		
//		Toast.makeText(getApplicationContext(), "score :" + score, Toast.LENGTH_LONG).show();
//		appelProcedureWL_String("printProc","score :" + score);
//				
//	} catch (OmniMatchException | OmniMatchUtilException |
//	IOException e) {
//		e.printStackTrace();
//	}
}
//
//public static byte[] readFileFromAsset(String fileName) {
//	String resultFilePath = "";
//	
//	resultFilePath = getApplicationContext().getExternalFilesDir(null) +
//	File.separator;
//	File resultFile = new File(resultFilePath);
//		
//	 String fingerprintFilePath = resultFilePath + fileName;
//	 
//	 //appelProcedureWL_String("printProc","image path :" + fingerprintFilePath);
//	 
//	 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//	 Bitmap bitmap = BitmapFactory.decodeFile(fingerprintFilePath);
//	 bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
//	 byte[] imageBytes = byteArrayOutputStream.toByteArray();
//	
//	
//	
//	return imageBytes;
//}



//public static void sampleCryptoDecode(byte[] image, String key)
public static void sampleCryptoDecode(String image, String key)
{

//	File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"crypto-tech-5.png");
//
//	String baseStr = Base64.encode(myFile, Base64.NO_WRAP);

	byte[] imageBytes =  Base64.decode(image,Base64.NO_WRAP);

	if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
		try {
			Files.write(new File(getApplicationContext().getExternalFilesDir(null)+File.separator+System.currentTimeMillis()+"crypto.png").toPath(), imageBytes);
		} catch (IOException e) {

		}
	}
	//String key = "0c5204df931ae3e7e75b9fd99d8098a7adf29fc1dfd931150133ea222fe0b0e616ecd232cb12a6f5391f6ee20062386d7e1e105d9d8cf4911d9f8fe949cbe9cf6301b4bcb208abbf204ee13377aa35091e9edbac97c41500b8c917e6bee4a6fa36659e6369fd6e658fb517e8518575f6c4703922332b9ba79cda52ede2b9e0be"

	CryptographDecodeUtil cryptographDecodeUtil = new CryptographDecodeUtil();

	Result result = null;

	try {
		result = cryptographDecodeUtil.decodeCryptogrpah(getApplicationContext(), key, imageBytes);
	} catch (TLVDecodeException e) {
		Toast.makeText(getApplicationContext(), "In exception.... ", Toast.LENGTH_LONG).show();
		e.printStackTrace();
	}
	//result code 13 = no image sent
	//Toast.makeText(getApplicationContext(), "code 1 : "+result.getCryptographDecodeResultCode(), Toast.LENGTH_LONG).show();
	//Toast.makeText(getApplicationContext(), "code 2 : "+result.getCryptographClientInitResultCode(), Toast.LENGTH_LONG).show();

	if(result != null && !result.getFingerprints().isEmpty()){
		JSONArray fingerDataArray = new JSONArray();
		//Parcourir le Hashmap avec la boucle For
		try {
			for (Map.Entry m : result.getFingerprints().entrySet()) {
			
				JSONObject fingr = new JSONObject();
				fingr.put("pos", ""+m.getKey());
				fingr.put("val", m.getValue());
				fingerDataArray.put(fingr);
				//System.out.println("ID: "+m.getKey()+", Nom: "+m.getValue());
				//appelProcedureWL_String("printProc", new String("Index: "+m.getKey()+", contenu : "+m.getValue()));
				//Toast.makeText(getApplicationContext(), "ID: "+m.getKey()+", Nom: "+m.getValue(), Toast.LENGTH_LONG).show();
			}
			appelProcedureWL_String("printProc", fingerDataArray.toString());
			//appelProcedureWL_String("printProc", new String(result.getFaceTemplate()));
		} catch (JSONException e)
		{
			//e.printStackTrace();
			Toast.makeText(getApplicationContext(),  e.getMessage(), Toast.LENGTH_LONG).show();
			//appelProcedureWL_String("getQueryResultAndEcho", "Error :" + e.getMessage());
			//Log.i(TAG, "Error :" + e.getMessage());
		}
		//FingerTemplateCreationAndMatching().createNISTT5FingerTemplate();
	}else{
		Toast.makeText(getApplicationContext(), "Pas de contenu dans FP", Toast.LENGTH_LONG).show();
	}

	if(result != null && result.getDemographics() != null){
		//Toast.makeText(getApplicationContext(), "in result different to null... ", Toast.LENGTH_LONG).show();
		//Toast.makeText(getApplicationContext(), new String(result.getDemographics().getContent()), Toast.LENGTH_LONG).show();
		appelProcedureWL_String("printProc", new String(result.getDemographics().getContent()));
		//result.getFingerprints() to get template from crypto
		//Map<Integer, Data> getFingerprints()
	}else{
		appelProcedureWL_String("printProc","Result is null");
	}
	//System.out.println("result :" + result);
}

// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// sampleCryptoDecode ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure sampleCryptoDecode()
//
//



public static void InitialisingOmnimatchSdk()
{
	Listener.initOmniMatch(getApplicationContext(), new OmniMatchInitCompletion() {
		@Override
		public void onInitCompletion(boolean isInitialized, @Nullable String s) {

			if (isInitialized) {
				Toast.makeText(getApplicationContext(), "Omnimatch succesfuly initialized", Toast.LENGTH_LONG).show();
				//NavHostFragment.findNavController(FirstFragment.this)
				//.navigate(R.id.action_FirstFragment_to_SecondFragment);
			} else {

				Toast.makeText(getApplicationContext(), "SDK init failed:" + s, Toast.LENGTH_LONG).show();
			}

		}
	});
}


// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// InitialisingOmnimatchSdk ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure InitialisingOmnimatchSdk()
//
//


//import androidx.navigation.fragment.NavHostFragment;


//import ai.tech5.finger.databinding.FragmentSecondBinding;

public static void CryptoMatch()
{		//getExternalFilesDir(null)
		//resultFilePath = Environment.getExternalStoragePublicDirectory(null);
		
		//String fingerprintFilePath = resultFilePath+File.separator+"2023072010581312_1689847155221_finger_10.png";
		//ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		//Bitmap bitmap = BitmapFactory.decodeFile(fingerprintFilePath);
		//bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
		//byte[] imageBytes = byteArrayOutputStream.toByteArray();
		//String imageString = Base64.encodeToString(imageBytes, Base64.DEFAULT);
		//imageString=imageString.replace("\n", "").replace("\r", "");
		//String filePos = fingerprintFilePath.substring(fingerprintFilePath.length() - 6);

		//String appPath = App.getApp().getApplicationContext().getFilesDir().getAbsolutePath();
		//File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"2023072010581312_1689847155221_finger_10.png");        
		//String baseStr = Base64.encode(myFile, Base64.NO_WRAP);
		
		//byte[] imageBytes =  Base64.decode(image,Base64.NO_WRAP);
	
		//byte[] fingerImage = Base64.decode(imageString,Base64.NO_WRAP);
		byte[] fingerImage = readFileFromAssets("2023072010581312_1689847155221_finger_10.png");
		
		OmniMatchUtil omniMatchUtil = new OmniMatchUtil();
		
//		try {
			
			//byte[] template = omniMatchUtil.createNNLiteFingerTemplate(fingerImage, 1, Common.ImageFormat.PNG);
			//WSQ PNG JPG
			/*byte[] rightThumbImage = readFileFromAssets("2000000000000001_LI.WSQ");
			byte[] rightIndexImage = readFileFromAssets("2000000000000001_LI.WSQ");
			
			HashMap<Integer, byte[]> images = new HashMap<>();
			images.put(1, rightThumbImage);
			images.put(2, rightIndexImage);*/
			//Map<Integer, byte[]> createNNLiteFingerTemplates(HashMap<Integer, byte[]> fingerImages, Common.ImageFormat fingersImageFormat)
//			
//			HashMap<Integer, byte[]> templates = new HashMap<>();
//			templates.put(1, template);
//			
//			MatcherCommon.Record record = omniMatchUtil.createRecord(templates);
//			
//			MatcherCommon.RecordResult recordResult = omniMatchUtil.verifyRecord(record, record);
//			
//			float score = recordResult.getCandidate().getScores().getFinger().getLogFAR();
//			// score >= 4 can be considered as match
//			
//			Log.d("TAG", "finger score : " + score);
//			
//			Toast.makeText(getApplicationContext(), "score :" + score, Toast.LENGTH_LONG).show();
//			
//			
//		} catch (OmniMatchException | OmniMatchUtilException |
//		IOException e) {
//			e.printStackTrace();
//		}
	
}

public static byte[] readFileFromAssets(String fileName) {
	
	byte[] buffer = null;
	
	try (InputStream stream = getApplicationContext().getAssets().open(fileName);) {
		
		int size = stream.available();
		buffer = new byte[size];
		int bytesRead = stream.read(buffer);
		Log.d("TAG", "bytes read: " + bytesRead);
		
		return buffer;
	} catch (IOException e) {
		
		Log.e("TAG", "unable to read file from assets");
	}
	
	return buffer;
}
// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// CryptoMatch ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure CryptoMatch()
//
//

// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure CryptographDecode()
//
//


//import androidx.navigation.fragment.NavHostFragment;



//import Tech5.OmniMatch.MatcherCommon;
//import ai.tech5.finger.databinding.FragmentSecondBinding;
//
//import ai.tech5.omnimatchutil.OmniMatchUtilException;




public static void CryptographDecodeNistT5(String image, String key)
{
	
	byte[] fingerImage10 = readFileFromAsset("2023072613357654_1690374969303_finger_10.png");
//	byte[] fingerImage9 = readFileFromAsset("2023072613357654_1690374969301_finger_9.png");
//	byte[] fingerImage8 = readFileFromAsset("2023072613357654_1690374969295_finger_8.png");
//	byte[] fingerImage7 = readFileFromAsset("2023072613357654_1690374969298_finger_7.png");
//	byte[] fingerImage6 = readFileFromAsset("2023072613357654_1690374988844_finger_6.png");
//	byte[] fingerImage5 = readFileFromAsset("2023072613357654_1690375011049_finger_5.png");
//	byte[] fingerImage4 = readFileFromAsset("2023072613357654_1690375011042_finger_4.png");
//	byte[] fingerImage3 = readFileFromAsset("2023072613357654_1690375011052_finger_3.png");
//	byte[] fingerImage2 = readFileFromAsset("2023072613357654_1690375011046_finger_2.png");
//	byte[] fingerImage1 = readFileFromAsset("2023072613357654_1690375032050_finger_1.png");
	
	OmniMatchUtil omniMatchUtil = new OmniMatchUtil();
	
	omniMatchUtil.matchFingerImages(fingerImage10, fingerImage10, Common.ImageFormat.PNG, new OmniMatchMatchCompletion() {
		@Override
		public void onMatchCompleted(float score, @Nullable String errorMessage) {
			
			if (errorMessage != null && errorMessage.isEmpty()) {
				//show error message
				Toast.makeText(getApplicationContext(),"error : " + errorMessage, Toast.LENGTH_LONG).show(); 
				//Log.d("TAG", "error : " + errorMessage);
			}
			
			
			if (score != -1) {
				Toast.makeText(getApplicationContext(), "score : " + score, Toast.LENGTH_LONG).show(); 
				Log.d("TAG", "score : " + score);
			}			
		}
	});
//	
////	Toast.makeText(getApplicationContext(),"Before Try...", Toast.LENGTH_LONG).show(); 
//	try {
//		byte[] template = omniMatchUtil.createNISTT5FingerTemplate(fingerImage10, Common.ImageFormat.PNG);
//		//byte[] template1 = omniMatchUtil.createNISTT5FingerTemplate(fingerImage10, Common.ImageFormat.PNG);
//		
////		appelProcedureWL_String("printProc", "authMatcherInstance : "+Listener.authMatcherInstance);
////		appelProcedureWL_String("printProc", "authMatcherNative : "+Listener.authMatcherNative);
////		Toast.makeText(getApplicationContext(),"Before Template "+ template.length+" template 1"+ template.length, Toast.LENGTH_LONG).show(); 
//		float score = omniMatchUtil.matchFingerMinexNistT5Templates(template, template);
////		Toast.makeText(getApplicationContext(),"After matching function", Toast.LENGTH_LONG).show(); 
////		
////		Toast.makeText(getApplicationContext(), "score :" + score, Toast.LENGTH_LONG).show();
////		appelProcedureWL_String("printProc", "is matched : " + (score >= 4.0f));
//				//OmniMatchException | InvalidProtocolBufferException e | IO
//	} catch (Exception e) {		
////		Toast.makeText(getApplicationContext(),  "error in matching " + e.getLocalizedMessage(), Toast.LENGTH_LONG).show();
//	}
}

public static byte[] readFileFromAsset(String fileName) {
	String resultFilePath = "";
	
	resultFilePath = getApplicationContext().getExternalFilesDir(null) +
	File.separator;
	File resultFile = new File(resultFilePath);
		
	 String fingerprintFilePath = resultFilePath + fileName;
	 
	 //appelProcedureWL_String("printProc","image path :" + fingerprintFilePath);
	 
	 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
	 Bitmap bitmap = BitmapFactory.decodeFile(fingerprintFilePath);
	 bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream);
	 byte[] imageBytes = byteArrayOutputStream.toByteArray();
	
	return imageBytes;
}






//import ai.tech5.pheonix.capture.controller.ImageType;
//import ai.tech5.pheonix.capture.controller.TypeOfImage;
//import ai.tech5.pheonix.capture.controller.FullFrontalCropConfig;


// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// StartFaceCapture ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure StartFaceCapture()
//
//
public static void StartFaceSelfieCapture(String sqrRef)
{
	FaceCaptureController controller = FaceCaptureController.getInstance();
	controller.setUseBackCamera(false);
	controller.setAutoCapture(true);

	controller.setEyeClosedEnabled(true);
	controller.setOcclusionEnabled(true);
	controller.setCompression(false);
	/*comment set
	liveness
	from sharedprefs*/
	//controller.setCaptureTimeoutInSecs(60);
	controller.setCaptureTimeoutInSecs(120);
	controller.setIsGetFullFrontalCrop(false);
	controller.setIsISOEnabled(false);
	controller.startFaceCapture( getCurrentActivity(), new FaceCaptureListener() {

		@Override
		public void onFaceCaptured(byte[] faceImage, byte[] originalImage, FaceBox faceBox) {
			Toast.makeText(getApplicationContext(), "Face captured, liveness:" + (faceBox.mLiveness), Toast.LENGTH_LONG).show();

			try {
				File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+sqrRef+"_"+System.currentTimeMillis()+"_finger_20.png");
				//File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"face_"+System.currentTimeMillis()+"_"+sqrRef+".png");
				//File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"face_"+System.currentTimeMillis()+".jpg");

				if (myFile.exists()) {
					myFile.delete();
				}

				if (myFile.createNewFile()) {
                     FileOutputStream fOut = new FileOutputStream(myFile);
                     fOut.write(faceImage);
                     fOut.close();
                }
			} catch (Exception e) {
				e.printStackTrace();
			}
		}


		@Override
		public void OnFaceCaptureFailed(String s) {
			//enableOrDisableCaptureBtns(true);
			Toast.makeText(getApplicationContext(), s, Toast.LENGTH_LONG).show();
		}

		@Override
		public void onCancelled() {
			//enableOrDisableCaptureBtns(true);
			Toast.makeText(getApplicationContext(), "User cancelled", Toast.LENGTH_LONG).show();
		}

		@Override
		public void onTimedout(byte[] bytes) {
			//Toast.makeText(getApplicationContext(), "On Face captured timeout... ", Toast.LENGTH_LONG).show();
			Toast.makeText(getApplicationContext(), "Capture timed out", Toast.LENGTH_LONG).show();

		}
	});

}






//import ai.tech5.pheonix.capture.controller.ImageType;
//import ai.tech5.pheonix.capture.controller.TypeOfImage;
//import ai.tech5.pheonix.capture.controller.FullFrontalCropConfig;


// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// StartFaceCapture ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure StartFaceCapture()
//
//
public static void StartFaceBackCapture(String sqrRef)
{
	FaceCaptureController controller = FaceCaptureController.getInstance();
	controller.setUseBackCamera(true);
	controller.setAutoCapture(false);

	controller.setEyeClosedEnabled(true);
	controller.setOcclusionEnabled(true);
	controller.setCompression(false);
	/*comment set
	liveness
	from sharedprefs*/
	//controller.setCaptureTimeoutInSecs(60);
	controller.setCaptureTimeoutInSecs(120);
	controller.setIsGetFullFrontalCrop(false);
	controller.setIsISOEnabled(false);
	controller.startFaceCapture( getCurrentActivity(), new FaceCaptureListener() {

		@Override
		public void onFaceCaptured(byte[] faceImage, byte[] originalImage, FaceBox faceBox) {
			Toast.makeText(getApplicationContext(), "Face captured, liveness:" + (faceBox.mLiveness), Toast.LENGTH_LONG).show();

			try {
				File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+sqrRef+"_"+System.currentTimeMillis()+"_finger_20.png");
				//File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"face_"+System.currentTimeMillis()+"_"+sqrRef+".png");
				//File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"face_"+System.currentTimeMillis()+".jpg");

				if (myFile.exists()) {
					myFile.delete();
				}

				if (myFile.createNewFile()) {
                     FileOutputStream fOut = new FileOutputStream(myFile);
                     fOut.write(faceImage);
                     fOut.close();
                }
			} catch (Exception e) {
				e.printStackTrace();
			}
		}


		@Override
		public void OnFaceCaptureFailed(String s) {
			//enableOrDisableCaptureBtns(true);
			Toast.makeText(getApplicationContext(), s, Toast.LENGTH_LONG).show();
		}

		@Override
		public void onCancelled() {
			//enableOrDisableCaptureBtns(true);
			Toast.makeText(getApplicationContext(), "User cancelled", Toast.LENGTH_LONG).show();
		}

		@Override
		public void onTimedout(byte[] bytes) {
			//Toast.makeText(getApplicationContext(), "On Face captured timeout... ", Toast.LENGTH_LONG).show();
			Toast.makeText(getApplicationContext(), "Capture timed out", Toast.LENGTH_LONG).show();

		}
	});

}




// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// StartFingerCapture ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure StartFingerCapture()

  public static void StartFingerCaptureLeftHand(String Sqr) {
        //private void startFingerCapture() {

        //Toast.makeText(getApplicationContext(), "Lancement de la procédure.... ", Toast.LENGTH_LONG).show();

       // String applicationId = getApplicationContext().getPackageName();
        //Toast.makeText(getApplicationContext(), applicationId, Toast.LENGTH_LONG).show();
        //appelProcedureWL_String("printProc",applicationId);

        try {

            T5FingerCaptureController t5FingerCaptureController = T5FingerCaptureController.getInstance();
            t5FingerCaptureController.setLicense("");

            t5FingerCaptureController.showElipses(true);
            t5FingerCaptureController.setLivenessCheck(true);

            //Toast.makeText(getApplicationContext(), "After set licence... ", Toast.LENGTH_LONG).show();

            t5FingerCaptureController.setIsGetQuality(true);
            //t5FingerCaptureController.setCreateTemplates(true);

            t5FingerCaptureController.setDetectorThreshold(0.9f);
            // SEGMENTATION_MODE_LEFT_SLAP,
            // SEGMENTATION_MODE_RIGHT_SLAP,
            // SEGMENTATION_MODE_LEFT_THUMB,
            // SEGMENTATION_MODE_RIGHT_THUMB,
            // SEGMENTATION_MODE_LEFT_AND_RIGHT_THUMBS,
            // SEGMENTATION_MODE_LEFT_INDEX,
            // SEGMENTATION_MODE_RIGHT_INDEX

            LinkedHashSet<SegmentationMode> segmentationModes = new LinkedHashSet<>();
            segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_SLAP);
            segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_SLAP);

            t5FingerCaptureController.setSegmentationModes(segmentationModes);

            t5FingerCaptureController.setCaptureMode(CaptureMode.CAPTURE_MODE_SELF);
            t5FingerCaptureController.setCaptureSpeed(CaptureSpeed.CAPTURE_SPEED_NORMAL);
            t5FingerCaptureController.setPropDenoise(true);
            t5FingerCaptureController.setTitle("KYVALA UNIVERS");

            ImageConfiguration segmentedFingersConfiguration = new ImageConfiguration();
           segmentedFingersConfiguration.setPrimaryImageType(ImageType.IMAGE_TYPE_PNG);
            segmentedFingersConfiguration.setRequireDisplayImage(false);
            segmentedFingersConfiguration.setDisplayImageType(ImageType.IMAGE_TYPE_PNG);

            //compresion ratio is only applicable for IMAGE_TYPE_WSQ
            segmentedFingersConfiguration.setCompressionRatio(10);
            segmentedFingersConfiguration.setIsCropImage(true);
            segmentedFingersConfiguration.setCroppedImageWidth(512);
            segmentedFingersConfiguration.setCroppedImageHeight(512);
            //0->Black color padding; 255->white color padding
            segmentedFingersConfiguration.setPaddingColor(255);

            t5FingerCaptureController.setSegmentedFingerImagesConfig(segmentedFingersConfiguration);

            ImageConfiguration slapConfig = new ImageConfiguration();
            slapConfig.setPrimaryImageType(ImageType.IMAGE_TYPE_BMP);
            slapConfig.setCompressionRatio(10);
            //	Toast.makeText(getApplicationContext(), "After image configuration... ", Toast.LENGTH_LONG).show();

            slapConfig.setIsCropImage(false);
            //            slapConfig.setCroppedImageWidth(1600);
            //            slapConfig.setCroppedImageHeight(1500);
            //            //0->Black color padding; 255->white color padding
            //            slapConfig.setPaddingColor(0);
            slapConfig.setRequireDisplayImage(false);

            t5FingerCaptureController.setSlapImagesConfig(slapConfig);

            //t5FingerCaptureController.setTimeoutInSecs(20);
            t5FingerCaptureController.setTimeoutInSecs(120);
            //	Toast.makeText(getApplicationContext(), "After set time out... ", Toast.LENGTH_LONG).show();

            t5FingerCaptureController.captureFingers(getCurrentActivity(), new T5FingerCapturedListener() {
                @Override
                public void onSuccess(FingerCaptureResult result) {
                    Toast.makeText(getApplicationContext(), "On Success Bloc... ", Toast.LENGTH_LONG).show();
                    if (result.fingers != null && !result.fingers.isEmpty()) {
                        HashMap<Integer, String> fingers = new HashMap<>();

                        for (Finger finger : result.fingers) {
                            try {
                                //File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"finger_"+System.currentTimeMillis()+"_"+Sqr+"_"+finger.pos+".png");
                                File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath() + File.separator + Sqr + "_" + System.currentTimeMillis() + "_finger_" + finger.pos + ".png");

                                if (myFile.exists()) {
                                    myFile.delete();
                                }

                                myFile.createNewFile();

                                FileOutputStream fOut = new FileOutputStream(myFile);

                                fOut.write(finger.primaryImage);
                                fOut.close();

                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            //fingers.put(finger.pos, Base64.encodeToString(finger.fingerImage, Base64.NO_WRAP));
                        }

                        String fingersJson = new Gson().toJson(fingers);
                        //appelProcedureWL_String("printJsonRet",fingersJson);
                    }
                }

                @Override
                public void onFailure(String s) {
                    Toast.makeText(getApplicationContext(), "On Failure Bloc... ", Toast.LENGTH_LONG).show();
                    //appelProcedureWL_String("printProc","On Failure Bloc... "+s);

                }

                @Override
                public void onCancelled() {
                    Toast.makeText(getApplicationContext(), "On Cancelled Bloc... ", Toast.LENGTH_LONG).show();

                }

                @Override
                public void onTimedout() {
                    Toast.makeText(getApplicationContext(), "On TimeOut Bloc... ", Toast.LENGTH_LONG).show();

                }
            });

            //Toast.makeText(getApplicationContext(), "after try... ", Toast.LENGTH_LONG).show();

        } catch (Exception e) {
            //appelProcedureWL_String("printProc","On catch exception :  Bloc... "+e);
            Toast.makeText(getApplicationContext(), "in exception bloc... ", Toast.LENGTH_LONG).show();

        }

        //Toast.makeText(getApplicationContext(), "after try... ", Toast.LENGTH_LONG).show();


    }



public static void StartSingleFingerCapture(String Sqr, String FingerToCapture)
{
	 //private void startFingerCapture() {

        //Toast.makeText(getApplicationContext(), "Lancement de la procédure.... ", Toast.LENGTH_LONG).show();

       // String applicationId = getApplicationContext().getPackageName();
        //Toast.makeText(getApplicationContext(), applicationId, Toast.LENGTH_LONG).show();
        //appelProcedureWL_String("printProc",applicationId);

        try {

            T5FingerCaptureController t5FingerCaptureController = T5FingerCaptureController.getInstance();
            t5FingerCaptureController.setLicense("");

            t5FingerCaptureController.showElipses(true);
            t5FingerCaptureController.setLivenessCheck(true);

            //Toast.makeText(getApplicationContext(), "After set licence... ", Toast.LENGTH_LONG).show();

            t5FingerCaptureController.setIsGetQuality(true);
            //t5FingerCaptureController.setCreateTemplates(true);

            t5FingerCaptureController.setDetectorThreshold(0.9f);
            // SEGMENTATION_MODE_LEFT_SLAP,
            // SEGMENTATION_MODE_RIGHT_SLAP,
            // SEGMENTATION_MODE_LEFT_THUMB,
            // SEGMENTATION_MODE_RIGHT_THUMB,
            // SEGMENTATION_MODE_LEFT_AND_RIGHT_THUMBS,
            // SEGMENTATION_MODE_LEFT_INDEX,
            // SEGMENTATION_MODE_RIGHT_INDEX

            LinkedHashSet<SegmentationMode> segmentationModes = new LinkedHashSet<>();
            //segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_SLAP);
            //segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_SLAP);

			switch(FingerToCapture) {
			  case "RT":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_THUMB);
			    break;
			  case "RI":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_INDEX);
			    break;
			  case "RM":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_MIDDLE);
			    break;
			  case "RR":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_RING);
			    break;
			  case "RL":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_LITTLE);
			    break;
			  case "LT":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_THUMB);
			    break;
			  case "LI":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_INDEX);
			    break;
			  case "LM":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_MIDDLE);
			    break;
			  case "LR":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_RING);
			    break;
			  case "LL":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_LITTLE);
			    break;
			}

            t5FingerCaptureController.setSegmentationModes(segmentationModes);

            t5FingerCaptureController.setCaptureMode(CaptureMode.CAPTURE_MODE_SELF);
            t5FingerCaptureController.setCaptureSpeed(CaptureSpeed.CAPTURE_SPEED_NORMAL);
            t5FingerCaptureController.setPropDenoise(true);
            t5FingerCaptureController.setTitle("KYVALA UNIVERS");

            ImageConfiguration segmentedFingersConfiguration = new ImageConfiguration();
           segmentedFingersConfiguration.setPrimaryImageType(ImageType.IMAGE_TYPE_PNG);
            segmentedFingersConfiguration.setRequireDisplayImage(false);
            segmentedFingersConfiguration.setDisplayImageType(ImageType.IMAGE_TYPE_PNG);

            //compresion ratio is only applicable for IMAGE_TYPE_WSQ
            segmentedFingersConfiguration.setCompressionRatio(10);
            segmentedFingersConfiguration.setIsCropImage(true);
            segmentedFingersConfiguration.setCroppedImageWidth(512);
            segmentedFingersConfiguration.setCroppedImageHeight(512);
            //0->Black color padding; 255->white color padding
            segmentedFingersConfiguration.setPaddingColor(255);

            t5FingerCaptureController.setSegmentedFingerImagesConfig(segmentedFingersConfiguration);

            ImageConfiguration slapConfig = new ImageConfiguration();
            slapConfig.setPrimaryImageType(ImageType.IMAGE_TYPE_BMP);
            slapConfig.setCompressionRatio(10);
            //	Toast.makeText(getApplicationContext(), "After image configuration... ", Toast.LENGTH_LONG).show();

            slapConfig.setIsCropImage(false);
            //            slapConfig.setCroppedImageWidth(1600);
            //            slapConfig.setCroppedImageHeight(1500);
            //            //0->Black color padding; 255->white color padding
            //            slapConfig.setPaddingColor(0);
            slapConfig.setRequireDisplayImage(false);

            t5FingerCaptureController.setSlapImagesConfig(slapConfig);

            //t5FingerCaptureController.setTimeoutInSecs(20);
            t5FingerCaptureController.setTimeoutInSecs(120);
            //	Toast.makeText(getApplicationContext(), "After set time out... ", Toast.LENGTH_LONG).show();

            t5FingerCaptureController.captureFingers(getCurrentActivity(), new T5FingerCapturedListener() {
                @Override
                public void onSuccess(FingerCaptureResult result) {
                    if (result.fingers != null && !result.fingers.isEmpty()) {
                        HashMap<Integer, String> fingers = new HashMap<>();

                        for (Finger finger : result.fingers) {
                            try {
                                //File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"finger_"+System.currentTimeMillis()+"_"+Sqr+"_"+finger.pos+".png");
                                //File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath() + File.separator + Sqr + "_" + System.currentTimeMillis() + "_finger_" + finger.pos + ".png");
								File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath() + File.separator + Sqr + "_" + System.currentTimeMillis() + "_finger_" + finger.pos + ".wsq");

                                if (myFile.exists()) {
                                    myFile.delete();
                                }

                                myFile.createNewFile();

                                FileOutputStream fOut = new FileOutputStream(myFile);

                                fOut.write(finger.primaryImage);
                                fOut.close();

                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            fingers.put(finger.pos, Base64.encodeToString(finger.primaryImage, Base64.NO_WRAP));
                        }

						//String cryptographResult = result.toJsonString();
						//String fingersJson = new Gson().toJson(fingers);
                        String fingersJson = convertHashMapToJson(fingers);
                        appelProcedureWL_String("printFingerCaptureJson", fingersJson);
                    	Toast.makeText(getApplicationContext(), fingersJson, Toast.LENGTH_LONG).show();
                    	Toast.makeText(getApplicationContext(), "On Success Bloc... ", Toast.LENGTH_LONG).show();
                    }
                }

                @Override
                public void onFailure(String s) {
                    Toast.makeText(getApplicationContext(), "On Failure Bloc... ", Toast.LENGTH_LONG).show();
                    //appelProcedureWL_String("printProc","On Failure Bloc... "+s);
                }

                @Override
                public void onCancelled() {
                    Toast.makeText(getApplicationContext(), "On Cancelled Bloc... ", Toast.LENGTH_LONG).show();
                }

                @Override
                public void onTimedout() {
                    Toast.makeText(getApplicationContext(), "On TimeOut Bloc... ", Toast.LENGTH_LONG).show();

                }
            });

            //Toast.makeText(getApplicationContext(), "after try... ", Toast.LENGTH_LONG).show();

        } catch (Exception e) {
            //appelProcedureWL_String("printProc","On catch exception :  Bloc... "+e);
            Toast.makeText(getApplicationContext(), "in exception bloc... ", Toast.LENGTH_LONG).show();
        }
}


	public static String convertHashMapToJson(HashMap<Integer, String> hashMap) {
        if (hashMap == null) {
            return new JSONObject().toString(); // Return an empty JSON object if input is null
        }

        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<Integer, String> entry : hashMap.entrySet()) {
            // org.json.JSONObject automatically handles different data types.
            // For Integer keys, it will convert them to String keys in the JSON.
            try {
                jsonObject.put(String.valueOf(entry.getKey()), entry.getValue());
            } catch (JSONException e) {

            }
        }
        return jsonObject.toString();
    }
    
// Summary: <specify the procedure action>
// Syntax:
// StartSingleFingerCapture ()
//
// Parameters:
//	None
// Return value:
// 	None
//
// Example:
// <Specify a usage example>
//
//procedure StartSingleFingerCapture()
//



public static void DecodeCryptoNew(String image, String key)
{
		HashMap<String, String> returnTreatment = new HashMap<>();

		//String[] myArrayRet =
        OmniMatchUtil omniMatchUtil = new OmniMatchUtil();

        byte[] imageBytes = Base64.decode(image, Base64.NO_WRAP);

        CryptographDecodeUtil cryptographDecodeUtil = new CryptographDecodeUtil();

        Result result = null;

        try {
            result = cryptographDecodeUtil.decodeCryptogrpah(getApplicationContext(), key, imageBytes);

            if (result != null && result.getCompressedImage() != null) {
                //binding.btnDecompressFaceImage.setEnabled(true);
            }

        } catch (TLVDecodeException ignored) {

        }

        if (result == null || result.getCryptographDecodeResultCode() != 0) {
            //cryptograph decode failed

            //send back error message as cryptograph failed
			//put crypto decode code in return variable
			//String crypTodeco =  result.getCryptographDecodeResultCode();
			returnTreatment.put("cryptoDecodeResultcode", Integer.toString(result.getCryptographDecodeResultCode()));
			appelProcedureWL_String("printJsonRet",returnTreatment);
            return;
        }


        //   1. decompress face image

        byte[] faceImage = null;
        if (result.getCompressedImage() != null && result.getCompressedImage().getContent() != null && result.getCompressedImage().getContent().length > 0) {

            try {
                faceImage = omniMatchUtil.decompressProprietaryFaceImage(result.getCompressedImage().getContent());
            } catch (IOException | OmniMatchException ignore) {

            }
        }

        if (faceImage != null) {
            //face image is successfully decompressed. now you can use this face image to display

            //or send it back as a base64 encoded string

			// add selfie to return variable
			//String baseFace =  Base64.encode(faceImage, Base64.NO_WRAP);
			//String baseFace =  Base64.getEncoder().encodeToString(faceImage);
			//returnTreatment.put("FaceImage", baseFace);
        }


        //  2. get demographics
        if (result.getDemographics() != null && result.getDemographics().getContent() != null) {

            String demographics = new String(result.getDemographics().getContent());

            //now you can send these demographics back

			//put demographics in return variable
			returnTreatment.put("Demographics", demographics);

        }


        //3. get finger templates

        HashMap<Integer, byte[]> fingerTemplates = new HashMap<>();

        if (result.getFingerprints() != null) {

            result.getFingerprints().forEach((integer, data) -> {

                if (data.getContent() != null && data.getContent().length > 0)
                {
                    fingerTemplates.put(integer, data.getContent());
                }
            });

        }

        //now you can send these finger templates back or use them to match with finger images

        HashMap<Integer, byte[]> fingerimages = new HashMap<>();
        //above is an empty fingers map, you need to add the finger images.

        // you can match finger images and templates using the below function

        omniMatchUtil.matchFingerImagesAndNistT5Templates(fingerimages, fingerTemplates, Common.ImageFormat.WSQ, 4.0f, (score, errorMessage) -> {

            if (errorMessage != null) {
                Log.d("TAG", "finger match failed: " + errorMessage);
            } else {
                Log.d("TAG", "finger match score: " + score);
            }
        });

		appelProcedureWL_String("printJsonRet",returnTreatment);
    }


// Summary: <specify the procedure action>
// Syntax:
// DecodeCryptoNew ()
//
// Parameters:
//	None
// Return value:
// 	None
//
// Example:
// <Specify a usage example>
//
//procedure DecodeCryptoNew()
//





//import ai.tech5.pheonix.capture.controller.ImageType;
//import ai.tech5.pheonix.capture.controller.TypeOfImage;
//import ai.tech5.pheonix.capture.controller.FullFrontalCropConfig;


// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// StartFaceCapture ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure StartFaceCapture()
//
//
public static void StartFaceCapture(String sqrRef, String whichCamera)
{
	FaceCaptureController controller = FaceCaptureController.getInstance();
	switch(whichCamera)
	{
	  case "CAMERA-FRONT":
		controller.setUseBackCamera(false);
	    break;
	  case "CAMERA-BACK":
		controller.setUseBackCamera(true);
	    break;
	}
	
	//controller.setUseBackCamera(false);
	controller.setAutoCapture(true);

	controller.setEyeClosedEnabled(true);
	controller.setOcclusionEnabled(true);
	controller.setCompression(false);
	/*comment set
	liveness
	from sharedprefs*/
	//controller.setCaptureTimeoutInSecs(60);
	controller.setCaptureTimeoutInSecs(120);
	controller.setIsGetFullFrontalCrop(false);
	controller.setIsISOEnabled(false);
	controller.startFaceCapture( getCurrentActivity(), new FaceCaptureListener() {

		@Override
		public void onFaceCaptured(byte[] faceImage, byte[] originalImage, FaceBox faceBox) {
			Toast.makeText(getApplicationContext(), "Face captured, liveness:" + (faceBox.mLiveness), Toast.LENGTH_LONG).show();

			try {
				File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+sqrRef+"_"+System.currentTimeMillis()+"_finger_20.png");
				//File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"face_"+System.currentTimeMillis()+"_"+sqrRef+".png");
				//File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"face_"+System.currentTimeMillis()+".jpg");

				if (myFile.exists()) {
					myFile.delete();
				}

				if (myFile.createNewFile()) {
                     FileOutputStream fOut = new FileOutputStream(myFile);
                     fOut.write(faceImage);
                     fOut.close();
                }
			} catch (Exception e) {
				e.printStackTrace();
			}
		}


		@Override
		public void OnFaceCaptureFailed(String s) {
			//enableOrDisableCaptureBtns(true);
			Toast.makeText(getApplicationContext(), s, Toast.LENGTH_LONG).show();
		}

		@Override
		public void onCancelled() {
			//enableOrDisableCaptureBtns(true);
			Toast.makeText(getApplicationContext(), "User cancelled", Toast.LENGTH_LONG).show();
		}

		@Override
		public void onTimedout(byte[] bytes) {
			//Toast.makeText(getApplicationContext(), "On Face captured timeout... ", Toast.LENGTH_LONG).show();
			Toast.makeText(getApplicationContext(), "Capture timed out", Toast.LENGTH_LONG).show();

		}
	});

}




public static void StartFingerCapture(String Sqr, String FingerToCapture)
{
	 //private void startFingerCapture() {

        //Toast.makeText(getApplicationContext(), "Lancement de la procédure.... ", Toast.LENGTH_LONG).show();

       // String applicationId = getApplicationContext().getPackageName();
        //Toast.makeText(getApplicationContext(), applicationId, Toast.LENGTH_LONG).show();
        //appelProcedureWL_String("printProc",applicationId);

        try {

            T5FingerCaptureController t5FingerCaptureController = T5FingerCaptureController.getInstance();
            t5FingerCaptureController.setLicense("");

            t5FingerCaptureController.showElipses(true);
            t5FingerCaptureController.setLivenessCheck(true);

            //Toast.makeText(getApplicationContext(), "After set licence... ", Toast.LENGTH_LONG).show();

            t5FingerCaptureController.setIsGetQuality(true);
            //t5FingerCaptureController.setCreateTemplates(true);

            t5FingerCaptureController.setDetectorThreshold(0.9f);
            // SEGMENTATION_MODE_LEFT_SLAP,
            // SEGMENTATION_MODE_RIGHT_SLAP,
            // SEGMENTATION_MODE_LEFT_THUMB,
            // SEGMENTATION_MODE_RIGHT_THUMB,
            // SEGMENTATION_MODE_LEFT_AND_RIGHT_THUMBS,
            // SEGMENTATION_MODE_LEFT_INDEX,
            // SEGMENTATION_MODE_RIGHT_INDEX

            LinkedHashSet<SegmentationMode> segmentationModes = new LinkedHashSet<>();
            //segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_SLAP);
            //segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_SLAP);

			switch(FingerToCapture) {
			  case "RT":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_THUMB);
			    break;
			  case "RI":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_INDEX);
			    break;
			  case "RM":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_MIDDLE);
			    break;
			  case "RR":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_RING);
			    break;
			  case "RL":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_LITTLE);
			    break;
			  case "LT":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_THUMB);
			    break;
			  case "LI":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_INDEX);
			    break;
			  case "LM":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_MIDDLE);
			    break;
			  case "LR":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_RING);
			    break;
			  case "LL":
			  		segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_LITTLE);
			    break;
			}

            t5FingerCaptureController.setSegmentationModes(segmentationModes);

            t5FingerCaptureController.setCaptureMode(CaptureMode.CAPTURE_MODE_SELF);
            t5FingerCaptureController.setCaptureSpeed(CaptureSpeed.CAPTURE_SPEED_NORMAL);
            t5FingerCaptureController.setPropDenoise(true);
            t5FingerCaptureController.setTitle("KYVALA UNIVERS");

            ImageConfiguration segmentedFingersConfiguration = new ImageConfiguration();
           segmentedFingersConfiguration.setPrimaryImageType(ImageType.IMAGE_TYPE_PNG);
            segmentedFingersConfiguration.setRequireDisplayImage(false);
            segmentedFingersConfiguration.setDisplayImageType(ImageType.IMAGE_TYPE_PNG);

            //compresion ratio is only applicable for IMAGE_TYPE_WSQ
            segmentedFingersConfiguration.setCompressionRatio(10);
            segmentedFingersConfiguration.setIsCropImage(true);
            segmentedFingersConfiguration.setCroppedImageWidth(512);
            segmentedFingersConfiguration.setCroppedImageHeight(512);
            //0->Black color padding; 255->white color padding
            segmentedFingersConfiguration.setPaddingColor(255);

            t5FingerCaptureController.setSegmentedFingerImagesConfig(segmentedFingersConfiguration);

            ImageConfiguration slapConfig = new ImageConfiguration();
            slapConfig.setPrimaryImageType(ImageType.IMAGE_TYPE_BMP);
            slapConfig.setCompressionRatio(10);
            //	Toast.makeText(getApplicationContext(), "After image configuration... ", Toast.LENGTH_LONG).show();

            slapConfig.setIsCropImage(false);
            //            slapConfig.setCroppedImageWidth(1600);
            //            slapConfig.setCroppedImageHeight(1500);
            //            //0->Black color padding; 255->white color padding
            //            slapConfig.setPaddingColor(0);
            slapConfig.setRequireDisplayImage(false);

            t5FingerCaptureController.setSlapImagesConfig(slapConfig);

            //t5FingerCaptureController.setTimeoutInSecs(20);
            t5FingerCaptureController.setTimeoutInSecs(120);
            //	Toast.makeText(getApplicationContext(), "After set time out... ", Toast.LENGTH_LONG).show();

            t5FingerCaptureController.captureFingers(getCurrentActivity(), new T5FingerCapturedListener() {
                @Override
                public void onSuccess(FingerCaptureResult result) {
                    Toast.makeText(getApplicationContext(), "On Success Bloc... ", Toast.LENGTH_LONG).show();
                    if (result.fingers != null && !result.fingers.isEmpty()) {
                        HashMap<Integer, String> fingers = new HashMap<>();

                        for (Finger finger : result.fingers) {
                            try {
                                //File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath()+File.separator+"finger_"+System.currentTimeMillis()+"_"+Sqr+"_"+finger.pos+".png");
                                //File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath() + File.separator + Sqr + "_" + System.currentTimeMillis() + "_finger_" + finger.pos + ".png");
								File myFile = new File(getApplicationContext().getExternalFilesDir(null).getAbsolutePath() + File.separator + Sqr + "_" + System.currentTimeMillis() + "_finger_" + finger.pos + ".wsq");

                                if (myFile.exists()) {
                                    myFile.delete();
                                }

                                myFile.createNewFile();

                                FileOutputStream fOut = new FileOutputStream(myFile);

                                fOut.write(finger.primaryImage);
                                fOut.close();

                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            //fingers.put(finger.pos, Base64.encodeToString(finger.fingerImage, Base64.NO_WRAP));
                        }

                        String fingersJson = new Gson().toJson(fingers);
                        //appelProcedureWL_String("printJsonRet",fingersJson);
                    }
                }

                @Override
                public void onFailure(String s) {
                    Toast.makeText(getApplicationContext(), "On Failure Bloc... ", Toast.LENGTH_LONG).show();
                    //appelProcedureWL_String("printProc","On Failure Bloc... "+s);
                }

                @Override
                public void onCancelled() {
                    Toast.makeText(getApplicationContext(), "On Cancelled Bloc... ", Toast.LENGTH_LONG).show();
                }

                @Override
                public void onTimedout() {
                    Toast.makeText(getApplicationContext(), "On TimeOut Bloc... ", Toast.LENGTH_LONG).show();

                }
            });

            //Toast.makeText(getApplicationContext(), "after try... ", Toast.LENGTH_LONG).show();

        } catch (Exception e) {
            //appelProcedureWL_String("printProc","On catch exception :  Bloc... "+e);
            Toast.makeText(getApplicationContext(), "in exception bloc... ", Toast.LENGTH_LONG).show();
        }

}
// Summary: <specify the procedure action>
// Syntax:
// StartSingleFingerCapture ()
//
// Parameters:
//	None
// Return value:
// 	None
//
// Example:
// <Specify a usage example>
//
//procedure StartSingleFingerCapture()
//


public static void ScanCryptographe()
{
	CryptographReader.getInstance().scanCryptograph(getCurrentActivity(), new ScannerConfig(), (action, result, errorMessage) -> {

            if (action == CryptographReaderAction.COMPLETE) {

                try {

                    if (result == null) {
                        Toast.makeText(getApplicationContext(), "Cryptograph scanning failed", Toast.LENGTH_SHORT).show();
                        return; 
                    }

                    String cryptographResult = result.toJsonString();
                    //send this cryptographResult json string back
					Toast.makeText(getApplicationContext(), "Cryptograph decode success", Toast.LENGTH_SHORT).show();
					appelProcedureWL_String("printJsonRet",cryptographResult);

                } catch (Exception e) {

                    // error in converting result to json. send this back
                }

            } else if (action == CryptographReaderAction.CANCEL) {

                // cancelled scanning. send this back

					 Toast.makeText(getApplicationContext(), "Cryptograph decode cancel", Toast.LENGTH_SHORT).show();

            } else if (action == CryptographReaderAction.ERROR) {
                Toast.makeText(getApplicationContext(), "Cryptograph scanning failed: " + errorMessage, Toast.LENGTH_SHORT).show();
                // cryptograph scanning error. send this back
            }

        });
}
// Summary: <specify the procedure action>
// Syntax:
// DecrypteLight ()
//
// Parameters:
//	None
// Return value:
// 	None
//
// Example:
// <Specify a usage example>
//
//procedure DecrypteLight()
//



public static void initializeReader(String key)
{
		CryptographReader.getInstance().initializeReader(getApplicationContext(), key, (isInitialized, errorMessage) -> {

          if (isInitialized) {
             Toast.makeText(getApplicationContext(), "Cryptograph Reader succesfully initialized", Toast.LENGTH_SHORT).show();
          } else {
			Toast.makeText(getApplicationContext(), "Cryptograph Reader initializATION  failed:", Toast.LENGTH_LONG).show();
		}
      });
}


// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// InitialisingOmnimatchSdk ()
//
// Paramètres :
//	Aucun
// Valeur de retour :
// 	Aucune
//
// Exemple :
// <Indiquez ici un exemple d'utilisation>
//
//procédure InitialisingOmnimatchSdk()
//
//

// Résumé : <indiquez ici ce que fait la procédure>
// Syntax:
// [ <Result> = ] printFaceImage (<sRetourRequette> is string)
// 
// Parameters:
// sRetourRequette (Unicode string): <indiquez ici le rôle de el>
// Exemple :
// <Indiquez ici un exemple d'utilisation>
// 
// Return value:
// Unicode string: // 	Aucune
// procédure printFaceImage(sRetourRequette est une chaine)

static public WDObjet fWD_printFaceImage( WDObjet vWD_sRetourRequette )
{
// procédure printFaceImage(sRetourRequette est une chaine)
ms_instance.initExecProcGlobale("printFaceImage");



try
{
vWD_sRetourRequette = WDParametre.traiterParametre(vWD_sRetourRequette, 1, 16, 0x0);



try
{
// 	decompressedFaceString = sRetourRequette
GWDPintegrationSdk.getInstance().vWD_decompressedFaceString.setValeur(vWD_sRetourRequette);

// 	ToastDisplay("Face image decompressed succesfully")
WDAPIToast.toastAffiche(new WDChaineU("Face image decompressed succesfully"));

// RETURN ""
return new WDChaineU("");

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return eCatch.getValeurRetour();
}
}
finally
{
finExecProcGlobale();

}
}



public static void DecompressFaceImage(String compressedFaceImage)
{
        try {
			OmniMatchUtil omniMatchUtil = new OmniMatchUtil();

            byte[] compressedFaceImageBytes = Base64.decode(compressedFaceImage, Base64.NO_WRAP);

            byte[] decompressed = omniMatchUtil.decompressProprietaryFaceImage(compressedFaceImageBytes);

            String faceImageDecompressed = Base64.encodeToString(decompressed,Base64.NO_WRAP);

           	//send this  faceImageDecompressed back to windev
			appelProcedureWL_String("printFaceImage", faceImageDecompressed);

        } catch (IOException | OmniMatchException ignore) {

        }

}
// Summary: <specify the procedure action>
// Syntax:
// DecompressFaceImage ()
//
// Parameters:
//	None
// Return value:
// 	None
//
// Example:
// <Specify a usage example>
//
//procedure DecompressFaceImage()
//



// MODIFICATION: Import for biometric data processing (adapt path as needed in Windev)
// Note: In Windev, you may need to adjust the import path or include the BiometricDataProcessor class directly

public static void ScanCryptoDemodecoded()
{
	CryptographReader.getInstance().scanCryptograph(getCurrentActivity(), new ScannerConfig(), (action, result, errorMessage) -> {

        if (action == CryptographReaderAction.COMPLETE) {

            try {

                if (result == null) {
                    Toast.makeText(getApplicationContext(), "Cryptograph scanning failed", Toast.LENGTH_SHORT).show();
                    return;
                }

                String cryptographResult = result.toJsonString();

                // MODIFICATION: Process biometric data for Windev integration
                try {
                    // Variables for processed data
                    String processedDemographics = "";
                    String fingerprintPositionsStr = "";
                    String windevFormattedData = "";

                    // Extract and decode demographics if available
                    if (cryptographResult.contains("demographics")) {
                        // Find demographics content using string operations (Windev compatible)
                        int demoStart = cryptographResult.indexOf("\"demographics\"");
                        if (demoStart != -1) {
                            int contentStart = cryptographResult.indexOf("\"content\"", demoStart);
                            if (contentStart != -1) {
                                int valueStart = cryptographResult.indexOf("\"", contentStart + 10) + 1;
                                int valueEnd = cryptographResult.indexOf("\"", valueStart);
                                if (valueStart > 0 && valueEnd > valueStart) {
                                    String base64Demographics = cryptographResult.substring(valueStart, valueEnd);

                                    // MODIFICATION: Decode Base64 demographics (Windev compatible approach)
                                    processedDemographics = decodeBase64Demographics(base64Demographics);

                                    // Log for debugging
                                    System.out.println("MODIFICATION: Decoded demographics: " + processedDemographics);
                                }
                            }
                        }
                    }

                    // MODIFICATION: Extract fingerprint positions (Windev compatible)
                    if (cryptographResult.contains("fingerprints")) {
                        fingerprintPositionsStr = extractFingerprintPositions(cryptographResult);
                        System.out.println("MODIFICATION: Fingerprint positions: " + fingerprintPositionsStr);
                    }

                    // MODIFICATION: Create Windev-formatted data string
                    windevFormattedData = createWindevFormat(processedDemographics, fingerprintPositionsStr);

                    // MODIFICATION: Create enhanced result for Windev
                    String enhancedResult = "{" +
                        "\"originalResult\":" + cryptographResult + "," +
                        "\"processedData\":{" +
                        "\"decodedDemographics\":\"" + escapeJsonString(processedDemographics) + "\"," +
                        "\"fingerprintPositions\":\"" + fingerprintPositionsStr + "\"," +
                        "\"windevFormat\":\"" + escapeJsonString(windevFormattedData) + "\"" +
                        "}" +
                        "}";

                    // MODIFICATION: Use enhanced result
                    cryptographResult = enhancedResult;

                    System.out.println("MODIFICATION: Enhanced cryptograph result with processed biometric data");

                } catch (Exception processingError) {
                    // MODIFICATION: If processing fails, continue with original result
                    System.err.println("MODIFICATION: Error processing biometric data: " + processingError.getMessage());
                    // cryptographResult remains unchanged
                }

                //send this cryptographResult json string back (now enhanced with processed data)
				Toast.makeText(getApplicationContext(), "Cryptograph decode success", Toast.LENGTH_SHORT).show();
				appelProcedureWL_String("printJsonRet",cryptographResult);

            } catch (Exception e) {

                // error in converting result to json. send this back
            }

        } else if (action == CryptographReaderAction.CANCEL) {

            // cancelled scanning. send this back

				 Toast.makeText(getApplicationContext(), "Cryptograph decode cancel", Toast.LENGTH_SHORT).show();

        } else if (action == CryptographReaderAction.ERROR) {
            Toast.makeText(getApplicationContext(), "Cryptograph scanning failed: " + errorMessage, Toast.LENGTH_SHORT).show();
            // cryptograph scanning error. send this back
        }

    });
}

// MODIFICATION: Windev-compatible helper procedures

// Decode Base64 demographics string
public static String decodeBase64Demographics(String base64String)
{
    try {
        if (base64String == null || base64String.trim().isEmpty()) {
            return "No demographics data";
        }

        // Decode Base64 (using Android/Java Base64 decoder)
        byte[] decodedBytes = android.util.Base64.decode(base64String, android.util.Base64.DEFAULT);
        String decodedString = new String(decodedBytes, "UTF-8");

        return decodedString;

    } catch (Exception e) {
		Toast.makeText(getApplicationContext(), "MODIFICATION: Error decoding demographics: " + e.getMessage(), Toast.LENGTH_SHORT).show();

        return "Error decoding demographics";
    }
}

// Extract fingerprint positions from JSON string
public static String extractFingerprintPositions(String jsonString)
{
    try {
        String positions = "";

        // Find fingerprints section
        int fingerprintsStart = jsonString.indexOf("\"fingerprints\"");
        if (fingerprintsStart == -1) {
            return "";
        }

        // Look for position numbers with content
        String[] possiblePositions = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};

        for (String pos : possiblePositions) {
            String searchPattern = "\"" + pos + "\"";
            int posIndex = jsonString.indexOf(searchPattern, fingerprintsStart);
            if (posIndex != -1) {
                // Check if this position has content
                int contentIndex = jsonString.indexOf("\"content\"", posIndex);
                if (contentIndex != -1 && contentIndex < jsonString.indexOf("}", posIndex)) {
                    int valueStart = jsonString.indexOf("\"", contentIndex + 10) + 1;
                    int valueEnd = jsonString.indexOf("\"", valueStart);
                    if (valueStart > 0 && valueEnd > valueStart) {
                        String content = jsonString.substring(valueStart, valueEnd);
                        if (!content.trim().isEmpty()) {
                            if (!positions.isEmpty()) {
                                positions += ",";
                            }
                            positions += pos;
                        }
                    }
                }
            }
        }

        return positions;

    } catch (Exception e) {
		Toast.makeText(getApplicationContext(), "MODIFICATION: Error extracting fingerprint positions: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        //System.err.println("MODIFICATION: Error extracting fingerprint positions: " + e.getMessage());
        return "";
    }
}

// Create Windev-formatted data string
public static String createWindevFormat(String demographics, String fingerprintPositions)
{
    try {
        String windevFormat = "";

        // Add demographics in Windev format
        if (!demographics.isEmpty()) {
            // Split demographics by comma and join with pipe
            String[] demoParts = demographics.split(",");
            windevFormat += "DEMOGRAPHICS_ARRAY:";
            for (int i = 0; i < demoParts.length; i++) {
                windevFormat += demoParts[i].trim();
                if (i < demoParts.length - 1) {
                    windevFormat += "|";
                }
            }
            windevFormat += ";";
        }

        // Add fingerprint positions
        if (!fingerprintPositions.isEmpty()) {
            windevFormat += "FINGERPRINT_POSITIONS:" + fingerprintPositions + ";";
        }

        return windevFormat;

    } catch (Exception e) {
		Toast.makeText(getApplicationContext(), "MODIFICATION: Error creating Windev format: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        //System.err.println("MODIFICATION: Error creating Windev format: " + e.getMessage());
        return "";
    }
}

// Escape JSON string for safe inclusion
public static String escapeJsonString(String input)
{
    if (input == null) {
        return "";
    }
    return input.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
}

// Summary: Scan cryptograph and process biometric data for Windev integration
// Syntax:
// ScanCryptographe()
//
// Parameters:
//	None
// Return value:
// 	None
//
// Example:
// ScanCryptographe();
//
// MODIFICATION: This procedure now enhances the cryptograph result with:
// - Decoded demographics data (from Base64)
// - Extracted fingerprint positions
// - Windev-formatted data string
// The enhanced result is sent back via appelProcedureWL_String("printJsonRet", enhancedResult)
//


public static void CryptoFingerAndTemplateCompare(String template, String capturesFp)
{
	OmniMatchUtil omniMatchUtil = new OmniMatchUtil();
//	 byte[] imageBytes = Base64.decode(image, Base64.NO_WRAP);


        //put elements in finger templates hashmap


//		fingerTemplates.put(integer, data.getContent());
//
//        if (result.getFingerprints() != null) {
//
//            result.getFingerprints().forEach((integer, data) -> {
//
//                if (data.getContent() != null && data.getContent().length > 0)
//                {
//                    fingerTemplates.put(integer, data.getContent());
//                }
//            });
//
//        }


		HashMap<Integer, byte[]> fingerTemplates = new HashMap<>(); // store crypto template
        HashMap<Integer, byte[]> fingerimages = new HashMap<>(); // store captured FP
        //above is an empty fingers map, you need to add the finger images and template.

        // you can match finger images and templates using the below function

        omniMatchUtil.matchFingerImagesAndNistT5Templates(fingerimages, fingerTemplates, Common.ImageFormat.WSQ, 4.0f, (score, errorMessage) -> {

            if (errorMessage != null) {
//                Log.d("TAG", "finger match failed: " + errorMessage);
				Toast.makeText(getApplicationContext(), "finger match failed: " + errorMessage, Toast.LENGTH_LONG).show();
            } else {
//                Log.d("TAG", "finger match score: " + score);
                Toast.makeText(getApplicationContext(),  "finger match score: " + score, Toast.LENGTH_LONG).show();
            }
        });

		//appelProcedureWL_String("printJsonRet",returnTreatment);

}
// Summary: <specify the procedure action>
// Syntax:
// CryptoFingerAndTemplateCompare ()
//
// Parameters:
//	None
// Return value:
// 	None
//
// Example:
// <Specify a usage example>
//
//procedure CryptoFingerAndTemplateCompare()
//

// Summary: <specify the procedure action>
// Syntax:
// printFingerCaptureJson (<sRetourRequette> is string)
// 
// Parameters:
// sRetourRequette (Unicode string):
// Example:
// <Specify a usage example>
// 
// procedure printFingerCaptureJson(sRetourRequette est une chaine)

static public void fWD_printFingerCaptureJson( WDObjet vWD_sRetourRequette )
{
// procedure printFingerCaptureJson(sRetourRequette est une chaine)
ms_instance.initExecProcGlobale("printFingerCaptureJson");



try
{
vWD_sRetourRequette = WDParametre.traiterParametre(vWD_sRetourRequette, 1, 16, 0x0);



try
{
// fpBase64Encoded = sRetourRequette
GWDPintegrationSdk.getInstance().vWD_fpBase64Encoded.setValeur(vWD_sRetourRequette);

// run.fp_string = "La chaine FP est : " +fpBase64Encoded
GWDPintegrationSdk.getInstance().getrun().mWD_fp_string.setValeur(new WDChaineU("La chaine FP est : ").opPlus(GWDPintegrationSdk.getInstance().vWD_fpBase64Encoded));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcGlobale();

}
}




    public static void ScanAndCompareFingerPrints(String fpFromCrytoGraphe, String FingerToCapture)
    {
        try
        {
            T5FingerCaptureController t5FingerCaptureController = T5FingerCaptureController.getInstance();
            t5FingerCaptureController.setLicense("");

            t5FingerCaptureController.showElipses(true);
            t5FingerCaptureController.setLivenessCheck(true);

            t5FingerCaptureController.setIsGetQuality(true);
            //t5FingerCaptureController.setCreateTemplates(true);

            t5FingerCaptureController.setDetectorThreshold(0.9f);

            LinkedHashSet<SegmentationMode> segmentationModes = new LinkedHashSet<>();
            //segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_SLAP);
            //segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_SLAP);

            switch(FingerToCapture) {
            case "RT":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_THUMB);
                break;
            case "RI":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_INDEX);
                break;
            case "RM":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_MIDDLE);
                break;
            case "RR":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_RING);
                break;
            case "RL":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_RIGHT_LITTLE);
                break;
            case "LT":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_THUMB);
                break;
            case "LI":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_INDEX);
                break;
            case "LM":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_MIDDLE);
                break;
            case "LR":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_RING);
                break;
            case "LL":
                    segmentationModes.add(SegmentationMode.SEGMENTATION_MODE_LEFT_LITTLE);
                break;
            }

            t5FingerCaptureController.setSegmentationModes(segmentationModes);

            t5FingerCaptureController.setCaptureMode(CaptureMode.CAPTURE_MODE_SELF);
            t5FingerCaptureController.setCaptureSpeed(CaptureSpeed.CAPTURE_SPEED_NORMAL);
            t5FingerCaptureController.setPropDenoise(true);
            t5FingerCaptureController.setTitle("AMLA CAMEROUN");

            ImageConfiguration segmentedFingersConfiguration = new ImageConfiguration();
            segmentedFingersConfiguration.setPrimaryImageType(ImageType.IMAGE_TYPE_WSQ);
            segmentedFingersConfiguration.setRequireDisplayImage(false);
            segmentedFingersConfiguration.setDisplayImageType(ImageType.IMAGE_TYPE_PNG);

            //compresion ratio is only applicable for IMAGE_TYPE_WSQ
            segmentedFingersConfiguration.setCompressionRatio(10);
            segmentedFingersConfiguration.setIsCropImage(true);
            segmentedFingersConfiguration.setCroppedImageWidth(512);
            segmentedFingersConfiguration.setCroppedImageHeight(512);
            //0->Black color padding; 255->white color padding
            segmentedFingersConfiguration.setPaddingColor(255);

            t5FingerCaptureController.setSegmentedFingerImagesConfig(segmentedFingersConfiguration);

            ImageConfiguration slapConfig = new ImageConfiguration();
            slapConfig.setPrimaryImageType(ImageType.IMAGE_TYPE_BMP);
            slapConfig.setCompressionRatio(10);
            //	Toast.makeText(getApplicationContext(), "After image configuration... ", Toast.LENGTH_LONG).show();

            slapConfig.setIsCropImage(false);
            //            slapConfig.setCroppedImageWidth(1600);
            //            slapConfig.setCroppedImageHeight(1500);
            //            //0->Black color padding; 255->white color padding
            //            slapConfig.setPaddingColor(0);
            slapConfig.setRequireDisplayImage(false);

            t5FingerCaptureController.setSlapImagesConfig(slapConfig);

            //t5FingerCaptureController.setTimeoutInSecs(20);
            t5FingerCaptureController.setTimeoutInSecs(120);
            //	Toast.makeText(getApplicationContext(), "After set time out... ", Toast.LENGTH_LONG).show();

            t5FingerCaptureController.captureFingers(getCurrentActivity(), new T5FingerCapturedListener()
			{
                @Override
                public void onSuccess(FingerCaptureResult result)
				{
                    if (result.fingers != null && !result.fingers.isEmpty())
					{
                        HashMap<Integer, byte[]> fingers = new HashMap<>();

                        for (Finger finger : result.fingers)
						{
                            try
							{
								fingers.put(finger.pos, finger.primaryImage);
							}catch(Exception e)
							{

							}
						}


						HashMap<Integer,byte[]> templates =  convertJsonToByteArrayMap(fpFromCrytoGraphe);

						if(templates!=null&&!templates.isEmpty()&&fingers!=null&&!fingers.isEmpty())
						{
							matchFingers(fingers, templates);
						}
					}
                }

                @Override
                public void onFailure(String s)
                {
                    Toast.makeText(getApplicationContext(), "On Failure Bloc... ", Toast.LENGTH_LONG).show();
                    //appelProcedureWL_String("printProc","On Failure Bloc... "+s);
                }

                @Override
                public void onCancelled()
                {
                    Toast.makeText(getApplicationContext(), "On Cancelled Bloc... ", Toast.LENGTH_LONG).show();
                }

                @Override
                public void onTimedout()
                {
                    Toast.makeText(getApplicationContext(), "On TimeOut Bloc... ", Toast.LENGTH_LONG).show();

                }
            });

            //Toast.makeText(getApplicationContext(), "after try... ", Toast.LENGTH_LONG).show();

        } catch (Exception e)
        {
            Toast.makeText(getApplicationContext(), "in exception bloc... "+e, Toast.LENGTH_LONG).show();
        }
    }


	 public static HashMap<Integer, byte[]> convertJsonToByteArrayMap(String jsonString)
	{
		HashMap<Integer, byte[]> resultMap = new HashMap<>();

		try
		{
			JSONObject jsonObject = new JSONObject(jsonString);
			Iterator<String> keys = jsonObject.keys();

			while (keys.hasNext())
			{
				String key = keys.next();
				try
				{
					int intKey = Integer.parseInt(key);
					JSONObject valueObject = jsonObject.getJSONObject(key);
					String base64Content = valueObject.getString("content");

					// Decode Base64 string to byte array
					byte[] byteArray = Base64.decode(base64Content, Base64.NO_WRAP);
					resultMap.put(intKey, byteArray);
				} catch (NumberFormatException e) {
					System.err.println("Skipping non-integer key: " + key);
				}
			}

		}catch (Exception e){

		}

		return resultMap;
	}

     private static void matchFingers(HashMap<Integer, byte[]> fingerImages, HashMap<Integer, byte[]> templates)
     {
		//String ret;
		OmniMatchUtil omniMatchUtil = new OmniMatchUtil();
        omniMatchUtil.matchFingerImagesAndNistT5Templates(fingerImages, templates, Common.ImageFormat.WSQ, 4.0f, (score, errorMessage) -> {

            //Log.d("TAG", "match error: " + errorMessage + ", score: " + score);

            if (errorMessage != null && !errorMessage.isEmpty())
            {
				//ret = "Match failed: " + errorMessage
                Toast.makeText(getApplicationContext(), "Match failed: " + errorMessage, Toast.LENGTH_LONG).show();
            } else
            {
                Toast.makeText(getApplicationContext(), "Match score : (" + score + ")", Toast.LENGTH_LONG).show();
            }
        });
    }

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
