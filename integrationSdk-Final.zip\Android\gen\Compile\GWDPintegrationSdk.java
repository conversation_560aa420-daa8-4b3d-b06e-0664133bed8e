/**
 * Code generated by WINDEV Mobile - DO NOT MODIFY!
 * WINDEV Mobile object: Projet
 * Android class: integrationSdk
 * Version of wdjava64.dll: 30.0.302.8
 */


package com.amlacameroon.sandbox.wdgen;


import com.amlacameroon.sandbox.*;
import fr.pcsoft.wdjava.core.types.*;
import fr.pcsoft.wdjava.core.*;
import fr.pcsoft.wdjava.core.application.*;
import fr.pcsoft.wdjava.core.context.*;
import fr.pcsoft.wdjava.api.*;
import fr.pcsoft.wdjava.json.*;
import fr.pcsoft.wdjava.core.poo.*;
import fr.pcsoft.wdjava.core.erreur.*;
/*Imports trouvés dans le code WL*/
/*Fin Imports trouvés dans le code WL*/





public class GWDPintegrationSdk extends WDProjet
{
private static GWDPintegrationSdk ms_instance = null;
/**
 * Accès au projet: integrationSdk
 * Pour accéder au projet à partir de n'importe où: 
 * GWDPintegrationSdk.getInstance()
 */
public static GWDPintegrationSdk getInstance()
{
return (GWDPintegrationSdk) ms_instance;
}

 // run
public GWDFrun mWD_run = new GWDFrun();
 // accesseur de run
public GWDFrun getrun()
{
mWD_run.checkOuverture();
return mWD_run;
}

 // FEN_Mobile_Camera_UI
public GWDFFEN_Mobile_Camera_UI mWD_FEN_Mobile_Camera_UI = new GWDFFEN_Mobile_Camera_UI();
 // accesseur de FEN_Mobile_Camera_UI
public GWDFFEN_Mobile_Camera_UI getFEN_Mobile_Camera_UI()
{
mWD_FEN_Mobile_Camera_UI.checkOuverture();
return mWD_FEN_Mobile_Camera_UI;
}

 // tt
public GWDFtt mWD_tt = new GWDFtt();
 // accesseur de tt
public GWDFtt gettt()
{
mWD_tt.checkOuverture();
return mWD_tt;
}


 // InternalWindow1
public GWDFIInternalWindow1 mWD_InternalWindow1 = new GWDFIInternalWindow1();
 // accesseur de InternalWindow1
public GWDFIInternalWindow1 getInternalWindow1()
{
GWDFIInternalWindow1 fiCtx = (GWDFIInternalWindow1)WDAppelContexte.getContexte().getFenetreInterne("InternalWindow1");
return fiCtx != null ? fiCtx  : mWD_InternalWindow1;
}

 // Constructeur de la classe GWDPintegrationSdk
public GWDPintegrationSdk()
{
ms_instance = this;
// Définition des langues du projet
setLangueProjet(new int[] {1}, new int[] {65001}, 1, false);
ajouterCollectionProcedures(GWDCPSDKInt.getInstance());

// Palette des couleurs
setPaletteCouleurGabarit(getCouleur_GEN(0xff394ae1), getCouleur_GEN(0xff26a0fa), getCouleur_GEN(0xff6dbc61), getCouleur_GEN(0xff85a800), getCouleur_GEN(0xffd2ac54), getCouleur_GEN(0xffc9822b), getCouleur_GEN(0xffa95cf8), getCouleur_GEN(0xffb86592), getCouleur_GEN(0xffb74a5e), getCouleur_GEN(0xff654e44), getCouleur_GEN(0xffa5a595));
ajouterFenetre("run", mWD_run);
ajouterFenetre("FEN_Mobile_Camera_UI", mWD_FEN_Mobile_Camera_UI);
ajouterFenetre("tt", mWD_tt);
ajouterFenetreInterne("InternalWindow1");



}

// Code de déclaration de integrationSdk
public void trtInitProjet()
// 
{
// 

try
{
// gkeyCrypto est une chaine = "0c5204df931ae3e7e75b9fd99d8098a7adf29fc1dfd931150133ea222fe0b0e616ecd232cb12a6f5391f6ee20062386d7e1e105d9d8cf4911d9f8fe949cbe9cf6301b4bcb208abbf204ee13377aa35091e9edbac97c41500b8c917e6bee4a6fa36659e6369fd6e658fb517e8518575f6c4703922332b9ba79cda52ede2b9e0be"
vWD_gkeyCrypto = new WDChaineU();

vWD_gkeyCrypto.setValeur("0c5204df931ae3e7e75b9fd99d8098a7adf29fc1dfd931150133ea222fe0b0e616ecd232cb12a6f5391f6ee20062386d7e1e105d9d8cf4911d9f8fe949cbe9cf6301b4bcb208abbf204ee13377aa35091e9edbac97c41500b8c917e6bee4a6fa36659e6369fd6e658fb517e8518575f6c4703922332b9ba79cda52ede2b9e0be");

super.ajouterVariableGlobale("gkeyCrypto",vWD_gkeyCrypto);



// connextionMessage est une chaîne = "Problème de connectivité détecté. Veuillez vérifier votre connexion à internet, elle est indispensable pour le fonctionnement de l'application"
vWD_connextionMessage = new WDChaineU();

vWD_connextionMessage.setValeur("Problème de connectivité détecté. Veuillez vérifier votre connexion à internet, elle est indispensable pour le fonctionnement de l'application");

super.ajouterVariableGlobale("connextionMessage",vWD_connextionMessage);



// SI Ping("www.checktatoo.com") = Faux ALORS
if(WDAPINet.ping("www.checktatoo.com").opEgal(false, 0))
{
// 	Info(connextionMessage)
WDAPIDialogue.info(vWD_connextionMessage.getString());

}

// gUrl est une chaîne = "http://my.kyvala.com" // "http://127.0.0.1:8000"
vWD_gUrl = new WDChaineU();

vWD_gUrl.setValeur("http://my.kyvala.com");

super.ajouterVariableGlobale("gUrl",vWD_gUrl);



// <COMPILE SI Configuration="Application Android">

// retourapi est un JSON
vWD_retourapi = new WDInstance( new WDJSON() );

super.ajouterVariableGlobale("retourapi",vWD_retourapi);



// decompressedFaceString, fpBase64Encoded est une chaine
vWD_decompressedFaceString = new WDChaineU();

super.ajouterVariableGlobale("decompressedFaceString",vWD_decompressedFaceString);


vWD_fpBase64Encoded = new WDChaineU();

super.ajouterVariableGlobale("fpBase64Encoded",vWD_fpBase64Encoded);



// <COMPILE IF Configuration="Application Android">
// 	InitialisingOmnimatchSdk()
GWDCPSDKInt.InitialisingOmnimatchSdk();

// 	initializeReader(gkeyCrypto)
GWDCPSDKInt.initializeReader(vWD_gkeyCrypto.getString());


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}





////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
static public WDObjet vWD_gkeyCrypto = WDVarNonAllouee.ref;
static public WDObjet vWD_connextionMessage = WDVarNonAllouee.ref;
static public WDObjet vWD_gUrl = WDVarNonAllouee.ref;
static public WDObjet vWD_retourapi = WDVarNonAllouee.ref;
static public WDObjet vWD_decompressedFaceString = WDVarNonAllouee.ref;
static public WDObjet vWD_fpBase64Encoded = WDVarNonAllouee.ref;
public String getVersionApplication(){ return "0.0.354.0";}
public String getNomSociete(){ return "AMLA CAMEROUN";}
public String getNomAPK(){ return "integrationSdk";}
public int getIdNomApplication(){return com.amlacameroon.sandbox.R.string.app_name;}
public boolean isModeAnsi(){ return false;}
public boolean isAssistanceAutoHFActive(){ return true;}
public String getNomFichierProperties(int nType)
{
switch (nType)
{
case 1 : return "options_compilation_985b3bcb0cd148dfb2fdf5e7332c0f3c.properties";
default : return "";
}
}
public String getPackageRacine(){ return "com.amlacameroon.sandbox";}
public int getIdIconeApplication(){ return com.amlacameroon.sandbox.R.drawable.i_c_o_n_e________0;}
public int getInfoPlateforme(EWDInfoPlateforme info)
{
switch(info)
{
case DPI_ECRAN : return 160;
case HAUTEUR_BARRE_SYSTEME : return 25;
case HAUTEUR_BARRE_TITRE : return 25;
case HAUTEUR_ACTION_BAR : return 56;
case HAUTEUR_BARRE_BAS : return 0;
case HAUTEUR_ECRAN : return 650;
case LARGEUR_ECRAN : return 360;
default : return 0;
}
}
public boolean isActiveThemeMaterialDesign()
{
return true;
}
////////////////////////////////////////////////////////////////////////////
public String getAdresseEmail() 
{
return "";
}
public boolean isIgnoreErreurCertificatHTTPS()
{
return false;
}
////////////////////////////////////////////////////////////////////////////
public boolean isUniteAffichageLogique()
{
return false;
}
public String getNomProjet()
{
return "integrationSdk";
}
public String getNomConfiguration()
{
return "Application Android";
}
public boolean isModeGestionFichierMultiUtilisateur()
{
return true;
}
public boolean isCreationAutoFichierDonnees()
{
return true;
}

////////////////////////////////////////////////////////////////////////////
// Formats des masques du projet
////////////////////////////////////////////////////////////////////////////
public String getFichierWDM()
{
return null;
}
protected void declarerRessources()
{
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\WON_BASIC-CHECK-BLEUE.PNG",com.amlacameroon.sandbox.R.drawable.won_basic_check_bleue_23, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\UTILISATEUR-BLEUE.PNG?E5",com.amlacameroon.sandbox.R.drawable.utilisateur_bleue_22_selector, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\IMG-CHARGEMENT-09.SVG",com.amlacameroon.sandbox.R.raw.img_chargement_09_21, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\GABARITS\\WM\\250 PHOENIX\\PHOENIX_PICT_NAVIGFORWARD_16_5.PNG",com.amlacameroon.sandbox.R.drawable.phoenix_pict_navigforward_16_5_20, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\GABARITS\\WM\\250 PHOENIX\\PHOENIX_EDT.PNG?E5_3NP_8_8_8_8",com.amlacameroon.sandbox.R.drawable.phoenix_edt_19_np3_8_8_8_8_selector, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\GABARITS\\WM\\250 PHOENIX\\PHOENIX_COMBO.PNG?E5_3NP_32_6_42_6",com.amlacameroon.sandbox.R.drawable.phoenix_combo_18_np3_32_6_42_6_selector, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\GABARITS\\WM\\250 PHOENIX\\PHOENIX_BTN_STD.PNG?E5_3NP_10_10_10_10",com.amlacameroon.sandbox.R.drawable.phoenix_btn_std_17_np3_10_10_10_10_selector, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\GABARITS\\WM\\250 PHOENIX\\PHOENIX_BTN_MENU.PNG?E5_3NP_10_10_10_10",com.amlacameroon.sandbox.R.drawable.phoenix_btn_menu_16_np3_10_10_10_10_selector, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\GABARITS\\WM\\250 PHOENIX\\PHOENIX_BREAK_PICT.PNG?E2_4O",com.amlacameroon.sandbox.R.drawable.phoenix_break_pict_15_selector, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\EMPREINTE-BLEUE.PNG?E5",com.amlacameroon.sandbox.R.drawable.empreinte_bleue_14_selector, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\CRYPTO TEST JUMP.PNG",com.amlacameroon.sandbox.R.drawable.crypto_test_jump_13, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\VIDEO-ENREGISTRER_MOBILE_66_1.SVG",com.amlacameroon.sandbox.R.raw.video_enregistrer_mobile_66_1_12, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\VIDEO-ENREGISTRER-STOP_MOBILE_66_1.SVG",com.amlacameroon.sandbox.R.raw.video_enregistrer_stop_mobile_66_1_11, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\SWITCH-CAMERA_MOBILE_24_1.SVG",com.amlacameroon.sandbox.R.raw.switch_camera_mobile_24_1_10, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\CM_CROIXBLANCHE.SVG",com.amlacameroon.sandbox.R.raw.cm_croixblanche_9, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\CAMERA-VIDEO_MOBILE_24_1.SVG",com.amlacameroon.sandbox.R.raw.camera_video_mobile_24_1_8, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\AUTOMATIQUE-ECLAIR-FLASH-ALIMENTATION_MOBILE_16_1.SVG",com.amlacameroon.sandbox.R.raw.automatique_eclair_flash_alimentation_mobile_16_1_7, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\AUTOMATIQUE-ECLAIR-FLASH-ALIMENTATION-DESACTIVE-OFF_MOBILE_16_1.SVG",com.amlacameroon.sandbox.R.raw.automatique_eclair_flash_alimentation_desactive_off_mobile_16_1_6, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\AUTOMATIQUE-ECLAIR-FLASH-ALIMENTATION-ACTIVE-ON_MOBILE_16_1.SVG",com.amlacameroon.sandbox.R.raw.automatique_eclair_flash_alimentation_active_on_mobile_16_1_5, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\APPAREIL-PHOTO_MOBILE_24_1.SVG",com.amlacameroon.sandbox.R.raw.appareil_photo_mobile_24_1_4, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\APPAREIL-PHOTO-CAPTURE_MOBILE_66_1.SVG",com.amlacameroon.sandbox.R.raw.appareil_photo_capture_mobile_66_1_3, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\WON_BASIC-CHECK 1.PNG",com.amlacameroon.sandbox.R.drawable.won_basic_check_1_2, "");
super.ajouterFichierAssocie("C:\\USERS\\<USER>\\DOCUMENTS\\INTEGRATIONSDK-COPY\\IMG_LOAD-BOX_LIGHT.PNG",com.amlacameroon.sandbox.R.drawable.img_load_box_light_1, "");
}
// Initialisation des collections de procédures
public void initCollections()
{
GWDCPSDKInt.init();

}


// Terminaison des collections de procédures
public void terminaisonCollections()
{
GWDCPSDKInt.term();

}

/**
 * Lancer de l'application Android
 */
public static class WDLanceur extends WDAbstractLanceur
{
public Class<? extends WDProjet> getClasseProjet()
{
return GWDPintegrationSdk.class;
}
}
}
