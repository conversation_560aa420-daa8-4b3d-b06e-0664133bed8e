// CryptoID Mobile Application - Main Window
// WLanguage code for WinDev Mobile

// Global variables
GLOBAL
	gsCurrentAdmin AS STRING = ""
	gnCurrentAdminID AS INT = 0
	gbIsAdminLoggedIn AS BOOLEAN = False
END

// Main window initialization
PROCEDURE WIN_Main_Initialize()
	// Initialize the database connection
	IF NOT InitializeDatabase() THEN
		Error("Failed to initialize database connection")
		EndProgram()
	END
	
	// Setup UI elements
	SetupMainInterface()
END

// Initialize database connection
PROCEDURE InitializeDatabase() : BOOLEAN
	LOCAL bResult AS BOOLEAN = True
	
	// Connect to SQLite database
	IF NOT HOpenConnection("CryptoIDDB", "", "", "CryptoID.db", "", hAccessHFSQL) THEN
		bResult = False
	END
	
	RESULT bResult
END

// Setup main interface
PROCEDURE SetupMainInterface()
	// Set window title
	WIN_Main..Title = "CryptoID Mobile"
	
	// Configure buttons
	BTN_GenerateID..Caption = "Generate ID Cryptograph"
	BTN_ScanCrypto..Caption = "Scan Cryptograph"
	
	// Set button images/icons if available
	BTN_GenerateID..Image = "generate_icon.png"
	BTN_ScanCrypto..Image = "scan_icon.png"
END

// Generate ID button click event
PROCEDURE BTN_GenerateID_Click()
	// Check if admin is logged in
	IF NOT gbIsAdminLoggedIn THEN
		// Open admin login window
		Open(WIN_AdminLogin)
	ELSE
		// Open ID category selection
		Open(WIN_IDCategorySelection)
	END
END

// Scan Cryptograph button click event
PROCEDURE BTN_ScanCrypto_Click()
	// Open scan options window (no authentication required)
	Open(WIN_ScanOptions)
END

// Admin logout procedure
PROCEDURE AdminLogout()
	gsCurrentAdmin = ""
	gnCurrentAdminID = 0
	gbIsAdminLoggedIn = False
	
	Info("Admin logged out successfully")
END

// Application cleanup
PROCEDURE WIN_Main_Close()
	// Close database connection
	HCloseConnection("CryptoIDDB")
	
	// End application
	EndProgram()
END
