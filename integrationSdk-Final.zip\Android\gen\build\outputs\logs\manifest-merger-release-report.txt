-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1-4500
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1-4500
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1-4500
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1-4500
MERGED from [airsnap-face-pro-core-1.2.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c45acb41f53c943bee525bda3e56de46\transformed\jetified-airsnap-face-pro-core-1.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:2:1-16:12
MERGED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:2:1-15:12
MERGED from [android-wrapper-4.5.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c0193cd3c6deec0a4e6b4085cb85b1b2\transformed\jetified-android-wrapper-4.5.1\AndroidManifest.xml:2:1-12:12
MERGED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:2:1-15:12
MERGED from [OmnimatchUtil-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a4a2fe34379fbe4d4586315a42588bef\transformed\jetified-OmnimatchUtil-release\AndroidManifest.xml:2:1-7:12
MERGED from [opencv_460-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b809a9eb03b2d9f414a86d0004800770\transformed\jetified-opencv_460-release\AndroidManifest.xml:2:1-9:12
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:2:1-13:12
MERGED from [T5CryptographClient-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d76c5e9ce513c6b417e1701a9fa03532\transformed\jetified-T5CryptographClient-release\AndroidManifest.xml:2:1-9:12
MERGED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:2:1-13:12
MERGED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:2:1-13:12
MERGED from [TLVDecode-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\0bbdfcd28894a7d2d90bfb5f28c0f64c\transformed\jetified-TLVDecode-release\AndroidManifest.xml:2:1-7:12
MERGED from [WD300Android.aar] C:\Users\<USER>\.gradle\caches\transforms-3\0d8eb4647bfff8495bf1c0a39c269c2b\transformed\jetified-WD300Android\AndroidManifest.xml:2:1-7:12
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:2:1-47:12
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e58339227eea27976199f5cf8fe7c88\transformed\material-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f434415aff5a7c033874cd3c8765f457\transformed\constraintlayout-2.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-view:1.0.0-alpha28] C:\Users\<USER>\.gradle\caches\transforms-3\7cb99b499b035d9638203514c2c2601b\transformed\jetified-camera-view-1.0.0-alpha28\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\e34f51530ac83adb25bdd1cc39247b9a\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b88e4f1102d8b08c17e772a2c7829ea1\transformed\appcompat-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-camera2:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7c7fb7b5574366d60fd07efaae1ff10\transformed\jetified-camera-camera2-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-lifecycle:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c9a8f037c43433ad1c0c554a8f8d34\transformed\jetified-camera-lifecycle-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-core:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c6df09c3e89373e09eb0645698cf39d9\transformed\jetified-camera-core-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-3\cb70f78d45420b59ef3f9fd7b54c4a15\transformed\swiperefreshlayout-1.1.0-alpha03\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\63b5ae39318766b5dd7e8116f42ec9e1\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\01545e93efa0c031c7de57f8850972a2\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\72de7e762df7c43ffd01ce059f3b8139\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35c215daf508ea2ed905f6c95eecfcdb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c07b0be527dbc89e7fba9344b85062\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\556fc0646425edbc97aa43751507d932\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b64eec0b7a88160a6e6bb64fa31568da\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c0f328dba3cc25af5f7c61e5975fd1b4\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb796c0a05bdbd36488a2ff0bb2fa415\transformed\jetified-activity-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\31f0b8ea4d81f50266dc1ff677fe56ae\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3e342b3e860162d16348275d3eabc53\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb2ecef6d4136617693791a86ff4c84a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c7b45c02a319d21e2673c065692f06\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c47e73e3e1aba521d00aa8b48fedcb3e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f1addd3f6cc7e5bc0909096ed371575\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5acbe31dc029a5efd0e84b8fa481e39\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e4b68c5a4009ef6ab8d41b8910a7e65\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56bb13959964d8b2e1a7e27387dd8ebb\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aa4623d3dab8fda6af309c5b40d4918\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27d1ee9f31d83f6b693a1bf75bd9da6c\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1-4500
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1-4500
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1-4500
	package
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:117-151
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
	android:versionName
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:152-183
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:70-116
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:11-69
	android:versionCode
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:184-209
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
	android:installLocation
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:210-240
application
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:241-3549
MERGED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:8:5-14:19
MERGED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:8:5-14:19
MERGED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:7:5-13:19
MERGED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:7:5-13:19
MERGED from [android-wrapper-4.5.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c0193cd3c6deec0a4e6b4085cb85b1b2\transformed\jetified-android-wrapper-4.5.1\AndroidManifest.xml:9:5-10:19
MERGED from [android-wrapper-4.5.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c0193cd3c6deec0a4e6b4085cb85b1b2\transformed\jetified-android-wrapper-4.5.1\AndroidManifest.xml:9:5-10:19
MERGED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:7:5-13:19
MERGED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:7:5-13:19
MERGED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:7:5-11:19
MERGED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:7:5-11:19
MERGED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:7:5-11:19
MERGED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:7:5-11:19
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:39:5-45:19
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:39:5-45:19
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e58339227eea27976199f5cf8fe7c88\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e58339227eea27976199f5cf8fe7c88\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f434415aff5a7c033874cd3c8765f457\transformed\constraintlayout-2.1.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f434415aff5a7c033874cd3c8765f457\transformed\constraintlayout-2.1.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:489-532
	android:extractNativeLibs
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:533-565
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
	android:label
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:316-348
	android:resizeableActivity
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:419-452
	android:largeHeap
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:393-417
	android:icon
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:349-392
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:453-488
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:254-315
activity#com.amlacameroon.sandbox.wdgen.GWDPintegrationSdk$WDLanceur
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:566-1008
	android:label
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:627-659
	android:exported
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:832-855
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:729-831
	android:theme
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:660-728
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:576-626
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:856-997
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:871-922
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:879-920
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:922-981
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:932-979
activity#com.amlacameroon.sandbox.wdgen.GWDFFEN_Mobile_Camera_UI$WDActiviteFenetre
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1008-1307
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1230-1269
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1270-1305
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1083-1192
	android:theme
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1193-1229
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1018-1082
activity#com.amlacameroon.sandbox.wdgen.GWDFrun$WDActiviteFenetre
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1307-1589
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1512-1551
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1552-1587
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1365-1474
	android:theme
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1475-1511
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1317-1364
activity#com.amlacameroon.sandbox.wdgen.GWDFtt$WDActiviteFenetre
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1589-1870
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1793-1832
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1833-1868
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1646-1755
	android:theme
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1756-1792
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1599-1645
uses-library#org.apache.http.legacy
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1870-1948
	android:required
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1922-1946
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1884-1921
activity#fr.pcsoft.wdjava.core.erreur.report.WDErrorReportActivity
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1948-2110
	tools:node
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2088-2108
	android:theme
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2031-2087
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1958-2030
provider#androidx.core.content.FileProvider
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2110-2424
	tools:node
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2290-2310
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2255-2289
	android:authorities
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2170-2229
	android:exported
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2230-2254
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2120-2169
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2311-2413
	android:resource
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2373-2411
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2322-2372
receiver#fr.pcsoft.wdjava.core.utils.WDAppUtils$APKInstallBroadcastReceiver
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2424-2563
	tools:node
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2541-2561
	android:exported
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2516-2540
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2434-2515
activity#fr.pcsoft.wdjava.ui.activite.WDActivite$BlankActivity
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2563-2733
	tools:node
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2711-2731
	android:theme
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2642-2710
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2573-2641
provider#fr.pcsoft.wdjava.ui.searchbar.WDSearchHistory
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2733-2898
	tools:node
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2876-2896
	android:authorities
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2829-2875
	android:exported
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2804-2828
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2743-2803
activity#fr.pcsoft.wdjava.ui.searchbar.WDSearchActivity
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2898-3229
	tools:node
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3003-3023
	android:label
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2970-3002
	android:exported
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3024-3048
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2908-2969
intent-filter#action:name:android.intent.action.SEARCH
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3049-3133
action#android.intent.action.SEARCH
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3064-3117
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3072-3115
meta-data#android.app.searchable
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3133-3218
	android:resource
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3182-3216
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3144-3181
service#fr.pcsoft.wdjava.core.service.WDServiceLocal
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3229-3299
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3238-3297
meta-data#fr.pcsoft.first_window_name
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3299-3374
	android:value
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3353-3372
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3310-3352
meta-data#fr.pcsoft.splash_orientation_phone
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3374-3454
	android:value
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3435-3452
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3385-3434
meta-data#fr.pcsoft.splash_orientation_tablet
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3454-3535
	android:value
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3516-3533
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3465-3515
supports-screens
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3549-3708
	android:largeScreens
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3624-3651
	android:smallScreens
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3567-3594
	android:normalScreens
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3595-3623
	android:xlargeScreens
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3652-3680
	android:anyDensity
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3681-3706
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3708-3783
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:9:5-81
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:9:5-81
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:33:5-81
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:33:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3725-3781
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3783-3842
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:34:5-65
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:34:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3800-3840
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3842-3907
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3859-3905
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3907-3968
MERGED from [android-wrapper-4.5.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c0193cd3c6deec0a4e6b4085cb85b1b2\transformed\jetified-android-wrapper-4.5.1\AndroidManifest.xml:7:5-67
MERGED from [android-wrapper-4.5.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c0193cd3c6deec0a4e6b4085cb85b1b2\transformed\jetified-android-wrapper-4.5.1\AndroidManifest.xml:7:5-67
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:11:5-67
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:11:5-67
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:36:5-67
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:36:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3924-3966
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3968-4042
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:10:5-80
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:10:5-80
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:37:5-80
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:37:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3985-4040
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4042-4111
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4059-4109
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4111-4181
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4128-4179
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4181-4241
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4198-4239
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4241-4329
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:26:5-28:35
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:26:5-28:35
	android:required
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4304-4327
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4255-4303
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4329-4407
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:29:5-31:35
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:29:5-31:35
	android:required
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4382-4405
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4343-4381
uses-feature#android.hardware.microphone
ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4407-4489
	android:required
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4464-4487
	android:name
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4421-4463
uses-sdk
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
MERGED from [airsnap-face-pro-core-1.2.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c45acb41f53c943bee525bda3e56de46\transformed\jetified-airsnap-face-pro-core-1.2.3\AndroidManifest.xml:5:5-44
MERGED from [airsnap-face-pro-core-1.2.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c45acb41f53c943bee525bda3e56de46\transformed\jetified-airsnap-face-pro-core-1.2.3\AndroidManifest.xml:5:5-44
MERGED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:6:5-44
MERGED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:6:5-44
MERGED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:5:5-44
MERGED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:5:5-44
MERGED from [android-wrapper-4.5.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c0193cd3c6deec0a4e6b4085cb85b1b2\transformed\jetified-android-wrapper-4.5.1\AndroidManifest.xml:5:5-44
MERGED from [android-wrapper-4.5.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c0193cd3c6deec0a4e6b4085cb85b1b2\transformed\jetified-android-wrapper-4.5.1\AndroidManifest.xml:5:5-44
MERGED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:5:5-44
MERGED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:5:5-44
MERGED from [OmnimatchUtil-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a4a2fe34379fbe4d4586315a42588bef\transformed\jetified-OmnimatchUtil-release\AndroidManifest.xml:5:5-44
MERGED from [OmnimatchUtil-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a4a2fe34379fbe4d4586315a42588bef\transformed\jetified-OmnimatchUtil-release\AndroidManifest.xml:5:5-44
MERGED from [opencv_460-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b809a9eb03b2d9f414a86d0004800770\transformed\jetified-opencv_460-release\AndroidManifest.xml:5:5-7:41
MERGED from [opencv_460-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b809a9eb03b2d9f414a86d0004800770\transformed\jetified-opencv_460-release\AndroidManifest.xml:5:5-7:41
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:5:5-7:41
MERGED from [T5AirSnap-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\cf5d9d2ef71d5888723487dfb5552d1b\transformed\jetified-T5AirSnap-release\AndroidManifest.xml:5:5-7:41
MERGED from [T5CryptographClient-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d76c5e9ce513c6b417e1701a9fa03532\transformed\jetified-T5CryptographClient-release\AndroidManifest.xml:5:5-7:41
MERGED from [T5CryptographClient-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d76c5e9ce513c6b417e1701a9fa03532\transformed\jetified-T5CryptographClient-release\AndroidManifest.xml:5:5-7:41
MERGED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:5:5-44
MERGED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:5:5-44
MERGED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:5:5-44
MERGED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:5:5-44
MERGED from [TLVDecode-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\0bbdfcd28894a7d2d90bfb5f28c0f64c\transformed\jetified-TLVDecode-release\AndroidManifest.xml:5:5-44
MERGED from [TLVDecode-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\0bbdfcd28894a7d2d90bfb5f28c0f64c\transformed\jetified-TLVDecode-release\AndroidManifest.xml:5:5-44
MERGED from [WD300Android.aar] C:\Users\<USER>\.gradle\caches\transforms-3\0d8eb4647bfff8495bf1c0a39c269c2b\transformed\jetified-WD300Android\AndroidManifest.xml:5:5-44
MERGED from [WD300Android.aar] C:\Users\<USER>\.gradle\caches\transforms-3\0d8eb4647bfff8495bf1c0a39c269c2b\transformed\jetified-WD300Android\AndroidManifest.xml:5:5-44
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:9:5-11:41
MERGED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:9:5-11:41
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e58339227eea27976199f5cf8fe7c88\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e58339227eea27976199f5cf8fe7c88\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f434415aff5a7c033874cd3c8765f457\transformed\constraintlayout-2.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f434415aff5a7c033874cd3c8765f457\transformed\constraintlayout-2.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-view:1.0.0-alpha28] C:\Users\<USER>\.gradle\caches\transforms-3\7cb99b499b035d9638203514c2c2601b\transformed\jetified-camera-view-1.0.0-alpha28\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-view:1.0.0-alpha28] C:\Users\<USER>\.gradle\caches\transforms-3\7cb99b499b035d9638203514c2c2601b\transformed\jetified-camera-view-1.0.0-alpha28\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\e34f51530ac83adb25bdd1cc39247b9a\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\e34f51530ac83adb25bdd1cc39247b9a\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b88e4f1102d8b08c17e772a2c7829ea1\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\b88e4f1102d8b08c17e772a2c7829ea1\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-camera2:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7c7fb7b5574366d60fd07efaae1ff10\transformed\jetified-camera-camera2-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-camera2:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7c7fb7b5574366d60fd07efaae1ff10\transformed\jetified-camera-camera2-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-lifecycle:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c9a8f037c43433ad1c0c554a8f8d34\transformed\jetified-camera-lifecycle-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-lifecycle:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\60c9a8f037c43433ad1c0c554a8f8d34\transformed\jetified-camera-lifecycle-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-core:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c6df09c3e89373e09eb0645698cf39d9\transformed\jetified-camera-core-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-core:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c6df09c3e89373e09eb0645698cf39d9\transformed\jetified-camera-core-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-3\cb70f78d45420b59ef3f9fd7b54c4a15\transformed\swiperefreshlayout-1.1.0-alpha03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-3\cb70f78d45420b59ef3f9fd7b54c4a15\transformed\swiperefreshlayout-1.1.0-alpha03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\63b5ae39318766b5dd7e8116f42ec9e1\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\63b5ae39318766b5dd7e8116f42ec9e1\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\01545e93efa0c031c7de57f8850972a2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\01545e93efa0c031c7de57f8850972a2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\72de7e762df7c43ffd01ce059f3b8139\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\72de7e762df7c43ffd01ce059f3b8139\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35c215daf508ea2ed905f6c95eecfcdb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35c215daf508ea2ed905f6c95eecfcdb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c07b0be527dbc89e7fba9344b85062\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c07b0be527dbc89e7fba9344b85062\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\556fc0646425edbc97aa43751507d932\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\556fc0646425edbc97aa43751507d932\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b64eec0b7a88160a6e6bb64fa31568da\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b64eec0b7a88160a6e6bb64fa31568da\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c0f328dba3cc25af5f7c61e5975fd1b4\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c0f328dba3cc25af5f7c61e5975fd1b4\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb796c0a05bdbd36488a2ff0bb2fa415\transformed\jetified-activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb796c0a05bdbd36488a2ff0bb2fa415\transformed\jetified-activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\31f0b8ea4d81f50266dc1ff677fe56ae\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\31f0b8ea4d81f50266dc1ff677fe56ae\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3e342b3e860162d16348275d3eabc53\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3e342b3e860162d16348275d3eabc53\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb2ecef6d4136617693791a86ff4c84a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb2ecef6d4136617693791a86ff4c84a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c7b45c02a319d21e2673c065692f06\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c7b45c02a319d21e2673c065692f06\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c47e73e3e1aba521d00aa8b48fedcb3e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c47e73e3e1aba521d00aa8b48fedcb3e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f1addd3f6cc7e5bc0909096ed371575\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f1addd3f6cc7e5bc0909096ed371575\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5acbe31dc029a5efd0e84b8fa481e39\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5acbe31dc029a5efd0e84b8fa481e39\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e4b68c5a4009ef6ab8d41b8910a7e65\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e4b68c5a4009ef6ab8d41b8910a7e65\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56bb13959964d8b2e1a7e27387dd8ebb\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56bb13959964d8b2e1a7e27387dd8ebb\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aa4623d3dab8fda6af309c5b40d4918\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aa4623d3dab8fda6af309c5b40d4918\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27d1ee9f31d83f6b693a1bf75bd9da6c\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27d1ee9f31d83f6b693a1bf75bd9da6c\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
activity#ai.tech5.pheonix.capture.activity.CaptureImageActivity
ADDED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:9:9-13:74
	android:screenOrientation
		ADDED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:12:13-49
	android:hardwareAccelerated
		ADDED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:11:13-47
	android:theme
		ADDED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:13:13-71
	android:name
		ADDED from [AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:10:13-82
activity#ai.tech5.finger.FingerCaptureActivity
ADDED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:8:9-12:74
	android:screenOrientation
		ADDED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:11:13-49
	android:hardwareAccelerated
		ADDED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:10:13-47
	android:theme
		ADDED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:12:13-71
	android:name
		ADDED from [AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:9:13-65
activity#ai.tech5.cryptograph.reader.api.ScanCryptographActivity
ADDED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:8:9-12:74
	android:screenOrientation
		ADDED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:11:13-49
	android:exported
		ADDED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:12:13-71
	android:name
		ADDED from [CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:9:13-83
activity#ai.tech5.t5ncnn.MainActivity
ADDED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:8:9-10:40
	android:exported
		ADDED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:10:13-37
	android:name
		ADDED from [t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:9:13-56
activity#ai.tech5.t5opencv.MainActivity
ADDED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:8:9-10:40
	android:exported
		ADDED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:10:13-37
	android:name
		ADDED from [t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:9:13-58
queries
ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:13:5-24:15
	xmlns:android
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:13:14-72
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:image/*
ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:14:9-18:18
action#android.intent.action.GET_CONTENT
ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:15:13-72
	android:name
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:15:21-69
data
ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:17:13-48
	android:mimeType
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:17:19-45
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:video/*
ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:19:9-23:18
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:35:5-79
	android:name
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:35:22-76
activity#fr.pcsoft.wdandroid_wdl.wdgen.GWDFFEN_LICENCE_WX$WDActiviteFenetre
ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:40:9-44:52
	android:hardwareAccelerated
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:43:13-48
	android:configChanges
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:42:13-122
	android:theme
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:44:13-49
	android:name
		ADDED from [wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:41:13-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
