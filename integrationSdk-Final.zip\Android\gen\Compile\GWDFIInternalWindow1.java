/**
 * Code generated by WINDEV Mobile - DO NOT MODIFY!
 * WINDEV Mobile object: Fenêtre
 * Android class: InternalWindow1
 * Version of wdjava64.dll: 30.0.302.8
 */


package com.amlacameroon.sandbox.wdgen;


import com.amlacameroon.sandbox.*;
import fr.pcsoft.wdjava.core.types.*;
import fr.pcsoft.wdjava.core.*;
import fr.pcsoft.wdjava.ui.champs.fenetreinterne.*;
import fr.pcsoft.wdjava.ui.champs.layout.*;
import fr.pcsoft.wdjava.ui.champs.image.*;
import fr.pcsoft.wdjava.ui.cadre.*;
import fr.pcsoft.wdjava.ui.champs.libelle.*;
import fr.pcsoft.wdjava.ui.champs.bouton.*;
import fr.pcsoft.wdjava.core.erreur.*;
import fr.pcsoft.wdjava.ui.style.shadow.*;
import fr.pcsoft.wdjava.api.*;
import fr.pcsoft.wdjava.core.application.*;
/*Imports trouvés dans le code WL*/
/*Fin Imports trouvés dans le code WL*/



public class GWDFIInternalWindow1 extends WDFenetreInterne
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs de InternalWindow1
////////////////////////////////////////////////////////////////////////////

/**
 * CELL_NoName14
 */
class GWDCELL_NoName14 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName14
////////////////////////////////////////////////////////////////////////////

/**
 * IMG_NoName1
 */
class GWDIMG_NoName1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName14.IMG_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225928518600l);

super.setChecksum("713630778");

super.setNom("IMG_NoName1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(4, 26);

super.setTailleInitiale(49, 49);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Img_load-box_light.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_NoName1 mWD_IMG_NoName1 = new GWDIMG_NoName1();

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName14.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225928584136l);

super.setChecksum("713694034");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Selfie");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(54, 41);

super.setTailleInitiale(99, 19);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°3 de InternalWindow1.InternalWindow1.CELL_NoName14.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName1
 */
class GWDBTN_NoName1 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName14.FLEX_NoName1.BTN_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225928715208l);

super.setChecksum("713825562");

super.setNom("BTN_NoName1");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName1 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// <COMPILE SI TypeConfiguration=Android>
{
super.clicSurBoutonGauche();

// <COMPILE SI TypeConfiguration=Android>

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartFaceCapture(sqr,"CAMERA-FRONT")	
GWDCPSDKInt.StartFaceCapture(vWD_sqr.getString(),"CAMERA-FRONT");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName1 mWD_BTN_NoName1 = new GWDBTN_NoName1();

/**
 * IMG_check1
 */
class GWDIMG_check1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName14.FLEX_NoName1.IMG_check1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225928780744l);

super.setChecksum("713892922");

super.setNom("IMG_check1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check1 mWD_IMG_check1 = new GWDIMG_check1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName14.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName14.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName1.initialiserObjet();
super.ajouter("BTN_NoName1", mWD_BTN_NoName1);
mWD_IMG_check1.initialiserObjet();
super.ajouter("IMG_check1", mWD_IMG_check1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225928649672l);

super.setChecksum("713821586");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(3);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName14
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName14
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_IMG_NoName1.initialiserObjet();
super.ajouter("IMG_NoName1", mWD_IMG_NoName1);
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225928453064l);

super.setChecksum("713627714");

super.setNom("CELL_NoName14");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(45, 23);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName14 mWD_CELL_NoName14;

/**
 * CELL_NoName15
 */
class GWDCELL_NoName15 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName15
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName15.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225929501718l);

super.setChecksum("714611616");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Pouce droit");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 41);

super.setTailleInitiale(140, 19);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName15.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName2
 */
class GWDBTN_NoName2 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName15.FLEX_NoName1.BTN_NoName2
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225929632790l);

super.setChecksum("714743144");

super.setNom("BTN_NoName2");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(137, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName2 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartFingerCapture(sqr,"RT")
GWDCPSDKInt.StartFingerCapture(vWD_sqr.getString(),"RT");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName2 mWD_BTN_NoName2 = new GWDBTN_NoName2();

/**
 * IMG_check2
 */
class GWDIMG_check2 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName15.FLEX_NoName1.IMG_check2
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225929698326l);

super.setChecksum("714810504");

super.setNom("IMG_check2");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(238, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check2 mWD_IMG_check2 = new GWDIMG_check2();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName15.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName15.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName2.initialiserObjet();
super.ajouter("BTN_NoName2", mWD_BTN_NoName2);
mWD_IMG_check2.initialiserObjet();
super.ajouter("IMG_check2", mWD_IMG_check2);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225929567254l);

super.setChecksum("714739168");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(137, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName15
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName15
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225929436182l);

super.setChecksum("714610832");

super.setNom("CELL_NoName15");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(44, 144);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(2);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName15 mWD_CELL_NoName15;

/**
 * CELL_NoName17
 */
class GWDCELL_NoName17 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°3 de InternalWindow1.InternalWindow1.CELL_NoName17
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName17.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225930288197l);

super.setChecksum("715398095");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Index droit");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 41);

super.setTailleInitiale(143, 19);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName17.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName3
 */
class GWDBTN_NoName3 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName17.FLEX_NoName1.BTN_NoName3
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225930419269l);

super.setChecksum("715529623");

super.setNom("BTN_NoName3");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName3 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// <COMPILE SI TypeConfiguration=Android>
{
super.clicSurBoutonGauche();

// <COMPILE SI TypeConfiguration=Android>

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartFingerCapture(sqr,"RI")
GWDCPSDKInt.StartFingerCapture(vWD_sqr.getString(),"RI");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName3 mWD_BTN_NoName3 = new GWDBTN_NoName3();

/**
 * IMG_check3
 */
class GWDIMG_check3 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName17.FLEX_NoName1.IMG_check3
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225930484805l);

super.setChecksum("715596983");

super.setNom("IMG_check3");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check3 mWD_IMG_check3 = new GWDIMG_check3();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName17.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName17.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName3.initialiserObjet();
super.ajouter("BTN_NoName3", mWD_BTN_NoName3);
mWD_IMG_check3.initialiserObjet();
super.ajouter("IMG_check3", mWD_IMG_check3);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225930353733l);

super.setChecksum("715525647");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName17
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName17
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225930222661l);

super.setChecksum("715397311");

super.setNom("CELL_NoName17");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(45, 265);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(3);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(3);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName17 mWD_CELL_NoName17;

/**
 * CELL_NoName16
 */
class GWDCELL_NoName16 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°4 de InternalWindow1.InternalWindow1.CELL_NoName16
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName16.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931074676l);

super.setChecksum("716184574");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Auriculaire gauche");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 31);

super.setTailleInitiale(104, 38);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName16.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName11
 */
class GWDBTN_NoName11 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName16.FLEX_NoName1.BTN_NoName11
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931205748l);

super.setChecksum("716316102");

super.setNom("BTN_NoName11");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName11 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// <COMPILE SI TypeConfiguration=Android>
{
super.clicSurBoutonGauche();

// <COMPILE SI TypeConfiguration=Android>

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartSingleFingerCapture(sqr,"LL")
GWDCPSDKInt.StartSingleFingerCapture(vWD_sqr.getString(),"LL");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName11 mWD_BTN_NoName11 = new GWDBTN_NoName11();

/**
 * IMG_check11
 */
class GWDIMG_check11 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName16.FLEX_NoName1.IMG_check11
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225931271284l);

super.setChecksum("716383462");

super.setNom("IMG_check11");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(238, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check11 mWD_IMG_check11 = new GWDIMG_check11();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName16.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName16.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName11.initialiserObjet();
super.ajouter("BTN_NoName11", mWD_BTN_NoName11);
mWD_IMG_check11.initialiserObjet();
super.ajouter("IMG_check11", mWD_IMG_check11);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931140212l);

super.setChecksum("716312126");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(131, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName16
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName16
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931009140l);

super.setChecksum("716183790");

super.setNom("CELL_NoName16");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(44, 1231);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(4);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(11);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName16 mWD_CELL_NoName16;

/**
 * CELL_NoName18
 */
class GWDCELL_NoName18 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°5 de InternalWindow1.InternalWindow1.CELL_NoName18
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName18.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931861139l);

super.setChecksum("716971037");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Majeur droit");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 41);

super.setTailleInitiale(119, 19);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName18.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName4
 */
class GWDBTN_NoName4 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName18.FLEX_NoName1.BTN_NoName4
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931992211l);

super.setChecksum("717102565");

super.setNom("BTN_NoName4");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName4 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartSingleFingerCapture(sqr,"RM")
GWDCPSDKInt.StartSingleFingerCapture(vWD_sqr.getString(),"RM");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName4 mWD_BTN_NoName4 = new GWDBTN_NoName4();

/**
 * IMG_check4
 */
class GWDIMG_check4 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName18.FLEX_NoName1.IMG_check4
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225932057747l);

super.setChecksum("717169925");

super.setNom("IMG_check4");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check4 mWD_IMG_check4 = new GWDIMG_check4();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName18.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName18.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName4.initialiserObjet();
super.ajouter("BTN_NoName4", mWD_BTN_NoName4);
mWD_IMG_check4.initialiserObjet();
super.ajouter("IMG_check4", mWD_IMG_check4);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931926675l);

super.setChecksum("717098589");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName18
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName18
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225931795603l);

super.setChecksum("716970253");

super.setNom("CELL_NoName18");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(45, 386);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(5);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(4);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName18 mWD_CELL_NoName18;

/**
 * CELL_NoName19
 */
class GWDCELL_NoName19 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°6 de InternalWindow1.InternalWindow1.CELL_NoName19
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName19.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225932647602l);

super.setChecksum("717757500");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Annulaire droit");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 31);

super.setTailleInitiale(95, 39);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName19.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName5
 */
class GWDBTN_NoName5 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName19.FLEX_NoName1.BTN_NoName5
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225932778674l);

super.setChecksum("717889028");

super.setNom("BTN_NoName5");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName5 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartSingleFingerCapture(sqr,"RR")
GWDCPSDKInt.StartSingleFingerCapture(vWD_sqr.getString(),"RR");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName5 mWD_BTN_NoName5 = new GWDBTN_NoName5();

/**
 * IMG_check5
 */
class GWDIMG_check5 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName19.FLEX_NoName1.IMG_check5
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225932844210l);

super.setChecksum("717956388");

super.setNom("IMG_check5");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check5 mWD_IMG_check5 = new GWDIMG_check5();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName19.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName19.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName5.initialiserObjet();
super.ajouter("BTN_NoName5", mWD_BTN_NoName5);
mWD_IMG_check5.initialiserObjet();
super.ajouter("IMG_check5", mWD_IMG_check5);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225932713138l);

super.setChecksum("717885052");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName19
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName19
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225932582066l);

super.setChecksum("717756716");

super.setNom("CELL_NoName19");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(45, 505);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(6);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(5);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName19 mWD_CELL_NoName19;

/**
 * CELL_NoName20
 */
class GWDCELL_NoName20 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°7 de InternalWindow1.InternalWindow1.CELL_NoName20
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName20.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225933434065l);

super.setChecksum("718543963");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Auriculaire droit");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 31);

super.setTailleInitiale(103, 38);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName20.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName6
 */
class GWDBTN_NoName6 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName20.FLEX_NoName1.BTN_NoName6
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225933565137l);

super.setChecksum("718675491");

super.setNom("BTN_NoName6");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName6 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartSingleFingerCapture(sqr,"RL")
GWDCPSDKInt.StartSingleFingerCapture(vWD_sqr.getString(),"RL");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName6 mWD_BTN_NoName6 = new GWDBTN_NoName6();

/**
 * IMG_check6
 */
class GWDIMG_check6 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName20.FLEX_NoName1.IMG_check6
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225933630673l);

super.setChecksum("718742851");

super.setNom("IMG_check6");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check6 mWD_IMG_check6 = new GWDIMG_check6();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName20.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName20.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName6.initialiserObjet();
super.ajouter("BTN_NoName6", mWD_BTN_NoName6);
mWD_IMG_check6.initialiserObjet();
super.ajouter("IMG_check6", mWD_IMG_check6);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225933499601l);

super.setChecksum("718671515");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName20
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName20
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225933368529l);

super.setChecksum("718543179");

super.setNom("CELL_NoName20");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(45, 626);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(7);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(6);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName20 mWD_CELL_NoName20;

/**
 * CELL_NoName21
 */
class GWDCELL_NoName21 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°8 de InternalWindow1.InternalWindow1.CELL_NoName21
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName21.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225934220529l);

super.setChecksum("719330427");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Pouce gauche");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 41);

super.setTailleInitiale(143, 19);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName21.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName7
 */
class GWDBTN_NoName7 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName21.FLEX_NoName1.BTN_NoName7
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225934351601l);

super.setChecksum("719461955");

super.setNom("BTN_NoName7");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(137, 26);

super.setTailleInitiale(87, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName7 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartFingerCapture(sqr,"LT")
GWDCPSDKInt.StartFingerCapture(vWD_sqr.getString(),"LT");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName7 mWD_BTN_NoName7 = new GWDBTN_NoName7();

/**
 * IMG_check7
 */
class GWDIMG_check7 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName21.FLEX_NoName1.IMG_check7
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225934417137l);

super.setChecksum("719529315");

super.setNom("IMG_check7");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check7 mWD_IMG_check7 = new GWDIMG_check7();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName21.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName21.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName7.initialiserObjet();
super.ajouter("BTN_NoName7", mWD_BTN_NoName7);
mWD_IMG_check7.initialiserObjet();
super.ajouter("IMG_check7", mWD_IMG_check7);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225934286065l);

super.setChecksum("719457979");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(137, 26);

super.setTailleInitiale(129, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName21
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName21
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225934154993l);

super.setChecksum("719329643");

super.setNom("CELL_NoName21");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(44, 747);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(8);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(7);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName21 mWD_CELL_NoName21;

/**
 * CELL_NoName22
 */
class GWDCELL_NoName22 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°9 de InternalWindow1.InternalWindow1.CELL_NoName22
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName22.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225935006992l);

super.setChecksum("720116890");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Index gauche");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 41);

super.setTailleInitiale(143, 19);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName22.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName8
 */
class GWDBTN_NoName8 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName22.FLEX_NoName1.BTN_NoName8
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225935138064l);

super.setChecksum("720248418");

super.setNom("BTN_NoName8");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(86, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName8 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartFingerCapture(sqr,"LI")
GWDCPSDKInt.StartFingerCapture(vWD_sqr.getString(),"LI");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName8 mWD_BTN_NoName8 = new GWDBTN_NoName8();

/**
 * IMG_check8
 */
class GWDIMG_check8 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName22.FLEX_NoName1.IMG_check8
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225935203600l);

super.setChecksum("720315778");

super.setNom("IMG_check8");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check8 mWD_IMG_check8 = new GWDIMG_check8();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName22.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName22.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName8.initialiserObjet();
super.ajouter("BTN_NoName8", mWD_BTN_NoName8);
mWD_IMG_check8.initialiserObjet();
super.ajouter("IMG_check8", mWD_IMG_check8);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225935072528l);

super.setChecksum("720244442");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName22
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName22
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225934941456l);

super.setChecksum("720116106");

super.setNom("CELL_NoName22");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(45, 868);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(9);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(8);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName22 mWD_CELL_NoName22;

/**
 * CELL_NoName23
 */
class GWDCELL_NoName23 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°10 de InternalWindow1.InternalWindow1.CELL_NoName23
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName23.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225935793471l);

super.setChecksum("720903369");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Majeur gauche");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 41);

super.setTailleInitiale(143, 19);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName23.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName9
 */
class GWDBTN_NoName9 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName23.FLEX_NoName1.BTN_NoName9
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225935924543l);

super.setChecksum("721034897");

super.setNom("BTN_NoName9");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(137, 26);

super.setTailleInitiale(87, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName9 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartSingleFingerCapture(sqr,"LM")
GWDCPSDKInt.StartSingleFingerCapture(vWD_sqr.getString(),"LM");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName9 mWD_BTN_NoName9 = new GWDBTN_NoName9();

/**
 * IMG_check9
 */
class GWDIMG_check9 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName23.FLEX_NoName1.IMG_check9
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225935990079l);

super.setChecksum("721102257");

super.setNom("IMG_check9");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check9 mWD_IMG_check9 = new GWDIMG_check9();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName23.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName23.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName9.initialiserObjet();
super.ajouter("BTN_NoName9", mWD_BTN_NoName9);
mWD_IMG_check9.initialiserObjet();
super.ajouter("IMG_check9", mWD_IMG_check9);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225935859007l);

super.setChecksum("721030921");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(137, 26);

super.setTailleInitiale(129, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName23
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName23
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225935727935l);

super.setChecksum("720902585");

super.setNom("CELL_NoName23");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(44, 989);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(10);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(9);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName23 mWD_CELL_NoName23;

/**
 * CELL_NoName24
 */
class GWDCELL_NoName24 extends WDChampCellule
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°11 de InternalWindow1.InternalWindow1.CELL_NoName24
////////////////////////////////////////////////////////////////////////////

/**
 * STC_NoName1
 */
class GWDSTC_NoName1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName24.STC_NoName1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225936579934l);

super.setChecksum("721689832");

super.setNom("STC_NoName1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Annulaire gauche");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 31);

super.setTailleInitiale(96, 38);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSTC_NoName1 mWD_STC_NoName1 = new GWDSTC_NoName1();

/**
 * FLEX_NoName1
 */
class GWDFLEX_NoName1 extends WDChampFlexbox
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName24.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////

/**
 * BTN_NoName10
 */
class GWDBTN_NoName10 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de InternalWindow1.InternalWindow1.CELL_NoName24.FLEX_NoName1.BTN_NoName10
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225936711006l);

super.setChecksum("721821360");

super.setNom("BTN_NoName10");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(87, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("MS Shell Dlg", -11.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffc98100), 5.000000, 5.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_NoName10 ( FLEX_NoName1 )
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	StartSingleFingerCapture(sqr,"LR")
GWDCPSDKInt.StartSingleFingerCapture(vWD_sqr.getString(),"LR");


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_NoName10 mWD_BTN_NoName10 = new GWDBTN_NoName10();

/**
 * IMG_check10
 */
class GWDIMG_check10 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de InternalWindow1.InternalWindow1.CELL_NoName24.FLEX_NoName1.IMG_check10
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );

super.setQuid(2903355225936776542l);

super.setChecksum("721888720");

super.setNom("IMG_check10");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(237, 36);

super.setTailleInitiale(29, 28);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\WON_Basic-Check 1@dpi1_5x.png");

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamMiseEnPageFlexbox(0, 0, 1);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_check10 mWD_IMG_check10 = new GWDIMG_check10();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName24.FLEX_NoName1
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName24.FLEX_NoName1
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_BTN_NoName10.initialiserObjet();
super.ajouter("BTN_NoName10", mWD_BTN_NoName10);
mWD_IMG_check10.initialiserObjet();
super.ajouter("IMG_check10", mWD_IMG_check10);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225936645470l);

super.setChecksum("721817384");

super.setNom("FLEX_NoName1");

super.setType(139);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 26);

super.setTailleInitiale(130, 48);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setTauxParallaxe(0, 0, false);

super.setParamFlexbox(1, 2, 4, 1, 3, 10, 4, true);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDFLEX_NoName1 mWD_FLEX_NoName1 = new GWDFLEX_NoName1();

/**
 * Initialise tous les champs de InternalWindow1.InternalWindow1.CELL_NoName24
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1.InternalWindow1.CELL_NoName24
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_STC_NoName1.initialiserObjet();
super.ajouter("STC_NoName1", mWD_STC_NoName1);
mWD_FLEX_NoName1.initialiserObjet();
super.ajouter("FLEX_NoName1", mWD_FLEX_NoName1);
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetreInterne( getWDFenetreInterneThis() );
super.setQuid(2903355225936514398l);

super.setChecksum("721689048");

super.setNom("CELL_NoName24");

super.setType(50014);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(44, 1110);

super.setTailleInitiale(272, 101);

super.setPlan(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(11);

super.setAncrageInitial(8, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(10);

super.setTauxParallaxe(0, 0, false);

super.setParamCellule(false);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff), 5.000000, 5.000000, 0, 1, 0, WDShadowFactory.creerOmbre_GEN(0, 4, 0x0, 45, 4)), 0, 0, 0, 0);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCELL_NoName24 mWD_CELL_NoName24;

/**
 * Traitement: Global declarations of InternalWindow1
 */
// PROCEDURE MyWindow()
public void declarerGlobale(WDObjet[] WD_tabParam)
{
// PROCEDURE MyWindow()
super.declarerGlobale(WD_tabParam, 0, 0);
int WD_ntabParamLen = 0;
if(WD_tabParam!=null) WD_ntabParamLen = WD_tabParam.length;





try
{
// sqr						est une chaîne	= Today()+Left(Now(), 4)+Random(9999)
vWD_sqr = new WDChaineU();

vWD_sqr.setValeur(WDAPIDate.dateDuJour().opPlus(WDAPIChaine.gauche(WDAPIDate.maintenant(),4)).opPlus(WDAPIDiversSTD.hasard(9999)));

super.ajouterVariableGlobale("sqr",vWD_sqr);



}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
 public WDObjet vWD_sqr = WDVarNonAllouee.ref;
////////////////////////////////////////////////////////////////////////////
// Création des champs de la fenêtre InternalWindow1
////////////////////////////////////////////////////////////////////////////
protected void creerChamps()
{
mWD_CELL_NoName14 = new GWDCELL_NoName14();
mWD_CELL_NoName15 = new GWDCELL_NoName15();
mWD_CELL_NoName17 = new GWDCELL_NoName17();
mWD_CELL_NoName16 = new GWDCELL_NoName16();
mWD_CELL_NoName18 = new GWDCELL_NoName18();
mWD_CELL_NoName19 = new GWDCELL_NoName19();
mWD_CELL_NoName20 = new GWDCELL_NoName20();
mWD_CELL_NoName21 = new GWDCELL_NoName21();
mWD_CELL_NoName22 = new GWDCELL_NoName22();
mWD_CELL_NoName23 = new GWDCELL_NoName23();
mWD_CELL_NoName24 = new GWDCELL_NoName24();

}
////////////////////////////////////////////////////////////////////////////
// Initialisation de la fenêtre InternalWindow1
////////////////////////////////////////////////////////////////////////////
public  void initialiserObjet()
{
super.initialiserObjet();
super.setQuid(2903355114257705441l);

super.setChecksum("711978233");

super.setNom("InternalWindow1");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setTailleInitiale(360, 1365);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setCouleurFond(getCouleur_GEN(0xffffffff, true));


activerEcoute();

////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de InternalWindow1
////////////////////////////////////////////////////////////////////////////
mWD_CELL_NoName14.initialiserObjet();
super.ajouter("CELL_NoName14", mWD_CELL_NoName14);
mWD_CELL_NoName15.initialiserObjet();
super.ajouter("CELL_NoName15", mWD_CELL_NoName15);
mWD_CELL_NoName17.initialiserObjet();
super.ajouter("CELL_NoName17", mWD_CELL_NoName17);
mWD_CELL_NoName16.initialiserObjet();
super.ajouter("CELL_NoName16", mWD_CELL_NoName16);
mWD_CELL_NoName18.initialiserObjet();
super.ajouter("CELL_NoName18", mWD_CELL_NoName18);
mWD_CELL_NoName19.initialiserObjet();
super.ajouter("CELL_NoName19", mWD_CELL_NoName19);
mWD_CELL_NoName20.initialiserObjet();
super.ajouter("CELL_NoName20", mWD_CELL_NoName20);
mWD_CELL_NoName21.initialiserObjet();
super.ajouter("CELL_NoName21", mWD_CELL_NoName21);
mWD_CELL_NoName22.initialiserObjet();
super.ajouter("CELL_NoName22", mWD_CELL_NoName22);
mWD_CELL_NoName23.initialiserObjet();
super.ajouter("CELL_NoName23", mWD_CELL_NoName23);
mWD_CELL_NoName24.initialiserObjet();
super.ajouter("CELL_NoName24", mWD_CELL_NoName24);

super.terminerInitialisation();
}

////////////////////////////////////////////////////////////////////////////
public boolean isUniteAffichageLogique()
{
return false;
}

public WDProjet getProjet()
{
return GWDPintegrationSdk.getInstance();
}

public IWDEnsembleElement getEnsemble()
{
return GWDPintegrationSdk.getInstance();
}
public int getModeContexteHF()
{
return 1;
}
}
