<?xml version="1.0" encoding="UTF-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools" package="com.amlacameroon.sandbox" android:versionName="0.0.354.0" android:versionCode="354" android:installLocation="auto"><application android:name="fr.pcsoft.wdjava.core.application.WDAndroidApp" android:label="@string/app_name" android:icon="@drawable/i_c_o_n_e________0" android:largeHeap="true"  android:resizeableActivity="true" android:usesCleartextTraffic="true" android:requestLegacyExternalStorage="true" android:extractNativeLibs="true"><activity android:name=".wdgen.GWDPintegrationSdk$WDLanceur" android:label="@string/app_name" android:theme="@style/Theme.AppCompat.Light.NoActionBar.Translucent" android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout" android:exported="true"><intent-filter><action android:name="android.intent.action.MAIN"/><category android:name="android.intent.category.LAUNCHER"/></intent-filter></activity><activity android:name=".wdgen.GWDFFEN_Mobile_Camera_UI$WDActiviteFenetre" android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode" android:theme="@android:style/Theme" android:screenOrientation="unspecified" android:hardwareAccelerated="false"/><activity android:name=".wdgen.GWDFrun$WDActiviteFenetre" android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode" android:theme="@android:style/Theme" android:screenOrientation="unspecified" android:hardwareAccelerated="false"/><activity android:name=".wdgen.GWDFtt$WDActiviteFenetre" android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode" android:theme="@android:style/Theme" android:screenOrientation="unspecified" android:hardwareAccelerated="false"/><uses-library android:name="org.apache.http.legacy" android:required="false"/><activity android:name="fr.pcsoft.wdjava.core.erreur.report.WDErrorReportActivity" android:theme="@android:style/Theme.DeviceDefault.Light" tools:node="replace"/><provider android:name="androidx.core.content.FileProvider" android:authorities="com.amlacameroon.sandbox.fileprovider" android:exported="false" android:grantUriPermissions="true" tools:node="replace"><meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/provider_paths"/></provider><receiver android:name="fr.pcsoft.wdjava.core.utils.WDAppUtils$APKInstallBroadcastReceiver" android:exported="false" tools:node="replace"/><activity android:name="fr.pcsoft.wdjava.ui.activite.WDActivite$BlankActivity" android:theme="@style/Theme.AppCompat.Light.NoActionBar.Translucent" tools:node="replace"/><provider android:name="fr.pcsoft.wdjava.ui.searchbar.WDSearchHistory" android:exported="false" android:authorities="com.amlacameroon.sandbox" tools:node="replace"/><activity android:name="fr.pcsoft.wdjava.ui.searchbar.WDSearchActivity" android:label="@string/app_name" tools:node="replace" android:exported="false"><intent-filter><action android:name="android.intent.action.SEARCH"/></intent-filter><meta-data android:name="android.app.searchable" android:resource="@xml/searchable"/></activity><service android:name="fr.pcsoft.wdjava.core.service.WDServiceLocal"/><meta-data android:name="fr.pcsoft.first_window_name" android:value="run"/><meta-data android:name="fr.pcsoft.splash_orientation_phone" android:value="4"/><meta-data android:name="fr.pcsoft.splash_orientation_tablet" android:value="4"/></application><supports-screens android:smallScreens="true" android:normalScreens="true" android:largeScreens="true" android:xlargeScreens="true" android:anyDensity="true"/><uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/><uses-permission android:name="android.permission.CAMERA"/><uses-permission android:name="android.permission.RECORD_AUDIO"/><uses-permission android:name="android.permission.INTERNET"/><uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/><uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/><uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/><uses-permission android:name="android.permission.VIBRATE"/><uses-feature android:name="android.hardware.camera.autofocus" android:required="true"/><uses-feature android:name="android.hardware.camera" android:required="true"/><uses-feature android:name="android.hardware.microphone" android:required="true"/></manifest>
