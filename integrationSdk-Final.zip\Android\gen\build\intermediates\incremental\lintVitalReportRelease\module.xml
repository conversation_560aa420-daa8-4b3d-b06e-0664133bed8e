<lint-module
    format="1"
    dir="C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen"
    name=":"
    type="APP"
    maven=":gen:"
    gradle="7.4.2"
    buildFolder="build"
    bootClassPath="C:\PC SOFT\WINDEV Mobile 2025\Personal\Android\AndroidSDK\platforms\android-34\android.jar;C:\PC SOFT\WINDEV Mobile 2025\Personal\Android\AndroidSDK\platforms\android-34\optional\org.apache.http.legacy.jar;C:\PC SOFT\WINDEV Mobile 2025\Personal\Android\AndroidSDK\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
