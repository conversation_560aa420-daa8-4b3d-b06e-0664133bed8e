-- CryptoID Mobile Application Database Schema
-- For WinDev Mobile with <PERSON><PERSON><PERSON><PERSON><PERSON>

-- Admin Users Table
CREATE TABLE AdminUsers (
    AdminID INTEGER PRIMARY KEY AUTOINCREMENT,
    Username VARCHAR(50) UNIQUE NOT NULL,
    Password VARCHAR(255) NOT NULL,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    LastLogin DATETIME,
    IsActive BOOLEAN DEFAULT 1
);

-- ID Categories Table
CREATE TABLE IDCategories (
    CategoryID INTEGER PRIMARY KEY AUTOINCREMENT,
    CategoryName VARCHAR(50) NOT NULL,
    CategoryDescription TEXT,
    IsActive BOOLEAN DEFAULT 1
);

-- Person Records Table
CREATE TABLE PersonRecords (
    PersonID INTEGER PRIMARY KEY AUTOINCREMENT,
    CategoryID INTEGER NOT NULL,
    FirstName VARCHAR(100) NOT NULL,
    LastName VARCHAR(100) NOT NULL,
    Email VARCHAR(150),
    Phone VARCHAR(20),
    DateOfBirth DATE,
    Address TEXT,
    PhotoPath VARCHAR(500),
    
    -- School ID specific fields
    StudentID VARCHAR(50),
    School VARCHAR(200),
    Grade VARCHAR(20),
    AcademicYear VARCHAR(20),
    
    -- Professional ID specific fields
    EmployeeID VARCHAR(50),
    Company VARCHAR(200),
    Department VARCHAR(100),
    Position VARCHAR(100),
    
    -- Business Card specific fields
    BusinessName VARCHAR(200),
    BusinessType VARCHAR(100),
    Website VARCHAR(200),
    
    -- Metadata
    CreatedBy INTEGER,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    IsActive BOOLEAN DEFAULT 1,
    
    FOREIGN KEY (CategoryID) REFERENCES IDCategories(CategoryID),
    FOREIGN KEY (CreatedBy) REFERENCES AdminUsers(AdminID)
);

-- Cryptographs Table
CREATE TABLE Cryptographs (
    CryptographID INTEGER PRIMARY KEY AUTOINCREMENT,
    PersonID INTEGER NOT NULL,
    CryptographData TEXT NOT NULL,
    CryptographHash VARCHAR(255) UNIQUE NOT NULL,
    GeneratedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    ExpiryDate DATE,
    IsActive BOOLEAN DEFAULT 1,
    
    FOREIGN KEY (PersonID) REFERENCES PersonRecords(PersonID)
);

-- Scan History Table (Optional - for tracking scans)
CREATE TABLE ScanHistory (
    ScanID INTEGER PRIMARY KEY AUTOINCREMENT,
    CryptographID INTEGER,
    ScanDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    ScanMethod VARCHAR(20), -- 'CAMERA' or 'UPLOAD'
    DeviceInfo TEXT,
    
    FOREIGN KEY (CryptographID) REFERENCES Cryptographs(CryptographID)
);

-- Insert default ID categories
INSERT INTO IDCategories (CategoryName, CategoryDescription) VALUES 
('School ID', 'Student identification cards'),
('Professional ID', 'Employee identification cards'),
('Business Card ID', 'Business card information');

-- Insert default admin user (password should be hashed in real implementation)
INSERT INTO AdminUsers (Username, Password) VALUES 
('admin', 'admin123'); -- This should be properly hashed in production
