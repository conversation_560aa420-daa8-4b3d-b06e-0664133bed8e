# NE PAS MODIFIER
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*,!code/allocation/variable,!class/unboxing/enum
-optimizationpasses 5
-dontpreverify
-dontshrink
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontwarn android.support.**
-ignorewarnings

-keepattributes InnerClasses,*Annotation*
-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keepclasseswithmembernames class * {
    native <methods>;
}
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}
-keep class * extends android.app.Activity {
   public void *(android.view.View);
}
-keep class * extends android.app.Service 
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
-keepclassmembers class **.R$* {
    public static <fields>;
}
-keepclasseswithmembernames class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keepclasseswithmembernames class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
# Webview
-keepclassmembers class fqcn.of.javascript.interface.for.webview { 
   public *;
}
# Google Maps v1
-dontwarn com.google.android.maps.**
# Google Play Service
-keep class * extends java.util.ListResourceBundle {
    protected java.lang.Object[][] getContents();
}
-keep public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
    @com.google.android.gms.common.annotation.KeepName *;
}
-dontwarn com.google.android.gms.common.GooglePlayServicesUtil
# KSoap
-dontwarn org.xmlpull.v1.**
#BuildConfig 
-keep class **.BuildConfig { *; }
#QW244168
-keep class org.xmlpull.** { *; }
-keep class fr.pcsoft.wdjava.ui.searchbar.WDSearchHistory { *; }
#TB93245
-keep class fr.pcsoft.wdjava.database.hf.jni.WDHFUtilsJNI { *; }
#TB#102876
-keep class fr.pcsoft.wdjava.jni.WDJNIExceptionErrWL { *; }
#TB93474
-keep class fr.pcsoft.wdjava.net.http.WDHTTPClientRPL { *; }
# Code genere
-keep class **.GWD* {
	... vWD_*;
	... mWD_*;
	... fWD_*(...);
}
#TB93368
-keep class **.WDSplashScreenGen { *; }
#QW293244
-keep class fr.pcsoft.wdjava.ui.champs.zr.WDAbstractZRRenderer$AbstractRepetitionView
{
    boolean hasExplicitFocusable();
}
#SDK Facebook
-keep class com.facebook.** { *; }
-keepattributes Signature
# Framework
-dontwarn fr.pcsoft.wdjava.j.a

#Google Sign-in
-if class androidx.credentials.CredentialManager
-keep class androidx.credentials.playservices.** {
  *;
}

# Règles générées par le processeur d'annotation


