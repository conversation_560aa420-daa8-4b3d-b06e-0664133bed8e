// CryptoID Mobile Application - Scan Options Window
// WLanguage code for WinDev Mobile

// Scan options window initialization
PROCEDURE WIN_ScanOptions_Initialize()
	// Set window title
	WIN_ScanOptions..Title = "Scan Cryptograph"
	
	// Configure scan option buttons
	BTN_ScanWithCamera..Caption = "Scan with Camera"
	BTN_UploadPhoto..Caption = "Upload Photo"
	BTN_ScanWithCamera..Image = "camera_icon.png"
	BTN_UploadPhoto..Image = "upload_icon.png"
	
	// Add instructions
	STC_Instructions..Caption = "Choose how you want to scan the cryptograph:" + CR + CR +
		"• Scan with Camera: Use your device camera to scan a cryptograph in real-time" + CR +
		"• Upload Photo: Select a photo of a cryptograph from your device"
END

// Scan with camera button click
PROCEDURE BTN_ScanWithCamera_Click()
	// Open camera scanner window
	Open(WIN_CameraScanner)
END

// Upload photo button click
PROCEDURE BTN_UploadPhoto_Click()
	LOCAL sSelectedFile AS STRING
	
	// Open file picker for image selection
	sSelectedFile = fSelect("", "", "Select Cryptograph Photo", "Image files" + TAB + "*.jpg;*.jpeg;*.png;*.bmp", "jpg", fselOpen)
	
	IF sSelectedFile <> "" THEN
		// Process the selected image
		ProcessCryptographImage(sSelectedFile, "UPLOAD")
	END
END

// Process cryptograph image (from camera or upload)
PROCEDURE ProcessCryptographImage(sImagePath AS STRING, sScanMethod AS STRING)
	LOCAL sCryptographData AS STRING
	LOCAL bScanResult AS BOOLEAN
	
	// Show processing message
	Info("Processing cryptograph... Please wait.")
	
	// Scan QR code from image
	sCryptographData = ScanQRCodeFromImage(sImagePath)
	
	IF sCryptographData <> "" THEN
		// Validate cryptograph data
		IF ValidateCryptographData(sCryptographData) THEN
			// Log scan activity
			LogScanActivity(sCryptographData, sScanMethod)
			
			// Display cryptograph information
			DisplayCryptographInfo(sCryptographData)
		ELSE
			Error("Invalid cryptograph data. The QR code may be corrupted or not a valid CryptoID.")
		END
	ELSE
		Error("No valid QR code found in the image. Please ensure the cryptograph is clearly visible and try again.")
	END
END

// Scan QR code from image file
PROCEDURE ScanQRCodeFromImage(sImagePath AS STRING) : STRING
	LOCAL sCryptographData AS STRING = ""
	LOCAL imgSource AS Image
	
	// Load the image
	imgSource = ImageLoad(sImagePath)
	
	IF imgSource..Width > 0 THEN
		// Scan QR code from the image
		// Note: This requires QR code scanning library in WinDev Mobile
		sCryptographData = QRCodeRead(imgSource)
	END
	
	RESULT sCryptographData
END

// Validate cryptograph data format
PROCEDURE ValidateCryptographData(sCryptographData AS STRING) : BOOLEAN
	LOCAL bValid AS BOOLEAN = False
	LOCAL stData AS VARIANT
	
	// Try to parse JSON data
	stData = JSONToVariant(sCryptographData)
	
	// Check if required fields exist
	IF VariantType(stData) = wlVariantObject THEN
		IF stData.PersonID <> Null AND stData.FirstName <> Null AND stData.LastName <> Null THEN
			bValid = True
		END
	END
	
	RESULT bValid
END

// Log scan activity
PROCEDURE LogScanActivity(sCryptographData AS STRING, sScanMethod AS STRING)
	LOCAL stData AS VARIANT
	LOCAL sQuery AS STRING
	LOCAL nCryptographID AS INT
	
	// Parse cryptograph data
	stData = JSONToVariant(sCryptographData)
	
	// Find cryptograph ID from hash (if exists in database)
	nCryptographID = FindCryptographID(sCryptographData)
	
	// Log the scan
	sQuery = StringBuild("INSERT INTO ScanHistory (CryptographID, ScanMethod, DeviceInfo) VALUES (%1, '%2', '%3')", 
		nCryptographID, sScanMethod, GetDeviceInfo())
	
	HExecuteQuery("QRY_LogScan", sQuery)
END

// Find cryptograph ID from database
PROCEDURE FindCryptographID(sCryptographData AS STRING) : INT
	LOCAL nCryptographID AS INT = 0
	LOCAL sHash AS STRING
	LOCAL sQuery AS STRING
	
	// Generate hash from data
	sHash = HashString(HA_SHA256, sCryptographData)
	
	// Search for cryptograph with matching hash
	sQuery = StringBuild("SELECT CryptographID FROM Cryptographs WHERE CryptographHash = '%1' AND IsActive = 1", sHash)
	
	IF HExecuteQuery("QRY_FindCryptograph", sQuery) THEN
		IF HReadFirst("QRY_FindCryptograph") THEN
			nCryptographID = QRY_FindCryptograph.CryptographID
		END
	END
	
	RESULT nCryptographID
END

// Get device information for logging
PROCEDURE GetDeviceInfo() : STRING
	LOCAL sDeviceInfo AS STRING
	
	sDeviceInfo = "Model: " + SysEnvironment(seDeviceModel) + ", "
	sDeviceInfo += "OS: " + SysEnvironment(seSystemVersion) + ", "
	sDeviceInfo += "App: CryptoID Mobile"
	
	RESULT sDeviceInfo
END

// Display cryptograph information
PROCEDURE DisplayCryptographInfo(sCryptographData AS STRING)
	// Store cryptograph data globally for the info window
	gsScannedCryptographData = sCryptographData
	
	// Open cryptograph info display window
	Open(WIN_CryptographInfo)
END

// Back button click
PROCEDURE BTN_Back_Click()
	Close()
END

// Global variable for scanned cryptograph data
GLOBAL
	gsScannedCryptographData AS STRING = ""
END
