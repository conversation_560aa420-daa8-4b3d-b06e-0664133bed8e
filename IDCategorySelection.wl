// CryptoID Mobile Application - ID Category Selection Window
// WLanguage code for WinDev Mobile

// Category selection window initialization
PROCEDURE WIN_IDCategorySelection_Initialize()
	// Set window title
	WIN_IDCategorySelection..Title = "Select ID Type"
	
	// Configure category buttons
	BTN_SchoolID..Caption = "School ID"
	BTN_ProfessionalID..Caption = "Professional ID"
	BTN_BusinessCardID..Caption = "Business Card ID"
	
	// Set button images if available
	BTN_SchoolID..Image = "school_icon.png"
	BTN_ProfessionalID..Image = "professional_icon.png"
	BTN_BusinessCardID..Image = "business_icon.png"
	
	// Display current admin info
	STC_AdminInfo..Caption = "Logged in as: " + gsCurrentAdmin
END

// School ID button click
PROCEDURE BTN_SchoolID_Click()
	OpenDataEntryForm(1, "School ID")
END

// Professional ID button click
PROCEDURE BTN_ProfessionalID_Click()
	OpenDataEntryForm(2, "Professional ID")
END

// Business Card ID button click
PROCEDURE BTN_BusinessCardID_Click()
	OpenDataEntryForm(3, "Business Card ID")
END

// Open data entry form for selected category
PROCEDURE OpenDataEntryForm(nCategoryID AS INT, sCategoryName AS STRING)
	// Store selected category in global variables
	gnSelectedCategoryID = nCategoryID
	gsSelectedCategoryName = sCategoryName
	
	// Open the data entry window
	Open(WIN_DataEntry)
END

// Logout button click
PROCEDURE BTN_Logout_Click()
	LOCAL nResponse AS INT
	
	nResponse = YesNo("Are you sure you want to logout?")
	IF nResponse = Yes THEN
		AdminLogout()
		Close()
	END
END

// Back button click
PROCEDURE BTN_Back_Click()
	Close()
END

// Global variables for category selection
GLOBAL
	gnSelectedCategoryID AS INT = 0
	gsSelectedCategoryName AS STRING = ""
END
