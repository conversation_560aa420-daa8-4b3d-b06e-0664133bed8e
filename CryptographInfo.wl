// CryptoID Mobile Application - Cryptograph Information Display Window
// WLanguage code for WinDev Mobile

// Cryptograph info window initialization
PROCEDURE WIN_CryptographInfo_Initialize()
	// Set window title
	WIN_CryptographInfo..Title = "Cryptograph Information"
	
	// Parse and display cryptograph data
	DisplayScannedInformation()
END

// Display scanned cryptograph information
PROCEDURE DisplayScannedInformation()
	LOCAL stPersonData AS VARIANT
	LOCAL sPersonInfo AS STRING
	LOCAL sPhotoPath AS STRING
	
	// Parse JSON data
	stPersonData = JSONToVariant(gsScannedCryptographData)
	
	IF VariantType(stPersonData) = wlVariantObject THEN
		// Display person photo if available
		sPhotoPath = stPersonData.PhotoPath
		IF sPhotoPath <> "" AND fFileExist(fDataDir() + "\" + sPhotoPath) THEN
			IMG_PersonPhoto..Image = fDataDir() + "\" + sPhotoPath
		ELSE
			IMG_PersonPhoto..Image = "default_photo.png"
		END
		
		// Build person information display
		sPersonInfo = BuildPersonInfoDisplay(stPersonData)
		
		// Display information in text control
		STC_PersonInfo..Caption = sPersonInfo
		
		// Set verification status
		SetVerificationStatus(stPersonData)
		
		// Display additional metadata
		DisplayMetadata(stPersonData)
	ELSE
		Error("Failed to parse cryptograph data")
		Close()
	END
END

// Build person information display string
PROCEDURE BuildPersonInfoDisplay(stPersonData AS VARIANT) : STRING
	LOCAL sInfo AS STRING
	
	// Basic information
	sInfo = "PERSONAL INFORMATION" + CR + CR
	sInfo += "Name: " + stPersonData.FirstName + " " + stPersonData.LastName + CR
	
	IF stPersonData.Email <> "" AND stPersonData.Email <> Null THEN
		sInfo += "Email: " + stPersonData.Email + CR
	END
	
	IF stPersonData.Phone <> "" AND stPersonData.Phone <> Null THEN
		sInfo += "Phone: " + stPersonData.Phone + CR
	END
	
	IF stPersonData.DateOfBirth <> "" AND stPersonData.DateOfBirth <> Null THEN
		sInfo += "Date of Birth: " + stPersonData.DateOfBirth + CR
	END
	
	IF stPersonData.Address <> "" AND stPersonData.Address <> Null THEN
		sInfo += "Address: " + stPersonData.Address + CR
	END
	
	// Category-specific information
	sInfo += CR + GetCategorySpecificInfo(stPersonData)
	
	RESULT sInfo
END

// Get category-specific information
PROCEDURE GetCategorySpecificInfo(stPersonData AS VARIANT) : STRING
	LOCAL sCategoryInfo AS STRING
	
	SWITCH stPersonData.CategoryID
		CASE 1: // School ID
			sCategoryInfo = "SCHOOL INFORMATION" + CR + CR
			sCategoryInfo += "Student ID: " + stPersonData.StudentID + CR
			sCategoryInfo += "School: " + stPersonData.School + CR
			sCategoryInfo += "Grade: " + stPersonData.Grade + CR
			sCategoryInfo += "Academic Year: " + stPersonData.AcademicYear + CR
			
		CASE 2: // Professional ID
			sCategoryInfo = "PROFESSIONAL INFORMATION" + CR + CR
			sCategoryInfo += "Employee ID: " + stPersonData.EmployeeID + CR
			sCategoryInfo += "Company: " + stPersonData.Company + CR
			sCategoryInfo += "Department: " + stPersonData.Department + CR
			sCategoryInfo += "Position: " + stPersonData.Position + CR
			
		CASE 3: // Business Card ID
			sCategoryInfo = "BUSINESS INFORMATION" + CR + CR
			sCategoryInfo += "Business Name: " + stPersonData.BusinessName + CR
			sCategoryInfo += "Business Type: " + stPersonData.BusinessType + CR
			IF stPersonData.Website <> "" AND stPersonData.Website <> Null THEN
				sCategoryInfo += "Website: " + stPersonData.Website + CR
			END
			
		OTHER CASE
			sCategoryInfo = "UNKNOWN CATEGORY" + CR
	END
	
	RESULT sCategoryInfo
END

// Set verification status
PROCEDURE SetVerificationStatus(stPersonData AS VARIANT)
	LOCAL bIsVerified AS BOOLEAN
	LOCAL sStatusText AS STRING
	LOCAL nStatusColor AS INT
	
	// Check if cryptograph exists in database (verification)
	bIsVerified = VerifyCryptographInDatabase(gsScannedCryptographData)
	
	IF bIsVerified THEN
		sStatusText = "✓ VERIFIED"
		nStatusColor = Green
		STC_VerificationDetails..Caption = "This cryptograph is verified and exists in our database."
	ELSE
		sStatusText = "⚠ UNVERIFIED"
		nStatusColor = Orange
		STC_VerificationDetails..Caption = "This cryptograph could not be verified in our database. It may be from an external source."
	END
	
	STC_VerificationStatus..Caption = sStatusText
	STC_VerificationStatus..Color = nStatusColor
END

// Verify cryptograph in database
PROCEDURE VerifyCryptographInDatabase(sCryptographData AS STRING) : BOOLEAN
	LOCAL bExists AS BOOLEAN = False
	LOCAL sHash AS STRING
	LOCAL sQuery AS STRING
	
	// Generate hash from data
	sHash = HashString(HA_SHA256, sCryptographData)
	
	// Check if cryptograph exists in database
	sQuery = StringBuild("SELECT COUNT(*) AS RecordCount FROM Cryptographs WHERE CryptographHash = '%1' AND IsActive = 1", sHash)
	
	IF HExecuteQuery("QRY_VerifyCryptograph", sQuery) THEN
		IF HReadFirst("QRY_VerifyCryptograph") THEN
			bExists = (QRY_VerifyCryptograph.RecordCount > 0)
		END
	END
	
	RESULT bExists
END

// Display metadata information
PROCEDURE DisplayMetadata(stPersonData AS VARIANT)
	LOCAL sMetadata AS STRING
	
	sMetadata = "CRYPTOGRAPH METADATA" + CR + CR
	
	IF stPersonData.GeneratedDate <> "" AND stPersonData.GeneratedDate <> Null THEN
		sMetadata += "Generated: " + stPersonData.GeneratedDate + CR
	END
	
	sMetadata += "Person ID: " + stPersonData.PersonID + CR
	sMetadata += "Category ID: " + stPersonData.CategoryID + CR
	
	STC_Metadata..Caption = sMetadata
END

// Save information button click
PROCEDURE BTN_SaveInfo_Click()
	LOCAL sFileName AS STRING
	LOCAL sFilePath AS STRING
	LOCAL sInfoText AS STRING
	LOCAL bResult AS BOOLEAN
	
	// Build complete information text
	sInfoText = "CRYPTOGRAPH INFORMATION" + CR + CR
	sInfoText += STC_PersonInfo..Caption + CR + CR
	sInfoText += STC_Metadata..Caption + CR + CR
	sInfoText += "Verification Status: " + STC_VerificationStatus..Caption + CR
	sInfoText += STC_VerificationDetails..Caption + CR + CR
	sInfoText += "Scanned on: " + DateTimeToString(DateTimeSys(), "YYYY-MM-DD HH:MM:SS")
	
	// Generate filename
	sFileName = "CryptoID_Info_" + DateTimeToString(DateTimeSys(), "YYYYMMDD_HHMMSS") + ".txt"
	sFilePath = SysDirExternalStorage(sseAppDocuments) + "\" + sFileName
	
	// Save to file
	bResult = fSaveText(sFilePath, sInfoText)
	
	IF bResult THEN
		Info("Information saved to: " + sFilePath)
	ELSE
		Error("Failed to save information")
	END
END

// Share information button click
PROCEDURE BTN_ShareInfo_Click()
	LOCAL stShareInfo AS STShareInfo
	LOCAL sShareText AS STRING
	
	// Build share text
	sShareText = "CryptoID Information:" + CR + CR
	sShareText += STC_PersonInfo..Caption + CR + CR
	sShareText += "Verification: " + STC_VerificationStatus..Caption + CR
	sShareText += "Scanned: " + DateTimeToString(DateTimeSys(), "YYYY-MM-DD HH:MM:SS")
	
	// Configure share
	stShareInfo.Title = "Share CryptoID Information"
	stShareInfo.Text = sShareText
	stShareInfo.MimeType = "text/plain"
	
	// Share the information
	IF AndroidShare(stShareInfo) THEN
		Info("Information shared successfully!")
	ELSE
		Error("Failed to share information")
	END
END

// Scan another button click
PROCEDURE BTN_ScanAnother_Click()
	// Close current window and return to scan options
	Close()
	Open(WIN_ScanOptions)
END

// Back button click
PROCEDURE BTN_Back_Click()
	Close()
END
