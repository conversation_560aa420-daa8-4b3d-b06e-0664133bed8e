// CryptoID Mobile Application - Configuration and Constants
// WLanguage code for WinDev Mobile

// Application constants
CONSTANT
	APP_NAME = "CryptoID Mobile"
	APP_VERSION = "1.0.0"
	DATABASE_NAME = "CryptoID.db"
	DEFAULT_PHOTO = "default_photo.png"
	
	// ID Categories
	CATEGORY_SCHOOL = 1
	CATEGORY_PROFESSIONAL = 2
	CATEGORY_BUSINESS = 3
	
	// Scan methods
	SCAN_METHOD_CAMERA = "CAMERA"
	SCAN_METHOD_UPLOAD = "UPLOAD"
	
	// File extensions
	SUPPORTED_IMAGE_FORMATS = "*.jpg;*.jpeg;*.png;*.bmp"
	
	// QR Code settings
	QR_CODE_SIZE = 200
	QR_CODE_ERROR_CORRECTION = qrCodeECC_M
	
	// Image settings
	CRYPTOGRAPH_WIDTH = 800
	CRYPTOGRAPH_HEIGHT = 600
	PERSON_PHOTO_WIDTH = 200
	PERSON_PHOTO_HEIGHT = 250
END

// Global application settings
GLOBAL
	gsAppDataPath AS STRING = ""
	gsPhotosPath AS STRING = ""
	gsCryptographsPath AS STRING = ""
	gbDebugMode AS BOOLEAN = False
END

// Initialize application configuration
PROCEDURE InitializeAppConfig()
	// Set application data paths
	gsAppDataPath = fDataDir()
	gsPhotosPath = gsAppDataPath + "\Photos"
	gsCryptographsPath = gsAppDataPath + "\Cryptographs"
	
	// Create directories if they don't exist
	CreateAppDirectories()
	
	// Set debug mode based on compilation
	gbDebugMode = InTestMode()
	
	// Log application startup
	LogAppEvent("Application started", "INFO")
END

// Create necessary application directories
PROCEDURE CreateAppDirectories()
	// Create photos directory
	IF NOT fDirectoryExist(gsPhotosPath) THEN
		fMakeDir(gsPhotosPath)
	END
	
	// Create cryptographs directory
	IF NOT fDirectoryExist(gsCryptographsPath) THEN
		fMakeDir(gsCryptographsPath)
	END
	
	// Create logs directory if debug mode
	IF gbDebugMode THEN
		LOCAL sLogsPath AS STRING = gsAppDataPath + "\Logs"
		IF NOT fDirectoryExist(sLogsPath) THEN
			fMakeDir(sLogsPath)
		END
	END
END

// Get category name by ID
PROCEDURE GetCategoryName(nCategoryID AS INT) : STRING
	LOCAL sCategoryName AS STRING
	
	SWITCH nCategoryID
		CASE CATEGORY_SCHOOL
			sCategoryName = "School ID"
		CASE CATEGORY_PROFESSIONAL
			sCategoryName = "Professional ID"
		CASE CATEGORY_BUSINESS
			sCategoryName = "Business Card ID"
		OTHER CASE
			sCategoryName = "Unknown Category"
	END
	
	RESULT sCategoryName
END

// Validate email format
PROCEDURE ValidateEmail(sEmail AS STRING) : BOOLEAN
	LOCAL bValid AS BOOLEAN = False
	
	IF sEmail <> "" THEN
		// Simple email validation using regular expression
		bValid = MatchRegularExpression(sEmail, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
	END
	
	RESULT bValid
END

// Validate phone number format
PROCEDURE ValidatePhone(sPhone AS STRING) : BOOLEAN
	LOCAL bValid AS BOOLEAN = False
	
	IF sPhone <> "" THEN
		// Remove spaces and special characters for validation
		LOCAL sCleanPhone AS STRING = Replace(Replace(Replace(sPhone, " ", ""), "-", ""), "(", "")
		sCleanPhone = Replace(Replace(sCleanPhone, ")", ""), "+", "")
		
		// Check if remaining characters are digits and length is reasonable
		bValid = (Length(sCleanPhone) >= 10 AND Length(sCleanPhone) <= 15 AND IsNumeric(sCleanPhone))
	END
	
	RESULT bValid
END

// Format file size for display
PROCEDURE FormatFileSize(nSizeBytes AS INT) : STRING
	LOCAL sFormattedSize AS STRING
	
	IF nSizeBytes < 1024 THEN
		sFormattedSize = nSizeBytes + " bytes"
	ELSE IF nSizeBytes < 1048576 THEN // 1024 * 1024
		sFormattedSize = Round(nSizeBytes / 1024, 1) + " KB"
	ELSE
		sFormattedSize = Round(nSizeBytes / 1048576, 1) + " MB"
	END
	
	RESULT sFormattedSize
END

// Log application events (for debugging)
PROCEDURE LogAppEvent(sMessage AS STRING, sLevel AS STRING = "INFO")
	IF gbDebugMode THEN
		LOCAL sLogFile AS STRING = gsAppDataPath + "\Logs\app_" + DateToString(DateSys(), "YYYYMMDD") + ".log"
		LOCAL sLogEntry AS STRING = DateTimeToString(DateTimeSys(), "YYYY-MM-DD HH:MM:SS") + " [" + sLevel + "] " + sMessage + CR
		
		fSaveText(sLogFile, sLogEntry, foAdd)
	END
END

// Get application information
PROCEDURE GetAppInfo() : STRING
	LOCAL sAppInfo AS STRING
	
	sAppInfo = APP_NAME + " v" + APP_VERSION + CR
	sAppInfo += "Build Date: " + CompilationDate() + CR
	sAppInfo += "Platform: Android" + CR
	sAppInfo += "Database: " + DATABASE_NAME + CR
	
	RESULT sAppInfo
END

// Clean up temporary files
PROCEDURE CleanupTempFiles()
	LOCAL sSearchResult AS STRING
	LOCAL sFilePath AS STRING
	
	// Clean up temporary QR code files
	sSearchResult = fListFile(gsAppDataPath + "\qrcode_*.png")
	WHILE sSearchResult <> ""
		sFilePath = gsAppDataPath + "\" + ExtractString(sSearchResult, 1, CR)
		
		// Delete files older than 1 day
		IF DateDifference(fDate(sFilePath, "", fDateModified), DateSys()) > 1 THEN
			fDelete(sFilePath)
		END
		
		sSearchResult = fListFile(gsAppDataPath + "\qrcode_*.png", sSearchResult)
	END
	
	// Clean up temporary cryptograph files
	sSearchResult = fListFile(gsAppDataPath + "\temp_*.png")
	WHILE sSearchResult <> ""
		sFilePath = gsAppDataPath + "\" + ExtractString(sSearchResult, 1, CR)
		
		// Delete files older than 1 day
		IF DateDifference(fDate(sFilePath, "", fDateModified), DateSys()) > 1 THEN
			fDelete(sFilePath)
		END
		
		sSearchResult = fListFile(gsAppDataPath + "\temp_*.png", sSearchResult)
	END
END

// Application shutdown cleanup
PROCEDURE AppShutdownCleanup()
	// Clean up temporary files
	CleanupTempFiles()
	
	// Log application shutdown
	LogAppEvent("Application shutdown", "INFO")
	
	// Close database connections
	HCloseConnection()
END
