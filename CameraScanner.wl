// CryptoID Mobile Application - Camera Scanner Window
// WLanguage code for WinDev Mobile

// Global variables for camera scanning
GLOBAL
	gbCameraActive AS BOOLEAN = False
	gbScanningActive AS BOOLEAN = False
	gsLastScannedData AS STRING = ""
END

// Camera scanner window initialization
PROCEDURE WIN_CameraScanner_Initialize()
	// Set window title
	WIN_CameraScanner..Title = "Scan Cryptograph"
	
	// Initialize camera
	InitializeCamera()
	
	// Setup UI elements
	BTN_StartScan..Caption = "Start Scanning"
	BTN_StopScan..Caption = "Stop Scanning"
	BTN_StopScan..State = Grayed
	BTN_CapturePhoto..Caption = "Capture Photo"
	
	// Add instructions
	STC_Instructions..Caption = "Position the cryptograph within the camera view and tap 'Start Scanning'"
	STC_Status..Caption = "Ready to scan"
	STC_Status..Color = Blue
	
	// Initialize scanning state
	gbScanningActive = False
	gsLastScannedData = ""
END

// Initialize camera
PROCEDURE InitializeCamera()
	// Configure camera control
	CAM_Scanner..VideoFormat = camFormatAuto
	CAM_Scanner..Flash = camFlashAuto
	CAM_Scanner..Focus = camFocusAuto
	
	// Start camera preview
	IF CameraStartDecoding(CAM_Scanner) THEN
		gbCameraActive = True
		STC_Status..Caption = "Camera ready"
		STC_Status..Color = Green
	ELSE
		gbCameraActive = False
		STC_Status..Caption = "Camera initialization failed"
		STC_Status..Color = Red
		Error("Failed to initialize camera. Please check camera permissions.")
	END
END

// Start scanning button click
PROCEDURE BTN_StartScan_Click()
	IF gbCameraActive THEN
		gbScanningActive = True
		
		// Update UI
		BTN_StartScan..State = Grayed
		BTN_StopScan..State = Active
		STC_Status..Caption = "Scanning for QR codes..."
		STC_Status..Color = Orange
		
		// Start continuous scanning timer
		TimerSys("ScanTimer", 500, ScanForQRCode) // Scan every 500ms
	ELSE
		Error("Camera is not active. Please restart the scanner.")
	END
END

// Stop scanning button click
PROCEDURE BTN_StopScan_Click()
	StopScanning()
END

// Stop scanning process
PROCEDURE StopScanning()
	gbScanningActive = False
	
	// Stop scanning timer
	EndTimer("ScanTimer")
	
	// Update UI
	BTN_StartScan..State = Active
	BTN_StopScan..State = Grayed
	STC_Status..Caption = "Scanning stopped"
	STC_Status..Color = Blue
END

// Continuous QR code scanning function
PROCEDURE ScanForQRCode()
	LOCAL sCapturedImage AS STRING
	LOCAL sCryptographData AS STRING
	
	IF gbScanningActive AND gbCameraActive THEN
		// Capture current camera frame
		sCapturedImage = CameraCaptureToFile(CAM_Scanner)
		
		IF sCapturedImage <> "" THEN
			// Scan for QR code in the captured frame
			sCryptographData = ScanQRCodeFromImage(sCapturedImage)
			
			IF sCryptographData <> "" AND sCryptographData <> gsLastScannedData THEN
				// New QR code found
				gsLastScannedData = sCryptographData
				
				// Stop scanning
				StopScanning()
				
				// Process the scanned data
				ProcessScannedData(sCryptographData)
				
				// Clean up temporary image
				fDelete(sCapturedImage)
			ELSE
				// Clean up temporary image if no QR code found
				IF sCapturedImage <> "" THEN
					fDelete(sCapturedImage)
				END
			END
		END
	END
END

// Process scanned QR code data
PROCEDURE ProcessScannedData(sCryptographData AS STRING)
	// Update status
	STC_Status..Caption = "QR code detected! Processing..."
	STC_Status..Color = Green
	
	// Validate cryptograph data
	IF ValidateCryptographData(sCryptographData) THEN
		// Log scan activity
		LogScanActivity(sCryptographData, "CAMERA")
		
		// Show success message
		Info("Cryptograph scanned successfully!")
		
		// Close scanner and display information
		Close()
		DisplayCryptographInfo(sCryptographData)
	ELSE
		// Invalid cryptograph
		Error("Invalid cryptograph detected. Please try scanning again.")
		
		// Reset for next scan
		gsLastScannedData = ""
		STC_Status..Caption = "Ready to scan"
		STC_Status..Color = Blue
	END
END

// Capture photo button click (manual capture)
PROCEDURE BTN_CapturePhoto_Click()
	LOCAL sCapturedImage AS STRING
	LOCAL sCryptographData AS STRING
	
	IF gbCameraActive THEN
		// Capture photo
		sCapturedImage = CameraCaptureToFile(CAM_Scanner)
		
		IF sCapturedImage <> "" THEN
			// Update status
			STC_Status..Caption = "Photo captured. Scanning for QR code..."
			STC_Status..Color = Orange
			
			// Scan for QR code in captured photo
			sCryptographData = ScanQRCodeFromImage(sCapturedImage)
			
			IF sCryptographData <> "" THEN
				ProcessScannedData(sCryptographData)
			ELSE
				Error("No QR code found in the captured photo. Please try again.")
				STC_Status..Caption = "Ready to scan"
				STC_Status..Color = Blue
			END
			
			// Clean up temporary image
			fDelete(sCapturedImage)
		ELSE
			Error("Failed to capture photo. Please try again.")
		END
	ELSE
		Error("Camera is not active.")
	END
END

// Toggle flashlight button click
PROCEDURE BTN_ToggleFlash_Click()
	IF gbCameraActive THEN
		// Toggle camera flash
		IF CAM_Scanner..Flash = camFlashOff THEN
			CAM_Scanner..Flash = camFlashOn
			BTN_ToggleFlash..Caption = "Flash: ON"
		ELSE
			CAM_Scanner..Flash = camFlashOff
			BTN_ToggleFlash..Caption = "Flash: OFF"
		END
	END
END

// Back button click
PROCEDURE BTN_Back_Click()
	// Stop scanning if active
	IF gbScanningActive THEN
		StopScanning()
	END
	
	// Stop camera
	IF gbCameraActive THEN
		CameraStopDecoding(CAM_Scanner)
		gbCameraActive = False
	END
	
	Close()
END

// Window close event
PROCEDURE WIN_CameraScanner_Close()
	// Clean up resources
	IF gbScanningActive THEN
		StopScanning()
	END
	
	IF gbCameraActive THEN
		CameraStopDecoding(CAM_Scanner)
		gbCameraActive = False
	END
END
