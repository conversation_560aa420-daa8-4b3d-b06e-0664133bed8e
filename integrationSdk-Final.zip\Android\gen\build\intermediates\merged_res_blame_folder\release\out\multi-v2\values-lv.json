{"logs": [{"outputFile": "com.amlacameroon.sandbox-mergeReleaseResources-42:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b88e4f1102d8b08c17e772a2c7829ea1\\transformed\\appcompat-1.5.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,8305", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,8383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "97", "startColumns": "4", "startOffsets": "8388", "endColumns": "100", "endOffsets": "8484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e58339227eea27976199f5cf8fe7c88\\transformed\\material-1.8.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1153,1247,1320,1381,1506,1572,1640,1701,1773,1833,1887,2007,2067,2129,2183,2260,2390,2477,2559,2700,2780,2865,2956,3010,3063,3129,3203,3284,3368,3441,3518,3595,3669,3762,3837,3927,4018,4090,4168,4259,4313,4381,4465,4552,4614,4678,4741,4851,4964,5067,5179,5237,5294", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,90,53,52,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,57,56,76", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1148,1242,1315,1376,1501,1567,1635,1696,1768,1828,1882,2002,2062,2124,2178,2255,2385,2472,2554,2695,2775,2860,2951,3005,3058,3124,3198,3279,3363,3436,3513,3590,3664,3757,3832,3922,4013,4085,4163,4254,4308,4376,4460,4547,4609,4673,4736,4846,4959,5062,5174,5232,5289,5366"}, "to": {"startLines": "2,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,3616,3704,3805,3939,4022,4087,4181,4254,4315,4440,4506,4574,4635,4707,4767,4821,4941,5001,5063,5117,5194,5324,5411,5493,5634,5714,5799,5890,5944,5997,6063,6137,6218,6302,6375,6452,6529,6603,6696,6771,6861,6952,7024,7102,7193,7247,7315,7399,7486,7548,7612,7675,7785,7898,8001,8113,8171,8228", "endLines": "6,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "endColumns": "12,86,84,80,104,87,100,133,82,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,81,140,79,84,90,53,52,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,57,56,76", "endOffsets": "369,3340,3425,3506,3611,3699,3800,3934,4017,4082,4176,4249,4310,4435,4501,4569,4630,4702,4762,4816,4936,4996,5058,5112,5189,5319,5406,5488,5629,5709,5794,5885,5939,5992,6058,6132,6213,6297,6370,6447,6524,6598,6691,6766,6856,6947,7019,7097,7188,7242,7310,7394,7481,7543,7607,7670,7780,7893,7996,8108,8166,8223,8300"}}]}]}