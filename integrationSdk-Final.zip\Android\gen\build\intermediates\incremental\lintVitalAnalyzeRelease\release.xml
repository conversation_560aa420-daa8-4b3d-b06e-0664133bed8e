<variant
    name="release"
    package="com.amlacameroon.sandbox"
    minSdkVersion="24"
    targetSdkVersion="34"
    mergedManifest="build\intermediates\merged_manifest\release\AndroidManifest.xml"
    proguardFiles="prg.dat"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\26436efe58187f47fc7a11f38c2f7795\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifest="AndroidManifest.xml"
        javaDirectories="src;src\main\java;src\main\kotlin"
        resDirectories="res"
        assetsDirectories="assets"/>
    <sourceProvider
        manifest="src\release\AndroidManifest.xml"
        javaDirectories="src\release\java;src\release\kotlin"
        resDirectories="src\release\res"
        assetsDirectories="src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <mainArtifact
      classOutputs="build\intermediates\javac\release\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.amlacameroon.sandbox"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\aidl_source_output_dir\release\out;build\generated\source\buildConfig\release;build\generated\renderscript_source_output_dir\release\out"
      generatedResourceFolders="build\generated\res\rs\release;build\generated\res\resValues\release">
  </mainArtifact>
</variant>
