// CryptoID Mobile Application - Data Entry Window
// WLanguage code for WinDev Mobile

// Global variables for data entry
GLOBAL
	gsPhotoPath AS STRING = ""
	gbPhotoSelected AS BOOLEAN = False
END

// Data entry window initialization
PROCEDURE WIN_DataEntry_Initialize()
	// Set window title
	WIN_DataEntry..Title = "Enter " + gsSelectedCategoryName + " Information"
	
	// Clear all fields
	ClearAllFields()
	
	// Configure fields based on category
	ConfigureFieldsForCategory(gnSelectedCategoryID)
	
	// Initialize photo section
	IMG_Photo..Image = "default_photo.png"
	gsPhotoPath = ""
	gbPhotoSelected = False
END

// Clear all input fields
PROCEDURE ClearAllFields()
	EDT_FirstName = ""
	EDT_LastName = ""
	EDT_Email = ""
	EDT_Phone = ""
	EDT_DateOfBirth = ""
	EDT_Address = ""
	
	// School ID fields
	EDT_StudentID = ""
	EDT_School = ""
	EDT_Grade = ""
	EDT_AcademicYear = ""
	
	// Professional ID fields
	EDT_EmployeeID = ""
	EDT_Company = ""
	EDT_Department = ""
	EDT_Position = ""
	
	// Business Card fields
	EDT_BusinessName = ""
	EDT_BusinessType = ""
	EDT_Website = ""
END

// Configure fields visibility based on category
PROCEDURE ConfigureFieldsForCategory(nCategoryID AS INT)
	// Hide all category-specific fields first
	HideAllCategoryFields()
	
	SWITCH nCategoryID
		CASE 1: // School ID
			ShowSchoolFields()
		CASE 2: // Professional ID
			ShowProfessionalFields()
		CASE 3: // Business Card ID
			ShowBusinessFields()
	END
END

// Hide all category-specific fields
PROCEDURE HideAllCategoryFields()
	// School fields
	EDT_StudentID..Visible = False
	EDT_School..Visible = False
	EDT_Grade..Visible = False
	EDT_AcademicYear..Visible = False
	STC_StudentID..Visible = False
	STC_School..Visible = False
	STC_Grade..Visible = False
	STC_AcademicYear..Visible = False
	
	// Professional fields
	EDT_EmployeeID..Visible = False
	EDT_Company..Visible = False
	EDT_Department..Visible = False
	EDT_Position..Visible = False
	STC_EmployeeID..Visible = False
	STC_Company..Visible = False
	STC_Department..Visible = False
	STC_Position..Visible = False
	
	// Business fields
	EDT_BusinessName..Visible = False
	EDT_BusinessType..Visible = False
	EDT_Website..Visible = False
	STC_BusinessName..Visible = False
	STC_BusinessType..Visible = False
	STC_Website..Visible = False
END

// Show school-specific fields
PROCEDURE ShowSchoolFields()
	EDT_StudentID..Visible = True
	EDT_School..Visible = True
	EDT_Grade..Visible = True
	EDT_AcademicYear..Visible = True
	STC_StudentID..Visible = True
	STC_School..Visible = True
	STC_Grade..Visible = True
	STC_AcademicYear..Visible = True
END

// Show professional-specific fields
PROCEDURE ShowProfessionalFields()
	EDT_EmployeeID..Visible = True
	EDT_Company..Visible = True
	EDT_Department..Visible = True
	EDT_Position..Visible = True
	STC_EmployeeID..Visible = True
	STC_Company..Visible = True
	STC_Department..Visible = True
	STC_Position..Visible = True
END

// Show business-specific fields
PROCEDURE ShowBusinessFields()
	EDT_BusinessName..Visible = True
	EDT_BusinessType..Visible = True
	EDT_Website..Visible = True
	STC_BusinessName..Visible = True
	STC_BusinessType..Visible = True
	STC_Website..Visible = True
END

// Select photo button click
PROCEDURE BTN_SelectPhoto_Click()
	LOCAL sSelectedFile AS STRING
	
	// Open file picker for image selection
	sSelectedFile = fSelect("", "", "Select Photo", "Image files" + TAB + "*.jpg;*.jpeg;*.png;*.bmp", "jpg", fselOpen)
	
	IF sSelectedFile <> "" THEN
		gsPhotoPath = sSelectedFile
		gbPhotoSelected = True
		IMG_Photo..Image = sSelectedFile
		BTN_SelectPhoto..Caption = "Change Photo"
	END
END

// Take photo button click
PROCEDURE BTN_TakePhoto_Click()
	// Use camera to take photo
	LOCAL sPhotoPath AS STRING

	sPhotoPath = CameraCapture()

	IF sPhotoPath <> "" THEN
		gsPhotoPath = sPhotoPath
		gbPhotoSelected = True
		IMG_Photo..Image = sPhotoPath
		BTN_SelectPhoto..Caption = "Change Photo"
	END
END

// Save button click
PROCEDURE BTN_Save_Click()
	IF ValidateForm() THEN
		// Check if person already exists
		LOCAL nExistingPersonID AS INT
		nExistingPersonID = CheckExistingPerson()

		IF nExistingPersonID > 0 THEN
			LOCAL nResponse AS INT
			nResponse = YesNo("This person is already registered. Do you want to generate a new cryptograph for them?")
			IF nResponse = Yes THEN
				GenerateCryptograph(nExistingPersonID)
			END
		ELSE
			// Save new person and generate cryptograph
			LOCAL nPersonID AS INT
			nPersonID = SavePersonData()
			IF nPersonID > 0 THEN
				GenerateCryptograph(nPersonID)
			END
		END
	END
END

// Validate form data
PROCEDURE ValidateForm() : BOOLEAN
	LOCAL bValid AS BOOLEAN = True
	LOCAL sErrorMessage AS STRING = ""

	// Validate required fields
	IF EDT_FirstName = "" THEN
		sErrorMessage += "First Name is required" + CR
		bValid = False
	END

	IF EDT_LastName = "" THEN
		sErrorMessage += "Last Name is required" + CR
		bValid = False
	END

	IF NOT gbPhotoSelected THEN
		sErrorMessage += "Photo is required" + CR
		bValid = False
	END

	// Category-specific validation
	SWITCH gnSelectedCategoryID
		CASE 1: // School ID
			IF EDT_StudentID = "" THEN
				sErrorMessage += "Student ID is required" + CR
				bValid = False
			END
			IF EDT_School = "" THEN
				sErrorMessage += "School name is required" + CR
				bValid = False
			END

		CASE 2: // Professional ID
			IF EDT_EmployeeID = "" THEN
				sErrorMessage += "Employee ID is required" + CR
				bValid = False
			END
			IF EDT_Company = "" THEN
				sErrorMessage += "Company name is required" + CR
				bValid = False
			END

		CASE 3: // Business Card ID
			IF EDT_BusinessName = "" THEN
				sErrorMessage += "Business name is required" + CR
				bValid = False
			END
	END

	IF NOT bValid THEN
		Error(sErrorMessage)
	END

	RESULT bValid
END

// Check if person already exists
PROCEDURE CheckExistingPerson() : INT
	LOCAL nPersonID AS INT = 0
	LOCAL sQuery AS STRING

	sQuery = StringBuild("SELECT PersonID FROM PersonRecords WHERE FirstName = '%1' AND LastName = '%2' AND CategoryID = %3 AND IsActive = 1",
		EDT_FirstName, EDT_LastName, gnSelectedCategoryID)

	IF HExecuteQuery("QRY_CheckPerson", sQuery) THEN
		IF HReadFirst("QRY_CheckPerson") THEN
			nPersonID = QRY_CheckPerson.PersonID
		END
	END

	RESULT nPersonID
END

// Save person data to database
PROCEDURE SavePersonData() : INT
	LOCAL nPersonID AS INT = 0
	LOCAL sQuery AS STRING
	LOCAL sPhotoFileName AS STRING

	// Copy photo to app directory and get filename
	sPhotoFileName = SavePhotoToAppDirectory(gsPhotoPath)

	// Build insert query
	sQuery = "INSERT INTO PersonRecords (CategoryID, FirstName, LastName, Email, Phone, DateOfBirth, Address, PhotoPath, "

	SWITCH gnSelectedCategoryID
		CASE 1: // School ID
			sQuery += "StudentID, School, Grade, AcademicYear, "
		CASE 2: // Professional ID
			sQuery += "EmployeeID, Company, Department, Position, "
		CASE 3: // Business Card ID
			sQuery += "BusinessName, BusinessType, Website, "
	END

	sQuery += "CreatedBy) VALUES ("
	sQuery += StringBuild("%1, '%2', '%3', '%4', '%5', '%6', '%7', '%8', ",
		gnSelectedCategoryID, EDT_FirstName, EDT_LastName, EDT_Email, EDT_Phone, EDT_DateOfBirth, EDT_Address, sPhotoFileName)

	SWITCH gnSelectedCategoryID
		CASE 1: // School ID
			sQuery += StringBuild("'%1', '%2', '%3', '%4', ", EDT_StudentID, EDT_School, EDT_Grade, EDT_AcademicYear)
		CASE 2: // Professional ID
			sQuery += StringBuild("'%1', '%2', '%3', '%4', ", EDT_EmployeeID, EDT_Company, EDT_Department, EDT_Position)
		CASE 3: // Business Card ID
			sQuery += StringBuild("'%1', '%2', '%3', ", EDT_BusinessName, EDT_BusinessType, EDT_Website)
	END

	sQuery += StringBuild("%1)", gnCurrentAdminID)

	// Execute insert query
	IF HExecuteQuery("QRY_InsertPerson", sQuery) THEN
		// Get the inserted person ID
		sQuery = "SELECT last_insert_rowid() AS PersonID"
		IF HExecuteQuery("QRY_GetPersonID", sQuery) THEN
			IF HReadFirst("QRY_GetPersonID") THEN
				nPersonID = QRY_GetPersonID.PersonID
			END
		END
	END

	RESULT nPersonID
END

// Save photo to application directory
PROCEDURE SavePhotoToAppDirectory(sSourcePath AS STRING) : STRING
	LOCAL sDestinationPath AS STRING
	LOCAL sFileName AS STRING
	LOCAL sExtension AS STRING

	// Generate unique filename
	sExtension = fExtractPath(sSourcePath, fExtension)
	sFileName = "photo_" + DateTimeToString(DateTimeSys(), "YYYYMMDD_HHMMSS") + "_" + Random(1000, 9999) + sExtension
	sDestinationPath = fDataDir() + "\" + sFileName

	// Copy file
	IF fCopyFile(sSourcePath, sDestinationPath) THEN
		RESULT sFileName
	ELSE
		RESULT ""
	END
END

// Generate cryptograph for person
PROCEDURE GenerateCryptograph(nPersonID AS INT)
	LOCAL sCryptographData AS STRING
	LOCAL sCryptographHash AS STRING
	LOCAL sQuery AS STRING

	// Get person data
	sQuery = StringBuild("SELECT * FROM PersonRecords WHERE PersonID = %1", nPersonID)

	IF HExecuteQuery("QRY_GetPersonData", sQuery) THEN
		IF HReadFirst("QRY_GetPersonData") THEN
			// Create cryptograph data (JSON format)
			sCryptographData = CreateCryptographData(QRY_GetPersonData)

			// Generate hash for the cryptograph
			sCryptographHash = HashString(HA_SHA256, sCryptographData + DateTimeToString(DateTimeSys()))

			// Save cryptograph to database
			sQuery = StringBuild("INSERT INTO Cryptographs (PersonID, CryptographData, CryptographHash) VALUES (%1, '%2', '%3')",
				nPersonID, sCryptographData, sCryptographHash)

			IF HExecuteQuery("QRY_InsertCryptograph", sQuery) THEN
				// Show cryptograph generation options
				ShowCryptographOptions(sCryptographData, sCryptographHash)
			ELSE
				Error("Failed to save cryptograph")
			END
		END
	END
END

// Cancel button click
PROCEDURE BTN_Cancel_Click()
	LOCAL nResponse AS INT

	nResponse = YesNo("Are you sure you want to cancel? All entered data will be lost.")
	IF nResponse = Yes THEN
		Close()
	END
END

// Clear form button click
PROCEDURE BTN_Clear_Click()
	LOCAL nResponse AS INT

	nResponse = YesNo("Are you sure you want to clear all fields?")
	IF nResponse = Yes THEN
		ClearAllFields()
		gsPhotoPath = ""
		gbPhotoSelected = False
		IMG_Photo..Image = "default_photo.png"
		BTN_SelectPhoto..Caption = "Select Photo"
	END
END
