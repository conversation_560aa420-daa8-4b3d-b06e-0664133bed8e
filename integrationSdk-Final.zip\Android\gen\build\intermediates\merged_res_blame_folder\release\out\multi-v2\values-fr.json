{"logs": [{"outputFile": "com.amlacameroon.sandbox-mergeReleaseResources-42:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e58339227eea27976199f5cf8fe7c88\\transformed\\material-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2934,2989,3040,3106,3186,3267,3370,3443,3521,3594,3666,3759,3831,3923,4015,4089,4173,4265,4322,4388,4471,4558,4620,4684,4747,4849,4947,5044,5145,5204,5259", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,92,54,50,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2929,2984,3035,3101,3181,3262,3365,3438,3516,3589,3661,3754,3826,3918,4010,4084,4168,4260,4317,4383,4466,4553,4615,4679,4742,4844,4942,5039,5140,5199,5254,5343"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,3523,3621,3751,3836,3902,3999,4082,4148,4250,4315,4390,4446,4525,4585,4639,4761,4820,4882,4936,5018,5153,5245,5329,5473,5552,5633,5726,5781,5832,5898,5978,6059,6162,6235,6313,6386,6458,6551,6623,6715,6807,6881,6965,7057,7114,7180,7263,7350,7412,7476,7539,7641,7739,7836,7937,7996,8051", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,92,54,50,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,58,54,88", "endOffsets": "328,3150,3231,3314,3423,3518,3616,3746,3831,3897,3994,4077,4143,4245,4310,4385,4441,4520,4580,4634,4756,4815,4877,4931,5013,5148,5240,5324,5468,5547,5628,5721,5776,5827,5893,5973,6054,6157,6230,6308,6381,6453,6546,6618,6710,6802,6876,6960,7052,7109,7175,7258,7345,7407,7471,7534,7636,7734,7831,7932,7991,8046,8135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8227", "endColumns": "100", "endOffsets": "8323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b88e4f1102d8b08c17e772a2c7829ea1\\transformed\\appcompat-1.5.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,8140", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,8222"}}]}]}