buildscript {
	repositories {
		google()
		jcenter()
	}

	dependencies {
		classpath 'com.android.tools.build:gradle:7.4.2'

	}
}
repositories {
	google()
	jcenter()
	flatDir {
		dirs getProject().file('libs')
	}

}
apply plugin: 'com.android.application'
archivesBaseName = 'integrationSdk'
android {
	compileSdkVersion 34
	buildToolsVersion '34.0.0'
	useLibrary 'org.apache.http.legacy'
	
	defaultConfig {
		applicationId "com.amlacameroon.sandbox"
		ndk {
			abiFilters "armeabi-v7a"
		}

		minSdkVersion 24

		targetSdkVersion 34
	}

	sourceSets {
		main {
			manifest.srcFile getProject().file('AndroidManifest.xml')
			java.srcDirs = [getProject().file('src')]
			resources.srcDirs = [getProject().file('src')]
			res.srcDirs = [getProject().file('res')]
			assets.srcDirs = [getProject().file('assets')]
			aidl.srcDirs = [getProject().file('src')]
			jniLibs.srcDirs = [getProject().file('libs')]
		}
	}
	compileOptions {
		sourceCompatibility JavaVersion.VERSION_1_8
		targetCompatibility JavaVersion.VERSION_1_8
    }
	lintOptions {
		abortOnError false
	}
	signingConfigs {
		fromFile {
			storeFile file('C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\AMLA_CAMEROUN.jks')
			storePassword 'AMLA_CAMEROUN'
			keyAlias 'AMLA_CAMEROUN'
			keyPassword 'AMLA_CAMEROUN'			
		}
	}
	aaptOptions {
		noCompress 'FNC'
	}
	
	dexOptions {
		preDexLibraries = true
		jumboMode = false
		javaMaxHeapSize = '4096m'
    }
	
	bundle {
    language {
        enableSplit = false
    }
    density {
        enableSplit = true
    }
    abi {
        enableSplit = true
    }
	}
	
	
	buildTypes {
		debug {
		zipAlignEnabled true
		minifyEnabled false
		proguardFile file('prg.dat')
        multiDexEnabled true
        signingConfig signingConfigs.fromFile

		}
		release {
		zipAlignEnabled true
		minifyEnabled false
		proguardFile file('prg.dat')
		multiDexEnabled true
        signingConfig signingConfigs.fromFile

		}
		applicationVariants.all { variant ->  
			variant.outputs.each { output ->
				output.outputFileName = new File(archivesBaseName + ".apk")
			}
		}
	}
}

dependencies {
	// Remote binary dependency
	implementation 'androidx.appcompat:appcompat:1.3.0'
	implementation 'com.google.android.material:material:1.8.0'
	implementation 'androidx.constraintlayout:constraintlayout:2.1.0'
	implementation 'androidx.camera:camera-camera2:1.0.1'
	implementation 'androidx.camera:camera-lifecycle:1.0.1'
	implementation 'androidx.camera:camera-view:1.0.0-alpha28'
	implementation 'androidx.camera:camera-core:1.0.1'
	implementation 'com.google.code.gson:gson:2.10.1'
	implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0-alpha03'
	implementation 'androidx.exifinterface:exifinterface:1.3.2'


	// Modules dependency


	// Local binary dependency
	implementation fileTree(dir: getProject().file('libs'), include: ['*.jar','*.aar'])
	implementation files(getProject().file('C:\\PC SOFT\\WINDEV Mobile 2025\\Programs\\FrameWork\\Android\\WD300Android.aar'))
	implementation files(getProject().file('C:\\PC SOFT\\WINDEV Mobile 2025\\Programs\\FrameWork\\Android\\lib\\wd300android_wdl.aar'))

}

gradle.projectsEvaluated {
	tasks.withType(JavaCompile) {
		options.warnings = false
		
	}
}

