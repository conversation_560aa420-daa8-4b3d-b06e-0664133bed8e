// CryptoID Mobile Application - Cryptograph Options Window
// WLanguage code for WinDev Mobile

// Global variables for cryptograph display
GLOBAL
	gsGeneratedCryptographPath AS STRING = ""
END

// Cryptograph options window initialization
PROCEDURE WIN_CryptographOptions_Initialize()
	// Set window title
	WIN_CryptographOptions..Title = "Cryptograph Generated"
	
	// Generate visual cryptograph
	LOCAL stPersonData AS VARIANT
	LOCAL sPersonPhotoPath AS STRING
	
	// Parse cryptograph data
	stPersonData = JSONToVariant(gsCryptographData)
	sPersonPhotoPath = stPersonData.PhotoPath
	
	// Create visual cryptograph
	gsGeneratedCryptographPath = CreateVisualCryptograph(gsCryptographData, sPersonPhotoPath)
	
	IF gsGeneratedCryptographPath <> "" THEN
		// Display the generated cryptograph
		IMG_Cryptograph..Image = gsGeneratedCryptographPath
		
		// Show success message
		STC_Status..Caption = "Cryptograph generated successfully!"
		STC_Status..Color = Green
		
		// Enable action buttons
		BTN_SaveToGallery..State = Active
		BTN_ShareCryptograph..State = Active
		BTN_PrintCryptograph..State = Active
	ELSE
		// Show error message
		STC_Status..Caption = "Failed to generate cryptograph"
		STC_Status..Color = Red
		
		// Disable action buttons
		BTN_SaveToGallery..State = Grayed
		BTN_ShareCryptograph..State = Grayed
		BTN_PrintCryptograph..State = Grayed
	END
	
	// Display person information
	DisplayPersonInfo(stPersonData)
END

// Display person information in the window
PROCEDURE DisplayPersonInfo(stPersonData AS VARIANT)
	LOCAL sPersonInfo AS STRING
	
	sPersonInfo = stPersonData.FirstName + " " + stPersonData.LastName + CR
	
	SWITCH stPersonData.CategoryID
		CASE 1: // School ID
			sPersonInfo += "School ID" + CR
			sPersonInfo += "Student ID: " + stPersonData.StudentID + CR
			sPersonInfo += "School: " + stPersonData.School + CR
			sPersonInfo += "Grade: " + stPersonData.Grade
			
		CASE 2: // Professional ID
			sPersonInfo += "Professional ID" + CR
			sPersonInfo += "Employee ID: " + stPersonData.EmployeeID + CR
			sPersonInfo += "Company: " + stPersonData.Company + CR
			sPersonInfo += "Position: " + stPersonData.Position
			
		CASE 3: // Business Card ID
			sPersonInfo += "Business Card" + CR
			sPersonInfo += "Business: " + stPersonData.BusinessName + CR
			sPersonInfo += "Type: " + stPersonData.BusinessType
			IF stPersonData.Website <> "" THEN
				sPersonInfo += CR + "Website: " + stPersonData.Website
			END
	END
	
	STC_PersonInfo..Caption = sPersonInfo
END

// Save to gallery button click
PROCEDURE BTN_SaveToGallery_Click()
	IF gsGeneratedCryptographPath <> "" THEN
		LOCAL sGalleryPath AS STRING
		LOCAL sFileName AS STRING
		LOCAL bResult AS BOOLEAN
		
		// Get device's gallery/pictures directory
		sGalleryPath = SysDirExternalStorage(sseAppPictures)
		sFileName = "CryptoID_" + DateTimeToString(DateTimeSys(), "YYYYMMDD_HHMMSS") + ".png"
		
		// Copy cryptograph to gallery
		bResult = fCopyFile(gsGeneratedCryptographPath, sGalleryPath + "\" + sFileName)
		
		IF bResult THEN
			Info("Cryptograph saved to gallery successfully!")
			
			// Refresh media scanner to make image visible in gallery
			AndroidRefreshGallery(sGalleryPath + "\" + sFileName)
		ELSE
			Error("Failed to save cryptograph to gallery")
		END
	END
END

// Share cryptograph button click
PROCEDURE BTN_ShareCryptograph_Click()
	IF gsGeneratedCryptographPath <> "" THEN
		LOCAL stShareInfo AS STShareInfo
		
		// Configure share information
		stShareInfo.Title = "Share CryptoID"
		stShareInfo.Text = "CryptoID - Secure Identity Verification"
		stShareInfo.FilePath = gsGeneratedCryptographPath
		stShareInfo.MimeType = "image/png"
		
		// Share the cryptograph
		IF AndroidShare(stShareInfo) THEN
			Info("Cryptograph shared successfully!")
		ELSE
			Error("Failed to share cryptograph")
		END
	END
END

// Print cryptograph button click
PROCEDURE BTN_PrintCryptograph_Click()
	IF gsGeneratedCryptographPath <> "" THEN
		LOCAL nResponse AS INT
		
		nResponse = YesNo("Do you want to print the cryptograph?")
		IF nResponse = Yes THEN
			// Print the cryptograph image
			IF PrintImage(gsGeneratedCryptographPath) THEN
				Info("Cryptograph sent to printer successfully!")
			ELSE
				Error("Failed to print cryptograph")
			END
		END
	END
END

// Print image function
PROCEDURE PrintImage(sImagePath AS STRING) : BOOLEAN
	LOCAL bResult AS BOOLEAN = False
	
	// Open print preview
	iPreview()
	
	// Configure print settings
	iParameter(iOrientation, iPortrait)
	iParameter(iMargin, 10, 10, 10, 10) // 10mm margins
	
	// Print the image
	iPrintImage(sImagePath, 0, 0, 0, 0, iCenterH + iCenterV)
	
	// End print preview
	bResult = iEndPrinting()
	
	RESULT bResult
END

// View QR Code only button click
PROCEDURE BTN_ViewQRCode_Click()
	// Generate and display only the QR code
	LOCAL sQRCodePath AS STRING
	
	sQRCodePath = GenerateQRCode(gsCryptographData)
	
	IF sQRCodePath <> "" THEN
		// Open QR code viewer window
		gsQRCodePath = sQRCodePath
		Open(WIN_QRCodeViewer)
	ELSE
		Error("Failed to generate QR code")
	END
END

// Generate new cryptograph button click
PROCEDURE BTN_GenerateNew_Click()
	LOCAL nResponse AS INT
	
	nResponse = YesNo("Do you want to generate a new cryptograph for another person?")
	IF nResponse = Yes THEN
		// Close current window and return to category selection
		Close()
		Open(WIN_IDCategorySelection)
	END
END

// Back button click
PROCEDURE BTN_Back_Click()
	Close()
END

// Window close event
PROCEDURE WIN_CryptographOptions_Close()
	// Clean up temporary files if needed
	IF gsGeneratedCryptographPath <> "" AND fFileExist(gsGeneratedCryptographPath) THEN
		// Optionally delete temporary cryptograph file
		// fDelete(gsGeneratedCryptographPath)
	END
END

// Global variable for QR code viewer
GLOBAL
	gsQRCodePath AS STRING = ""
END
