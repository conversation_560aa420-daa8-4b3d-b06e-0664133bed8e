AAPT         |"      [   "      
raw/img_chargement_09_21 "=com.amlacameroon.sandbox-gen-45:/raw/img_chargement_09_21.svg <?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Calque_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="36px" height="864px" viewBox="0 0 36 864" enable-background="new 0 0 36 864" xml:space="preserve">
<g>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M18,3
		c1.343,0,2.646,0.176,3.884,0.507c1.28,0.343,2.496,0.851,3.617,1.5c1.12,0.648,2.149,1.438,3.063,2.343
		c0.941,0.935,1.76,1.993,2.431,3.148c0.638,1.104,1.14,2.295,1.481,3.554C32.817,15.31,33,16.633,33,17.999"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M21.884,39.507
		c1.28,0.343,2.496,0.851,3.617,1.5c1.12,0.648,2.149,1.438,3.063,2.343c0.941,0.935,1.76,1.993,2.431,3.148
		c0.638,1.104,1.14,2.295,1.481,3.554C32.817,51.31,33,52.633,33,53.999c0,1.343-0.176,2.645-0.508,3.883"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M25.501,77.007
		c1.12,0.648,2.149,1.438,3.063,2.343c0.941,0.935,1.76,1.993,2.431,3.148c0.638,1.104,1.14,2.295,1.481,3.554
		C32.817,87.31,33,88.633,33,89.999c0,1.343-0.176,2.645-0.508,3.883c-0.342,1.281-0.853,2.496-1.499,3.618"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M28.562,115.35
		c0.941,0.936,1.761,1.993,2.432,3.148c0.639,1.104,1.141,2.296,1.48,3.555c0.343,1.258,0.525,2.58,0.525,3.947
		c0,1.342-0.176,2.645-0.508,3.883c-0.342,1.281-0.852,2.496-1.499,3.618c-0.659,1.138-1.464,2.182-2.386,3.104"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M30.993,154.497
		c0.639,1.104,1.141,2.297,1.48,3.556c0.343,1.258,0.525,2.58,0.525,3.946c0,1.343-0.176,2.646-0.508,3.884
		c-0.342,1.28-0.852,2.496-1.499,3.617c-0.659,1.139-1.464,2.183-2.386,3.105c-0.926,0.923-1.969,1.729-3.106,2.386"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M32.474,194.053
		c0.343,1.258,0.525,2.58,0.525,3.947c0,1.342-0.176,2.645-0.508,3.883c-0.342,1.28-0.852,2.496-1.499,3.618
		c-0.659,1.138-1.464,2.182-2.386,3.104c-0.926,0.923-1.969,1.729-3.106,2.386c-1.122,0.649-2.336,1.157-3.616,1.5"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M33,234
		c0,1.342-0.176,2.645-0.508,3.883c-0.342,1.28-0.853,2.496-1.499,3.618c-0.659,1.138-1.464,2.182-2.386,3.104
		c-0.925,0.923-1.97,1.729-3.106,2.386c-1.121,0.649-2.336,1.157-3.617,1.5C20.646,248.823,19.343,249,18,249"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M32.492,273.883
		c-0.342,1.28-0.853,2.496-1.499,3.618c-0.659,1.138-1.464,2.182-2.386,3.104c-0.925,0.923-1.97,1.729-3.106,2.386
		c-1.121,0.649-2.336,1.157-3.617,1.5C20.646,284.823,19.343,285,18,285s-2.646-0.178-3.883-0.509"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M30.993,313.501
		c-0.659,1.138-1.464,2.182-2.386,3.104c-0.925,0.923-1.97,1.729-3.106,2.386c-1.121,0.649-2.336,1.157-3.617,1.5
		C20.646,320.823,19.343,321,18,321s-2.646-0.178-3.883-0.509c-1.28-0.343-2.496-0.851-3.618-1.5"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M28.607,352.607
		c-0.925,0.923-1.97,1.729-3.106,2.386c-1.121,0.649-2.336,1.157-3.617,1.5C20.646,356.823,19.343,357,18,357
		s-2.646-0.177-3.883-0.508c-1.281-0.343-2.496-0.851-3.618-1.5c-1.138-0.657-2.182-1.463-3.105-2.386"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M25.501,390.991
		c-1.121,0.649-2.336,1.157-3.617,1.5C20.646,392.823,19.343,393,18,393s-2.646-0.178-3.883-0.509
		c-1.281-0.343-2.496-0.851-3.618-1.5c-1.138-0.657-2.182-1.463-3.105-2.386c-0.926-0.925-1.729-1.972-2.389-3.111"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M21.884,428.491
		C20.646,428.823,19.343,429,18,429s-2.646-0.178-3.883-0.509c-1.281-0.343-2.496-0.851-3.618-1.5
		c-1.138-0.657-2.182-1.463-3.105-2.386c-0.926-0.925-1.729-1.972-2.389-3.111c-0.647-1.119-1.154-2.332-1.498-3.609"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M18,465
		c-1.343,0-2.646-0.178-3.883-0.509c-1.281-0.343-2.496-0.851-3.618-1.5c-1.138-0.657-2.182-1.463-3.105-2.386
		c-0.926-0.925-1.729-1.972-2.389-3.111c-0.647-1.119-1.154-2.332-1.498-3.609c-0.33-1.239-0.508-2.541-0.508-3.885"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M14.117,500.491
		c-1.281-0.343-2.496-0.851-3.618-1.5c-1.138-0.657-2.182-1.463-3.105-2.386c-0.926-0.925-1.729-1.972-2.389-3.111
		c-0.647-1.119-1.154-2.332-1.498-3.609c-0.33-1.239-0.508-2.541-0.508-3.885c0-1.345,0.176-2.646,0.508-3.885"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M10.499,534.991
		c-1.138-0.657-2.182-1.463-3.105-2.386c-0.926-0.925-1.729-1.972-2.389-3.111c-0.647-1.119-1.154-2.332-1.498-3.609
		c-0.33-1.239-0.508-2.541-0.508-3.885c0-1.345,0.176-2.646,0.508-3.885c0.342-1.28,0.851-2.496,1.5-3.617"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M7.394,568.607
		c-0.926-0.925-1.729-1.972-2.389-3.111c-0.647-1.12-1.154-2.332-1.498-3.609c-0.33-1.239-0.508-2.541-0.508-3.886
		c0-1.344,0.176-2.645,0.508-3.884c0.342-1.28,0.851-2.496,1.5-3.618c0.658-1.138,1.461-2.182,2.385-3.104"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M5.005,601.494
		c-0.647-1.119-1.154-2.332-1.498-3.609c-0.33-1.239-0.508-2.541-0.508-3.885c0-1.345,0.176-2.646,0.508-3.885
		c0.342-1.28,0.851-2.496,1.5-3.617c0.658-1.139,1.461-2.183,2.385-3.105c0.924-0.924,1.968-1.727,3.105-2.386"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M3.509,633.885
		C3.177,632.646,3,631.344,3,630c0-1.345,0.177-2.646,0.507-3.885c0.344-1.28,0.852-2.496,1.5-3.617
		c0.659-1.139,1.463-2.183,2.385-3.105c0.925-0.924,1.969-1.727,3.106-2.386c1.104-0.639,2.295-1.141,3.554-1.482"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M3,666
		c0-1.345,0.177-2.646,0.507-3.885c0.344-1.28,0.852-2.496,1.5-3.617c0.659-1.139,1.463-2.183,2.385-3.105
		c0.925-0.924,1.969-1.727,3.106-2.386c1.104-0.639,2.295-1.141,3.554-1.482c1.258-0.342,2.582-0.523,3.947-0.523"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M21.884,687.506
		C20.644,687.176,19.343,687,18,687c-1.366,0-2.688,0.183-3.948,0.523c-1.258,0.343-2.449,0.845-3.553,1.482
		c-1.138,0.658-2.183,1.462-3.105,2.386c-0.924,0.924-1.728,1.968-2.385,3.104c-0.649,1.122-1.158,2.337-1.5,3.618"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M5.007,730.497
		c0.659-1.138,1.463-2.182,2.385-3.104c0.925-0.924,1.969-1.727,3.106-2.386c1.104-0.639,2.295-1.141,3.554-1.482
		c1.258-0.342,2.582-0.523,3.947-0.523c1.344,0,2.645,0.176,3.883,0.506c1.281,0.344,2.496,0.852,3.618,1.501"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M7.392,763.393
		c0.925-0.924,1.969-1.727,3.106-2.386c1.104-0.639,2.295-1.141,3.554-1.482c1.258-0.342,2.582-0.523,3.947-0.523
		c1.344,0,2.645,0.176,3.883,0.506c1.281,0.344,2.496,0.852,3.618,1.501c1.12,0.648,2.149,1.438,3.062,2.343"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M10.499,797.007
		c1.104-0.639,2.295-1.141,3.553-1.482s2.582-0.523,3.948-0.523c1.343,0,2.646,0.176,3.884,0.506
		c1.28,0.344,2.495,0.852,3.617,1.501c1.12,0.648,2.149,1.438,3.063,2.343c0.941,0.935,1.76,1.993,2.431,3.147"/>
	<path fill="none" stroke="#673AB7" stroke-width="1.8749" stroke-linecap="round" stroke-miterlimit="10" d="M14.052,831.525
		c1.258-0.342,2.582-0.523,3.948-0.523c1.343,0,2.646,0.176,3.884,0.506c1.28,0.344,2.496,0.852,3.617,1.501
		c1.12,0.648,2.149,1.438,3.063,2.343c0.941,0.935,1.76,1.993,2.431,3.147c0.638,1.104,1.14,2.297,1.481,3.556"/>
	<rect y="0" fill="none" width="36" height="864"/>
</g>
</svg>
   