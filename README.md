# CryptoID Mobile Application

A secure mobile application for generating and scanning cryptographic ID cards using WLanguage and WinDev Mobile.

## Features

### Admin Features (Authentication Required)
- **Generate ID Cryptographs**: Create secure cryptographic IDs for:
  - School IDs (Student information)
  - Professional IDs (Employee information)
  - Business Card IDs (Business information)
- **Person Registration**: Store person information with photos
- **Duplicate Detection**: Prevent duplicate registrations
- **Cryptograph Management**: Generate, save, and share cryptographs

### Public Features (No Authentication)
- **Scan Cryptographs**: Read and verify cryptographic IDs using:
  - Camera scanning (real-time)
  - Photo upload from device
- **Information Display**: View decoded person information
- **Verification Status**: Check if cryptograph is verified in database
- **Save/Share**: Export scanned information

## Project Structure

```
CryptoID/
├── Database_Schema.sql          # Database structure and initial data
├── MainWindow.wl               # Main application window
├── AdminLogin.wl               # Admin authentication
├── IDCategorySelection.wl      # ID type selection screen
├── DataEntry.wl                # Dynamic data entry forms
├── CryptographGeneration.wl    # Cryptograph creation functions
├── CryptographOptions.wl       # Save/share generated cryptographs
├── ScanOptions.wl              # Scan method selection
├── CameraScanner.wl            # Real-time camera scanning
├── CryptographInfo.wl          # Display scanned information
└── README.md                   # This file
```

## Database Schema

The application uses SQLite database with the following tables:
- **AdminUsers**: Admin authentication
- **IDCategories**: ID type definitions
- **PersonRecords**: Person information and photos
- **Cryptographs**: Generated cryptograph data
- **ScanHistory**: Scan activity logging

## Setup Instructions

### Prerequisites
1. **WinDev Mobile** (latest version)
2. **Android SDK** (for Android deployment)
3. **Camera permissions** on target device
4. **Storage permissions** for saving files

### WinDev Mobile Project Setup

1. **Create New Project**:
   - Open WinDev Mobile
   - Create new Android project
   - Name: "CryptoID"
   - Target: Android

2. **Import Code Files**:
   - Copy all `.wl` files to project directory
   - Import each window/procedure into WinDev Mobile

3. **Database Setup**:
   - Create new HyperFileSQL database
   - Execute `Database_Schema.sql` to create tables
   - Configure database connection in project

4. **Required Components**:
   - Enable Camera control
   - Enable QR Code generation/scanning
   - Enable Image processing functions
   - Enable File management functions

5. **Permissions Configuration**:
   ```xml
   <uses-permission android:name="android.permission.CAMERA" />
   <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
   <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
   ```

### Window Structure

1. **WIN_Main**: Main menu with two options
2. **WIN_AdminLogin**: Admin authentication
3. **WIN_IDCategorySelection**: Choose ID type
4. **WIN_DataEntry**: Dynamic form for person data
5. **WIN_CryptographOptions**: Save/share generated cryptographs
6. **WIN_ScanOptions**: Choose scan method
7. **WIN_CameraScanner**: Real-time camera scanning
8. **WIN_CryptographInfo**: Display scanned information

## Key Functions

### Cryptograph Generation
- Creates JSON data structure with person information
- Generates QR code containing encrypted data
- Creates visual cryptograph with photo and QR code
- Stores cryptograph hash for verification

### Scanning Process
- Supports camera scanning and photo upload
- Decodes QR codes to extract person information
- Validates cryptograph data format
- Verifies against database for authenticity
- Logs scan activity for audit trail

## Security Features

- **Admin Authentication**: Secure login for ID generation
- **Data Encryption**: Cryptographic hashing for verification
- **Duplicate Prevention**: Checks existing registrations
- **Audit Trail**: Logs all scan activities
- **Data Validation**: Ensures cryptograph integrity

## Deployment

1. **Build Configuration**:
   - Set target Android version
   - Configure signing certificate
   - Set application permissions

2. **Testing**:
   - Test on physical device with camera
   - Verify database operations
   - Test QR code generation/scanning

3. **Distribution**:
   - Generate APK file
   - Deploy to target devices
   - Configure database on each device

## Usage Workflow

### Generating IDs (Admin)
1. Launch app → "Generate ID Cryptograph"
2. Login with admin credentials
3. Select ID type (School/Professional/Business)
4. Fill person information and add photo
5. Generate cryptograph
6. Save/share the generated cryptograph

### Scanning IDs (Public)
1. Launch app → "Scan Cryptograph"
2. Choose scan method (Camera/Upload)
3. Scan QR code or upload photo
4. View decoded information
5. Verify authenticity status
6. Save/share information if needed

## Technical Notes

- **QR Code Library**: Requires WinDev Mobile QR code component
- **Image Processing**: Uses built-in image manipulation functions
- **Database**: SQLite for local storage
- **JSON Handling**: Built-in JSON parsing functions
- **Camera Integration**: Native camera control

## Default Admin Credentials
- Username: `admin`
- Password: `admin123`

**⚠️ Important**: Change default credentials before production deployment!

## Support

For technical support or questions about implementation, refer to WinDev Mobile documentation or contact the development team.
