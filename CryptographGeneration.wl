// CryptoID Mobile Application - Cryptograph Generation Functions
// WLanguage code for WinDev Mobile

// Create cryptograph data in JSON format
PROCEDURE CreateCryptographData(stPersonRecord) : STRING
	LOCAL sCryptographData AS STRING
	LOCAL sPersonData AS STRING
	
	// Build JSON structure with person data
	sPersonData = "{"
	sPersonData += StringBuild("""PersonID"":%1,", stPersonRecord.PersonID)
	sPersonData += StringBuild("""CategoryID"":%1,", stPersonRecord.CategoryID)
	sPersonData += StringBuild("""FirstName"":"%1",", stPersonRecord.FirstName)
	sPersonData += StringBuild("""LastName"":"%1",", stPersonRecord.LastName)
	sPersonData += StringBuild("""Email"":"%1",", stPersonRecord.Email)
	sPersonData += StringBuild("""Phone"":"%1",", stPersonRecord.Phone)
	sPersonData += StringBuild("""DateOfBirth"":"%1",", stPersonRecord.DateOfBirth)
	sPersonData += StringBuild("""Address"":"%1",", stPersonRecord.Address)
	sPersonData += StringBuild("""PhotoPath"":"%1",", stPersonRecord.PhotoPath)
	
	// Add category-specific data
	SWITCH stPersonRecord.CategoryID
		CASE 1: // School ID
			sPersonData += StringBuild("""StudentID"":"%1",", stPersonRecord.StudentID)
			sPersonData += StringBuild("""School"":"%1",", stPersonRecord.School)
			sPersonData += StringBuild("""Grade"":"%1",", stPersonRecord.Grade)
			sPersonData += StringBuild("""AcademicYear"":"%1",", stPersonRecord.AcademicYear)
			
		CASE 2: // Professional ID
			sPersonData += StringBuild("""EmployeeID"":"%1",", stPersonRecord.EmployeeID)
			sPersonData += StringBuild("""Company"":"%1",", stPersonRecord.Company)
			sPersonData += StringBuild("""Department"":"%1",", stPersonRecord.Department)
			sPersonData += StringBuild("""Position"":"%1",", stPersonRecord.Position)
			
		CASE 3: // Business Card ID
			sPersonData += StringBuild("""BusinessName"":"%1",", stPersonRecord.BusinessName)
			sPersonData += StringBuild("""BusinessType"":"%1",", stPersonRecord.BusinessType)
			sPersonData += StringBuild("""Website"":"%1",", stPersonRecord.Website)
	END
	
	sPersonData += StringBuild("""GeneratedDate"":"%1"", DateTimeToString(DateTimeSys(), "YYYY-MM-DD HH:MM:SS"))
	sPersonData += "}"
	
	RESULT sPersonData
END

// Show cryptograph options window
PROCEDURE ShowCryptographOptions(sCryptographData AS STRING, sCryptographHash AS STRING)
	// Store cryptograph data globally for the options window
	gsCryptographData = sCryptographData
	gsCryptographHash = sCryptographHash
	
	// Open cryptograph options window
	Open(WIN_CryptographOptions)
END

// Generate QR Code from cryptograph data
PROCEDURE GenerateQRCode(sCryptographData AS STRING) : STRING
	LOCAL sQRCodePath AS STRING
	LOCAL sFileName AS STRING
	
	// Generate unique filename for QR code
	sFileName = "qrcode_" + DateTimeToString(DateTimeSys(), "YYYYMMDD_HHMMSS") + "_" + Random(1000, 9999) + ".png"
	sQRCodePath = fDataDir() + "\" + sFileName
	
	// Generate QR code using WinDev's QR code functions
	// Note: This requires QR code generation library in WinDev Mobile
	IF QRCodeGenerate(sCryptographData, sQRCodePath, qrCodeECC_M, 200, 200) THEN
		RESULT sQRCodePath
	ELSE
		RESULT ""
	END
END

// Create visual cryptograph with person info and QR code
PROCEDURE CreateVisualCryptograph(sCryptographData AS STRING, sPersonPhotoPath AS STRING) : STRING
	LOCAL sVisualCryptographPath AS STRING
	LOCAL sFileName AS STRING
	LOCAL sQRCodePath AS STRING
	
	// Generate QR code first
	sQRCodePath = GenerateQRCode(sCryptographData)
	
	IF sQRCodePath = "" THEN
		Error("Failed to generate QR code")
		RESULT ""
	END
	
	// Generate unique filename for visual cryptograph
	sFileName = "cryptograph_" + DateTimeToString(DateTimeSys(), "YYYYMMDD_HHMMSS") + "_" + Random(1000, 9999) + ".png"
	sVisualCryptographPath = fDataDir() + "\" + sFileName
	
	// Create composite image with person photo and QR code
	IF CreateCompositeImage(sPersonPhotoPath, sQRCodePath, sCryptographData, sVisualCryptographPath) THEN
		RESULT sVisualCryptographPath
	ELSE
		RESULT ""
	END
END

// Create composite image with photo, info, and QR code
PROCEDURE CreateCompositeImage(sPhotoPath AS STRING, sQRCodePath AS STRING, sCryptographData AS STRING, sOutputPath AS STRING) : BOOLEAN
	LOCAL bResult AS BOOLEAN = False
	LOCAL imgComposite AS Image
	LOCAL imgPhoto AS Image
	LOCAL imgQRCode AS Image
	LOCAL stPersonData AS VARIANT
	
	// Parse JSON data
	stPersonData = JSONToVariant(sCryptographData)
	
	// Create composite image (800x600 pixels)
	imgComposite..Width = 800
	imgComposite..Height = 600
	imgComposite..BackgroundColor = White
	
	// Load person photo
	imgPhoto = ImageLoad(fDataDir() + "\" + sPhotoPath)
	IF imgPhoto..Width > 0 THEN
		// Resize photo to fit (200x250 pixels)
		imgPhoto = ImageResize(imgPhoto, 200, 250)
		// Draw photo on composite
		ImageDraw(imgComposite, imgPhoto, 50, 50)
	END
	
	// Load QR code
	imgQRCode = ImageLoad(sQRCodePath)
	IF imgQRCode..Width > 0 THEN
		// Draw QR code on composite (right side)
		ImageDraw(imgComposite, imgQRCode, 550, 50)
	END
	
	// Add text information
	DrawPersonInfo(imgComposite, stPersonData)
	
	// Save composite image
	bResult = ImageSave(imgComposite, sOutputPath, imgPNG)
	
	RESULT bResult
END

// Draw person information on the composite image
PROCEDURE DrawPersonInfo(imgComposite AS Image, stPersonData AS VARIANT)
	LOCAL nYPosition AS INT = 320
	LOCAL sFont AS STRING = "Arial,12,Bold"
	
	// Draw person information
	ImageDrawText(imgComposite, 50, nYPosition, stPersonData.FirstName + " " + stPersonData.LastName, Black, sFont)
	nYPosition += 25
	
	IF stPersonData.Email <> "" THEN
		ImageDrawText(imgComposite, 50, nYPosition, "Email: " + stPersonData.Email, Black, "Arial,10")
		nYPosition += 20
	END
	
	IF stPersonData.Phone <> "" THEN
		ImageDrawText(imgComposite, 50, nYPosition, "Phone: " + stPersonData.Phone, Black, "Arial,10")
		nYPosition += 20
	END
	
	// Category-specific information
	SWITCH stPersonData.CategoryID
		CASE 1: // School ID
			ImageDrawText(imgComposite, 50, nYPosition, "Student ID: " + stPersonData.StudentID, Black, "Arial,10")
			nYPosition += 20
			ImageDrawText(imgComposite, 50, nYPosition, "School: " + stPersonData.School, Black, "Arial,10")
			nYPosition += 20
			ImageDrawText(imgComposite, 50, nYPosition, "Grade: " + stPersonData.Grade, Black, "Arial,10")
			
		CASE 2: // Professional ID
			ImageDrawText(imgComposite, 50, nYPosition, "Employee ID: " + stPersonData.EmployeeID, Black, "Arial,10")
			nYPosition += 20
			ImageDrawText(imgComposite, 50, nYPosition, "Company: " + stPersonData.Company, Black, "Arial,10")
			nYPosition += 20
			ImageDrawText(imgComposite, 50, nYPosition, "Position: " + stPersonData.Position, Black, "Arial,10")
			
		CASE 3: // Business Card ID
			ImageDrawText(imgComposite, 50, nYPosition, "Business: " + stPersonData.BusinessName, Black, "Arial,10")
			nYPosition += 20
			IF stPersonData.Website <> "" THEN
				ImageDrawText(imgComposite, 50, nYPosition, "Website: " + stPersonData.Website, Black, "Arial,10")
			END
	END
	
	// Add generation date
	ImageDrawText(imgComposite, 50, 550, "Generated: " + stPersonData.GeneratedDate, Gray, "Arial,8")
END

// Global variables for cryptograph options
GLOBAL
	gsCryptographData AS STRING = ""
	gsCryptographHash AS STRING = ""
END
