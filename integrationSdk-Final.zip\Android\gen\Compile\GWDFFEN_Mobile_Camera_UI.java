/**
 * Code generated by WINDEV Mobile - DO NOT MODIFY!
 * WINDEV Mobile object: Fenêtre
 * Android class: FEN_Mobile_Camera_UI
 * Version of wdjava64.dll: 30.0.302.8
 */


package com.amlacameroon.sandbox.wdgen;


import com.amlacameroon.sandbox.*;
import fr.pcsoft.wdjava.core.types.*;
import fr.pcsoft.wdjava.core.*;
import fr.pcsoft.wdjava.ui.champs.fenetre.*;
import fr.pcsoft.wdjava.ui.champs.libelle.*;
import fr.pcsoft.wdjava.ui.cadre.*;
import fr.pcsoft.wdjava.ui.champs.camera.*;
import fr.pcsoft.wdjava.api.*;
import fr.pcsoft.wdjava.core.erreur.*;
import fr.pcsoft.wdjava.ui.champs.image.*;
import fr.pcsoft.wdjava.core.context.*;
import fr.pcsoft.wdjava.core.application.*;
import fr.pcsoft.wdjava.ui.champs.multimedia.*;
import fr.pcsoft.wdjava.ui.champs.bouton.*;
import fr.pcsoft.wdjava.core.debug.*;
import fr.pcsoft.wdjava.ui.champs.*;
import fr.pcsoft.wdjava.ui.activite.*;
/*Imports trouvés dans le code WL*/
/*Fin Imports trouvés dans le code WL*/



public class GWDFFEN_Mobile_Camera_UI extends WDFenetre
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des plans nommés de FEN_Mobile_Camera_UI
////////////////////////////////////////////////////////////////////////////
 public WDEntier4 mWD_PLAN_Capture_Photo = new WDEntier4(1);
 public WDEntier4 mWD_PLAN_Capture_Video = new WDEntier4(2);
 public WDEntier4 mWD_PLAN_Enregistrement_en_cours = new WDEntier4(3);
 public WDEntier4 mWD_PLAN_Apercu_de_photo = new WDEntier4(4);
 public WDEntier4 mWD_PLAN_Apercu_de_video = new WDEntier4(5);

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs de FEN_Mobile_Camera_UI
////////////////////////////////////////////////////////////////////////////

/**
 * LIB_Fond
 */
class GWDLIB_Fond extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de FEN_Mobile_Camera_UI.LIB_Fond
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2391678905617609355l);

super.setChecksum("592636718");

super.setNom("LIB_Fond");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 360);

super.setPlans(new int[]{4, 5});

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(10, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDLIB_Fond mWD_LIB_Fond;

/**
 * CAM_Capture
 */
class GWDCAM_Capture extends WDChampCameraV2
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de FEN_Mobile_Camera_UI.CAM_Capture
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360889480587891227l);

super.setChecksum("2099875156");

super.setNom("CAM_Capture");

super.setType(24);

super.setBulle("");

super.setLibelle("");

super.setCurseurSouris(0);

super.setNote("", "");

super.setCouleurFond(getCouleur_GEN(0xfff5f5f5));

super.setEtatInitial(0);

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 360);

super.setPlans(new int[]{1, 2, 3});

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAncrageInitial(10, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setParamDecodageCodeBarres(0, 0, 60);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setParamCamera(1, 2);

super.setParamPrevisualisation(3);

super.setParamGesture(true, true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -11.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xfff5f5f5), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);


activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Zoom in or out in CAM_Capture
 */
public void trtZoomUtilisateur()
// ModifieZoom(Arrondi(CAM_Capture..zoom,1))
{
super.trtZoomUtilisateur();

// ModifieZoom(Arrondi(CAM_Capture..zoom,1))

try
{
// ModifieZoom(Arrondi(CAM_Capture..zoom,1))
fWD_modifieZoom(WDAPIMath.arrondi(this.getProp(EWDPropriete.PROP_ZOOM),1));

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCAM_Capture mWD_CAM_Capture;

/**
 * IMG_ModeVidéo
 */
class GWDIMG_ModeVideo extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°3 de FEN_Mobile_Camera_UI.IMG_ModeVidéo
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360788923603063122l);

super.setChecksum("-1110649706");

super.setNom("IMG_ModeVidéo");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(32, 301);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Camera-Video_Mobile_24_1.svg");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(3);

super.setAncrageInitial(1, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click IMG_ModeVidéo
 */
public void clicSurBoutonGauche()
// Retour haptique
// 
{
super.clicSurBoutonGauche();

// 

try
{
// RetourHaptique()
fWD_retourHaptique();

// MaFenetre..Plan = PLAN_Capture_Vidéo
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Capture_Video);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_ModeVideo mWD_IMG_ModeVideo;

/**
 * LIB_TempsEcoulé
 */
class GWDLIB_TempsEcoule extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°4 de FEN_Mobile_Camera_UI.LIB_TempsEcoulé
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2360792896520891886l);

super.setChecksum("-1037571097");

super.setNom("LIB_TempsEcoulé");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("HH:MM:SS");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 40);

super.setPlan(3);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(4);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -11.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff808080), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffbababa), getCouleur_GEN(0xff3a3a3a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDLIB_TempsEcoule mWD_LIB_TempsEcoule;

/**
 * IMG_ModePhoto
 */
class GWDIMG_ModePhoto extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°5 de FEN_Mobile_Camera_UI.IMG_ModePhoto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360844486138919268l);

super.setChecksum("1728278321");

super.setNom("IMG_ModePhoto");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(32, 301);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Appareil-Photo_Mobile_24_1.svg");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(5);

super.setAncrageInitial(1, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click IMG_ModePhoto
 */
public void clicSurBoutonGauche()
// Bascule vers la capture de photo
// // Bascule vers la capture de photo
{
super.clicSurBoutonGauche();

// // Bascule vers la capture de photo

try
{
// MaFenêtre..Plan = PLAN_Capture_Photo
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Capture_Photo);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_ModePhoto mWD_IMG_ModePhoto;

/**
 * IMG_Flash
 */
class GWDIMG_Flash extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°6 de FEN_Mobile_Camera_UI.IMG_Flash
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360844657939616844l);

super.setChecksum("1730284097");

super.setNom("IMG_Flash");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(296, 32);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Automatique-Eclair-Flash-Alimentation_Mobile_16_1.svg");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(6);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of IMG_Flash
 */
public void init()
// Le picto du bouton se mettra a jour automatiquement avec le bon picto
// 
{
super.init();

// 
final WDProcedureInterne []fWDI_demandeMAJUI_Flash = new WDProcedureInterne[1];
fWDI_demandeMAJUI_Flash[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "DemandeMAJUI_Flash";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_demandeMAJUI_Flash();
}
// 	PROCÉDURE INTERNE DemandeMAJUI_Flash()
public void fWD_demandeMAJUI_Flash()
{
// 	PROCÉDURE INTERNE DemandeMAJUI_Flash()
initExecProcInterne();



try
{

try
{
// 		SELON CAM_Capture..Flash
// Délimiteur de visibilité pour ne pas étendre la visibilité de la variable temporaire _WDExpSelon
{
// 		SELON CAM_Capture..Flash
WDObjet _WDExpSelon0 = mWD_CAM_Capture.getProp(EWDPropriete.PROP_FLASH);
// 		SELON CAM_Capture..Flash
if(_WDExpSelon0.opEgal(2, 0))
{
// 				MonChamp = IMG_FlashAuto
mWD_IMG_Flash.setValeur(mWD_IMG_FlashAuto);

}
// 		SELON CAM_Capture..Flash
else if(_WDExpSelon0.opEgal(1, 0))
{
// 				MonChamp = IMG_FlashOn
mWD_IMG_Flash.setValeur(mWD_IMG_FlashOn);

}
// 		SELON CAM_Capture..Flash
else if(_WDExpSelon0.opEgal(0, 0))
{
// 				MonChamp = IMG_FlashOff
mWD_IMG_Flash.setValeur(mWD_IMG_FlashOff);

}
// 		SELON CAM_Capture..Flash
else {
}

}

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_demandeMAJUI_Flash[0]);

try
{
// MaFenêtre..Traitement[trtDemandeMiseAJour]+=DemandeMAJUI_Flash
WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_TRAITEMENT).get(234).opPlusEgal(fWDI_demandeMAJUI_Flash[0]);

// 	PROCÉDURE INTERNE DemandeMAJUI_Flash()

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: Click IMG_Flash
 */
public void clicSurBoutonGauche()
// Change le mode de flash
// // Change le mode de flash
{
super.clicSurBoutonGauche();

// // Change le mode de flash

try
{
// SELON CAM_Capture..Flash
// Délimiteur de visibilité pour ne pas étendre la visibilité de la variable temporaire _WDExpSelon
{
// SELON CAM_Capture..Flash
WDObjet _WDExpSelon0 = mWD_CAM_Capture.getProp(EWDPropriete.PROP_FLASH);
// SELON CAM_Capture..Flash
if(_WDExpSelon0.opEgal(0, 0))
{
// 	CAS camFlashOff:	CAM_Capture..Flash=camFlashAuto
mWD_CAM_Capture.setProp(EWDPropriete.PROP_FLASH,2);

}
// SELON CAM_Capture..Flash
else if(_WDExpSelon0.opEgal(2, 0))
{
// 	CAS camFlashAuto:	CAM_Capture..Flash=camFlashOn
mWD_CAM_Capture.setProp(EWDPropriete.PROP_FLASH,1);

}
// SELON CAM_Capture..Flash
else if(_WDExpSelon0.opEgal(1, 0))
{
// 	CAS camFlashOn:		CAM_Capture..Flash=camFlashOff
mWD_CAM_Capture.setProp(EWDPropriete.PROP_FLASH,0);

}
// SELON CAM_Capture..Flash
else {
}

}

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_Flash mWD_IMG_Flash;

/**
 * IMG_Torche
 */
class GWDIMG_Torche extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°7 de FEN_Mobile_Camera_UI.IMG_Torche
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360844718070746227l);

super.setChecksum("1731871350");

super.setNom("IMG_Torche");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(296, 32);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Automatique-Eclair-Flash-Alimentation-Desactive-Off_Mobile_16_1.svg");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(7);

super.setAncrageInitial(4, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of IMG_Torche
 */
public void init()
// Le picto du bouton se mettra a jour automatiquement avec le bon picto
// // Le picto du bouton se mettra a jour automatiquement avec le bon picto
{
super.init();

// // Le picto du bouton se mettra a jour automatiquement avec le bon picto
final WDProcedureInterne []fWDI_demandeMAJUI_Torche = new WDProcedureInterne[1];
fWDI_demandeMAJUI_Torche[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "DemandeMAJUI_Torche";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_demandeMAJUI_Torche();
}
// 	PROCÉDURE INTERNE DemandeMAJUI_Torche()
public void fWD_demandeMAJUI_Torche()
{
// 	PROCÉDURE INTERNE DemandeMAJUI_Torche()
initExecProcInterne();



try
{

try
{
// 		SELON CAM_Capture..Torche
// Délimiteur de visibilité pour ne pas étendre la visibilité de la variable temporaire _WDExpSelon
{
// 		SELON CAM_Capture..Torche
WDObjet _WDExpSelon0 = mWD_CAM_Capture.getProp(EWDPropriete.PROP_TORCHE);
// 		SELON CAM_Capture..Torche
if(_WDExpSelon0.opEgal(2, 0))
{
// 				MonChamp = IMG_FlashAuto
mWD_IMG_Torche.setValeur(mWD_IMG_FlashAuto);

}
// 		SELON CAM_Capture..Torche
else if(_WDExpSelon0.opEgal(1, 0))
{
// 				MonChamp = IMG_FlashOn
mWD_IMG_Torche.setValeur(mWD_IMG_FlashOn);

}
// 		SELON CAM_Capture..Torche
else if(_WDExpSelon0.opEgal(0, 0))
{
// 				MonChamp = IMG_FlashOff
mWD_IMG_Torche.setValeur(mWD_IMG_FlashOff);

}
// 		SELON CAM_Capture..Torche
else {
}

}

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_demandeMAJUI_Torche[0]);

try
{
// MaFenêtre..Traitement[trtDemandeMiseAJour]+=DemandeMAJUI_Torche
WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_TRAITEMENT).get(234).opPlusEgal(fWDI_demandeMAJUI_Torche[0]);

// 	PROCÉDURE INTERNE DemandeMAJUI_Torche()

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: Click IMG_Torche
 */
public void clicSurBoutonGauche()
// Change le mode de torche
// // Change le mode de torche
{
super.clicSurBoutonGauche();

// // Change le mode de torche

try
{
// Si CAM_Capture..Torche=camFlashOn alors
if(mWD_CAM_Capture.getProp(EWDPropriete.PROP_TORCHE).opEgal(1, 0))
{
// 	CAM_Capture..Torche=camFlashOff 
mWD_CAM_Capture.setProp(EWDPropriete.PROP_TORCHE,0);

}
else
{
// 	CAM_Capture..Torche=camFlashOn
mWD_CAM_Capture.setProp(EWDPropriete.PROP_TORCHE,1);

}

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_Torche mWD_IMG_Torche;

/**
 * IMG_ChangerCaméra
 */
class GWDIMG_ChangerCamera extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°8 de FEN_Mobile_Camera_UI.IMG_ChangerCaméra
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360844902755890268l);

super.setChecksum("1733421706");

super.setNom("IMG_ChangerCaméra");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(288, 301);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Switch-Camera_Mobile_24_1.svg");

super.setPlans(new int[]{1, 2});

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(8);

super.setAncrageInitial(5, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click IMG_ChangerCaméra
 */
public void clicSurBoutonGauche()
// Demande de basculer la caméra
// // Demande de basculer la caméra
{
super.clicSurBoutonGauche();

// // Demande de basculer la caméra

try
{
// si CAM_Capture..Caméra=camCaméraDorsale alors
if(mWD_CAM_Capture.getProp(EWDPropriete.PROP_CAMERA).opEgal(1, 0))
{
// 	CAM_Capture..Caméra=camCaméraFrontale 
mWD_CAM_Capture.setProp(EWDPropriete.PROP_CAMERA,0);

}
else
{
// 	CAM_Capture..Caméra=camCaméraDorsale 
mWD_CAM_Capture.setProp(EWDPropriete.PROP_CAMERA,1);

}

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_ChangerCamera mWD_IMG_ChangerCamera;

/**
 * IMG_ActionPhoto
 */
class GWDIMG_ActionPhoto extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°9 de FEN_Mobile_Camera_UI.IMG_ActionPhoto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360845237766169227l);

super.setChecksum("1736251655");

super.setNom("IMG_ActionPhoto");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(147, 284);

super.setTailleInitiale(66, 66);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Appareil-Photo-Capture_Mobile_66_1.svg");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(9);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click IMG_ActionPhoto
 */
public void clicSurBoutonGauche()
// Prend une photo à partir du champ caméra
// // Prend une photo à partir du champ caméra
{
super.clicSurBoutonGauche();

// // Prend une photo à partir du champ caméra

try
{
// PrendrePhoto()
fWD_prendrePhoto();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_ActionPhoto mWD_IMG_ActionPhoto;

/**
 * IMG_ActionVidéo
 */
class GWDIMG_ActionVideo extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°10 de FEN_Mobile_Camera_UI.IMG_ActionVidéo
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360845349436524837l);

super.setChecksum("1737457595");

super.setNom("IMG_ActionVidéo");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(147, 284);

super.setTailleInitiale(66, 66);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Video-Enregistrer_Mobile_66_1.svg");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(10);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click IMG_ActionVidéo
 */
public void clicSurBoutonGauche()
// Démarre l'enregistrement d'une vidéo à partir du champ caméra
// // Démarre l'enregistrement d'une vidéo à partir du champ caméra
{
super.clicSurBoutonGauche();

// // Démarre l'enregistrement d'une vidéo à partir du champ caméra

try
{
// DémarrerEnregistrementVidéo()
fWD_demarrerEnregistrementVideo();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_ActionVideo mWD_IMG_ActionVideo;

/**
 * IMG_ArrêtEnregistrement
 */
class GWDIMG_ArretEnregistrement extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°11 de FEN_Mobile_Camera_UI.IMG_ArrêtEnregistrement
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360845551301345766l);

super.setChecksum("1738815659");

super.setNom("IMG_ArrêtEnregistrement");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(147, 284);

super.setTailleInitiale(66, 66);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Video-Enregistrer-Stop_Mobile_66_1.svg");

super.setPlan(3);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(11);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click IMG_ArrêtEnregistrement
 */
public void clicSurBoutonGauche()
// CaméraVidéoArrête(CAM_Capture)
{
super.clicSurBoutonGauche();

// CaméraVidéoArrête(CAM_Capture)

try
{
// CaméraVidéoArrête(CAM_Capture)
WDAPICamera.cameraVideoArrete(mWD_CAM_Capture);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_ArretEnregistrement mWD_IMG_ArretEnregistrement;

/**
 * MM_Aperçu
 */
class GWDMM_Apercu extends WDChampMultimedia
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°12 de FEN_Mobile_Camera_UI.MM_Aperçu
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360888162013356990l);

super.setChecksum("2080307324");

super.setNom("MM_Aperçu");

super.setType(39);

super.setBulle("");

super.setLibelle("Multimédia");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setValeurInitiale("");

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 288);

super.setPlan(5);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(12);

super.setAncrageInitial(10, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(3);

super.setModeAffichage(6);

super.setParamControles(1);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -11.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);


activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDMM_Apercu mWD_MM_Apercu;

/**
 * BTN_ReprendreVidéo
 */
class GWDBTN_ReprendreVideo extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°13 de FEN_Mobile_Camera_UI.BTN_ReprendreVidéo
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2360888458368660001l);

super.setChecksum("2082851020");

super.setNom("BTN_ReprendreVidéo");

super.setType(4);

super.setBulle("");

super.setLibelle("Réessayer");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(14, 303);

super.setTailleInitiale(160, 48);

super.setPlan(5);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(13);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(4);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(1);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 4, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_ReprendreVidéo
 */
public void clicSurBoutonGauche()
// Arrête la vidéo
// // Arrête la vidéo
{
super.clicSurBoutonGauche();

// // Arrête la vidéo

try
{
// MultimédiaArrêt(MM_Aperçu)
WDAPIMediaPlayer.multimediaArret(mWD_MM_Apercu);

// fSupprime(gsChemin)
WDAPIFichier.fSupprime(vWD_gsChemin.getString());

// gsChemin = ""
vWD_gsChemin.setValeur("");

// MaFenêtre..Plan =  PLAN_Capture_Vidéo
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Capture_Video);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_ReprendreVideo mWD_BTN_ReprendreVideo;

/**
 * IMG_Aperçu
 */
class GWDIMG_Apercu extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°14 de FEN_Mobile_Camera_UI.IMG_Aperçu
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2360888630170603167l);

super.setChecksum("2086104210");

super.setNom("IMG_Aperçu");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 288);

super.setValeurInitiale("");

super.setPlan(4);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(14);

super.setAncrageInitial(10, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 2, true, 400);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_Apercu mWD_IMG_Apercu;

/**
 * BTN_ReprendrePhoto
 */
class GWDBTN_ReprendrePhoto extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°15 de FEN_Mobile_Camera_UI.BTN_ReprendrePhoto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2360855094867896616l);

super.setChecksum("1888035195");

super.setNom("BTN_ReprendrePhoto");

super.setType(4);

super.setBulle("");

super.setLibelle("Réessayer");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(14, 303);

super.setTailleInitiale(160, 48);

super.setPlan(4);

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(15);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(1);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 4, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_ReprendrePhoto
 */
public void clicSurBoutonGauche()
// Ignore la photo prise
// // Ignore la photo prise
{
super.clicSurBoutonGauche();

// // Ignore la photo prise

try
{
// fSupprime(gsChemin)
WDAPIFichier.fSupprime(vWD_gsChemin.getString());

// gsChemin = ""
vWD_gsChemin.setValeur("");

// IMG_Aperçu = ""
mWD_IMG_Apercu.setValeur("");

// MaFenêtre..Plan	= PLAN_Capture_Photo
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Capture_Photo);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_ReprendrePhoto mWD_BTN_ReprendrePhoto;

/**
 * BTN_OK
 */
class GWDBTN_OK extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°16 de FEN_Mobile_Camera_UI.BTN_OK
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2360855116342869200l);

super.setChecksum("1888171304");

super.setNom("BTN_OK");

super.setType(4);

super.setBulle("");

super.setLibelle("OK");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(187, 303);

super.setTailleInitiale(160, 48);

super.setPlans(new int[]{4, 5});

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(16);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(2);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(1);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 4, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff7f7f7f), getCouleur_GEN(0xff000000), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_OK
 */
public void clicSurBoutonGauche()
// Ferme
{
super.clicSurBoutonGauche();

// Ferme

try
{
// Ferme
WDAPIFenetre.ferme();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_OK mWD_BTN_OK;

/**
 * BTN_Zoom100
 */
class GWDBTN_Zoom100 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°17 de FEN_Mobile_Camera_UI.BTN_Zoom100
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2360891190014305436l);

super.setChecksum("2129296835");

super.setNom("BTN_Zoom100");

super.setType(4);

super.setBulle("");

super.setLibelle("1x");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(100, 222);

super.setTailleInitiale(48, 48);

super.setPlans(new int[]{1, 2});

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(17);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(-1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(true);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(1);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 4, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of BTN_Zoom100
 */
public void init()
// global
{
super.init();

// global

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
final WDObjet vWD_ChampZoom = new WDChampWL();



final WDProcedureInterne []fWDI_demandeMAJUI_BoutonZoom = new WDProcedureInterne[1];
fWDI_demandeMAJUI_BoutonZoom[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "DemandeMAJUI_BoutonZoom";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_demandeMAJUI_BoutonZoom();
}
// 	PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()
public void fWD_demandeMAJUI_BoutonZoom()
{
// 	PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()
initExecProcInterne();



try
{

try
{
// 		RetourHaptique()
fWD_retourHaptique();

// 		ChampZoom..Enfoncé = (CAM_Capture..Zoom=dValeurZoomBouton)
vWD_ChampZoom.setProp(EWDPropriete.PROP_ENFONCE,mWD_CAM_Capture.getProp(EWDPropriete.PROP_ZOOM).opEgal(vWD_dValeurZoomBouton, 0));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_demandeMAJUI_BoutonZoom[0]);

try
{
// global

// 	dValeurZoomBouton est un numerique = val(MonChamp..Libellé)
vWD_dValeurZoomBouton = new WDNumerique(32, 6);

vWD_dValeurZoomBouton.setValeur(WDAPIChaine.val(this.getProp(EWDPropriete.PROP_LIBELLE)));

super.ajouterVariableGlobale("dValeurZoomBouton",vWD_dValeurZoomBouton);



// 	dbgAssertion(dValeurZoomBouton>0.0, "Le libellé ne correspond pas a la valeur du zoom")
if(WDAssert.isAssertionActive())WDAPIAssert.dbgAssertion(vWD_dValeurZoomBouton.opSup(0),"Le libellé ne correspond pas a la valeur du zoom");

// 	si dValeurZoomBouton<=0.0 ALORS RETOUR
if(vWD_dValeurZoomBouton.opInfEgal(0))
{
// 	si dValeurZoomBouton<=0.0 ALORS RETOUR
return;

}

// 	LOCAL 

// 	ChampZoom est un champ <- monchamp

vWD_ChampZoom.opPriseReference(this);


// 	MaFenêtre..Traitement[trtDemandeMiseAJour]+=DemandeMAJUI_BoutonZoom
WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_TRAITEMENT).get(234).opPlusEgal(fWDI_demandeMAJUI_BoutonZoom[0]);

// 	PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: Click BTN_Zoom100
 */
public void clicSurBoutonGauche()
// Modifie le zoom
// // Modifie le zoom
{
super.clicSurBoutonGauche();

// // Modifie le zoom

try
{
// ModifieZoom(dValeurZoomBouton)
fWD_modifieZoom(vWD_dValeurZoomBouton);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
 public WDObjet vWD_dValeurZoomBouton = WDVarNonAllouee.ref;
}
public GWDBTN_Zoom100 mWD_BTN_Zoom100;

/**
 * BTN_Zoom200
 */
class GWDBTN_Zoom200 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°18 de FEN_Mobile_Camera_UI.BTN_Zoom200
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2360891503547581134l);

super.setChecksum("2129959998");

super.setNom("BTN_Zoom200");

super.setType(4);

super.setBulle("");

super.setLibelle("2x");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(156, 222);

super.setTailleInitiale(48, 48);

super.setPlans(new int[]{1, 2});

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(18);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(-1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(true);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(1);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 4, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of BTN_Zoom200
 */
public void init()
// GLOBAL
{
super.init();

// GLOBAL

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
final WDObjet vWD_ChampZoom = new WDChampWL();



final WDProcedureInterne []fWDI_demandeMAJUI_BoutonZoom = new WDProcedureInterne[1];
fWDI_demandeMAJUI_BoutonZoom[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "DemandeMAJUI_BoutonZoom";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_demandeMAJUI_BoutonZoom();
}
// 		PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()
public void fWD_demandeMAJUI_BoutonZoom()
{
// 		PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()
initExecProcInterne();



try
{

try
{
// 		RetourHaptique()
fWD_retourHaptique();

// 		ChampZoom..Enfoncé = (CAM_Capture..Zoom=dValeurZoomBouton)
vWD_ChampZoom.setProp(EWDPropriete.PROP_ENFONCE,mWD_CAM_Capture.getProp(EWDPropriete.PROP_ZOOM).opEgal(vWD_dValeurZoomBouton, 0));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_demandeMAJUI_BoutonZoom[0]);

try
{
// GLOBAL

// 	dValeurZoomBouton est un numerique = val(MonChamp..Libellé)
vWD_dValeurZoomBouton = new WDNumerique(32, 6);

vWD_dValeurZoomBouton.setValeur(WDAPIChaine.val(this.getProp(EWDPropriete.PROP_LIBELLE)));

super.ajouterVariableGlobale("dValeurZoomBouton",vWD_dValeurZoomBouton);



// 	dbgAssertion(dValeurZoomBouton>0.0, "Le libellé ne correspond pas a la valeur du zoom")
if(WDAssert.isAssertionActive())WDAPIAssert.dbgAssertion(vWD_dValeurZoomBouton.opSup(0),"Le libellé ne correspond pas a la valeur du zoom");

// 	SI dValeurZoomBouton<=0.0 ALORS RETOUR
if(vWD_dValeurZoomBouton.opInfEgal(0))
{
// 	SI dValeurZoomBouton<=0.0 ALORS RETOUR
return;

}

// LOCAL 

// 	ChampZoom est un champ <- monchamp

vWD_ChampZoom.opPriseReference(this);


// 	MaFenêtre..Traitement[trtDemandeMiseAJour]+=DemandeMAJUI_BoutonZoom
WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_TRAITEMENT).get(234).opPlusEgal(fWDI_demandeMAJUI_BoutonZoom[0]);

// 		PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: Click BTN_Zoom200
 */
public void clicSurBoutonGauche()
// Modifie le zoom
// // Modifie le zoom
{
super.clicSurBoutonGauche();

// // Modifie le zoom

try
{
// ModifieZoom(dValeurZoomBouton)
fWD_modifieZoom(vWD_dValeurZoomBouton);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
 public WDObjet vWD_dValeurZoomBouton = WDVarNonAllouee.ref;
}
public GWDBTN_Zoom200 mWD_BTN_Zoom200;

/**
 * BTN_Zoom400
 */
class GWDBTN_Zoom400 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°19 de FEN_Mobile_Camera_UI.BTN_Zoom400
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2360891512137976525l);

super.setChecksum("2130420799");

super.setNom("BTN_Zoom400");

super.setType(4);

super.setBulle("");

super.setLibelle("4x");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(212, 222);

super.setTailleInitiale(48, 48);

super.setPlans(new int[]{1, 2});

super.setImageEtat(1);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(19);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(-1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(true);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(1);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -7.000000f, 2, 4, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff808080));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(31, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff808080), 2.000000, 2.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of BTN_Zoom400
 */
public void init()
// GLOBAL
{
super.init();

// GLOBAL

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
final WDObjet vWD_ChampZoom = new WDChampWL();



final WDProcedureInterne []fWDI_demandeMAJUI_BoutonZoom = new WDProcedureInterne[1];
fWDI_demandeMAJUI_BoutonZoom[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "DemandeMAJUI_BoutonZoom";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_demandeMAJUI_BoutonZoom();
}
// 	PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()
public void fWD_demandeMAJUI_BoutonZoom()
{
// 	PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()
initExecProcInterne();



try
{

try
{
// 		RetourHaptique()
fWD_retourHaptique();

// 		ChampZoom..Enfoncé = (CAM_Capture..Zoom=dValeurZoomBouton)
vWD_ChampZoom.setProp(EWDPropriete.PROP_ENFONCE,mWD_CAM_Capture.getProp(EWDPropriete.PROP_ZOOM).opEgal(vWD_dValeurZoomBouton, 0));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_demandeMAJUI_BoutonZoom[0]);

try
{
// GLOBAL

// 	dValeurZoomBouton est un numerique = val(MonChamp..Libellé)
vWD_dValeurZoomBouton = new WDNumerique(32, 6);

vWD_dValeurZoomBouton.setValeur(WDAPIChaine.val(this.getProp(EWDPropriete.PROP_LIBELLE)));

super.ajouterVariableGlobale("dValeurZoomBouton",vWD_dValeurZoomBouton);



// 	dbgAssertion(dValeurZoomBouton>0.0, "Le libellé ne correspond pas a la valeur du zoom")
if(WDAssert.isAssertionActive())WDAPIAssert.dbgAssertion(vWD_dValeurZoomBouton.opSup(0),"Le libellé ne correspond pas a la valeur du zoom");

// 	SI dValeurZoomBouton<=0.0 ALORS RETOUR
if(vWD_dValeurZoomBouton.opInfEgal(0))
{
// 	SI dValeurZoomBouton<=0.0 ALORS RETOUR
return;

}

// LOCAL 

// 	ChampZoom est un champ <- monchamp

vWD_ChampZoom.opPriseReference(this);


// 	MaFenêtre..Traitement[trtDemandeMiseAJour]+=DemandeMAJUI_BoutonZoom
WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_TRAITEMENT).get(234).opPlusEgal(fWDI_demandeMAJUI_BoutonZoom[0]);

// 	PROCÉDURE INTERNE DemandeMAJUI_BoutonZoom()

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: Click BTN_Zoom400
 */
public void clicSurBoutonGauche()
// Modifie le zoom
// // Modifie le zoom
{
super.clicSurBoutonGauche();

// // Modifie le zoom

try
{
// ModifieZoom(dValeurZoomBouton)
fWD_modifieZoom(vWD_dValeurZoomBouton);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
 public WDObjet vWD_dValeurZoomBouton = WDVarNonAllouee.ref;
}
public GWDBTN_Zoom400 mWD_BTN_Zoom400;

/**
 * IMG_FlashOff
 */
class GWDIMG_FlashOff extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°20 de FEN_Mobile_Camera_UI.IMG_FlashOff
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2372302989650932669l);

super.setChecksum("648666877");

super.setNom("IMG_FlashOff");

super.setType(8);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(1);

super.setPositionInitiale(-225, 0);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Automatique-Eclair-Flash-Alimentation-Desactive-Off_Mobile_16_1.svg");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(20);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(false);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_FlashOff mWD_IMG_FlashOff;

/**
 * IMG_FlashOn
 */
class GWDIMG_FlashOn extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°21 de FEN_Mobile_Camera_UI.IMG_FlashOn
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2372303036896173467l);

super.setChecksum("649267430");

super.setNom("IMG_FlashOn");

super.setType(8);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(1);

super.setPositionInitiale(-188, 0);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Automatique-Eclair-Flash-Alimentation-Active-On_Mobile_16_1.svg");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(21);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(false);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_FlashOn mWD_IMG_FlashOn;

/**
 * IMG_FlashAuto
 */
class GWDIMG_FlashAuto extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°22 de FEN_Mobile_Camera_UI.IMG_FlashAuto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2372303088436317292l);

super.setChecksum("649803715");

super.setNom("IMG_FlashAuto");

super.setType(8);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(1);

super.setPositionInitiale(-153, 0);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Automatique-Eclair-Flash-Alimentation_Mobile_16_1.svg");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(22);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(false);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_FlashAuto mWD_IMG_FlashAuto;

/**
 * LIB_ZOOM
 */
class GWDLIB_ZOOM extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°23 de FEN_Mobile_Camera_UI.LIB_ZOOM
////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
// Procédures utilisateur de FEN_Mobile_Camera_UI.LIB_ZOOM
////////////////////////////////////////////////////////////////////////////
// fixe le zoom, fait apparaitre le libellé si la valeur de zoom est différente de la valeur initiale
// procédure SetZoom(dZoom, LOCAL bRendVisibleSiNecessaire=vrai)
public void fWD_setZoom( final WDObjet vWD_dZoom , WDObjet _vWD_bRendVisibleSiNecessaire )
{
// procédure SetZoom(dZoom, LOCAL bRendVisibleSiNecessaire=vrai)
initExecProcLocale("SetZoom");



try
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_sNouveauLibelleZoom = new WDChaineU();



final WDObjet vWD_bRendVisibleSiNecessaire = WDParametre.traiterParametre(_vWD_bRendVisibleSiNecessaire, 2, 0, 0x1);


final WDProcedureInterne []fWDI_timerSys_Callback = new WDProcedureInterne[1];
fWDI_timerSys_Callback[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "TimerSys_Callback";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_timerSys_Callback();
}
// 	procédure interne TimerSys_Callback()
public void fWD_timerSys_Callback()
{
// 	procédure interne TimerSys_Callback()
initExecProcInterne();



try
{

try
{
// 		FinTimerSys(gnTimerInvisible)
WDAPITimer.finTimerSys(mWD_LIB_ZOOM.vWD_gnTimerInvisible.getInt());

// 		gnTimerInvisible=0
mWD_LIB_ZOOM.vWD_gnTimerInvisible.setValeur(0);

// 		MonChamp..VisibleAvecAnimation=Faux
mWD_LIB_ZOOM.setProp(EWDPropriete.PROP_VISIBLEAVECANIMATION,false);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_timerSys_Callback[0]);

try
{
// sNouveauLibelleZoom est une chaine = ChaîneConstruit("%1 x", Arrondi(dZoom,1))

vWD_sNouveauLibelleZoom.setValeur(WDAPIChaine.chaineConstruit(new WDChaineU("%1 x"),WDAPIMath.arrondi(vWD_dZoom,1)));


// si bRendVisibleSiNecessaire _et_ sNouveauLibelleZoom=MonChamp..Libellé ALORS
if((vWD_bRendVisibleSiNecessaire.getBoolean() && vWD_sNouveauLibelleZoom.opEgal(mWD_LIB_ZOOM.getProp(EWDPropriete.PROP_LIBELLE), 0)))
{
// 	bRendVisibleSiNecessaire=Faux
vWD_bRendVisibleSiNecessaire.setValeur(false);

}

// MonChamp..Libellé=sNouveauLibelleZoom
mWD_LIB_ZOOM.setProp(EWDPropriete.PROP_LIBELLE,vWD_sNouveauLibelleZoom);

// si bRendVisibleSiNecessaire alors
if(vWD_bRendVisibleSiNecessaire.getBoolean())
{
// 	MonChamp..VisibleAvecAnimation=vrai
mWD_LIB_ZOOM.setProp(EWDPropriete.PROP_VISIBLEAVECANIMATION,true);

// 	si gnTimerInvisible<>0 alors 
if(mWD_LIB_ZOOM.vWD_gnTimerInvisible.opDiff(0))
{
// 		FinTimerSys(gnTimerInvisible)
WDAPITimer.finTimerSys(mWD_LIB_ZOOM.vWD_gnTimerInvisible.getInt());

}

// 	gnTimerInvisible=TimerSys(TimerSys_Callback, 1s)
mWD_LIB_ZOOM.vWD_gnTimerInvisible.setValeur(WDAPITimer.timerSys(fWDI_timerSys_Callback[0],(new WDDuree("0000001000"))));

}

// 	procédure interne TimerSys_Callback()

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcLocale();

}
}
public void fWD_setZoom(WDObjet vWD_dZoom)
{
fWD_setZoom(vWD_dZoom, new WDBooleen(true));
}




public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2373758017218654448l);

super.setChecksum("671022751");

super.setNom("LIB_ZOOM");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("1x");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(120, 188);

super.setTailleInitiale(120, 23);

super.setPlans(new int[]{1, 2, 3});

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(false);

super.setAltitude(23);

super.setAncrageInitial(5, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -9.000000f, 2, 0, 1.000000f, 0.000000f), 3, 10, getCouleur_GEN(0xff68635f), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff4c4c4c), getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of LIB_ZOOM
 */
public void init()
// global
{
super.init();

// global

try
{
// global

// 	gnTimerInvisible est un entier	// timer pour rendre le libellé invisible
vWD_gnTimerInvisible = new WDEntier4();

super.ajouterVariableGlobale("gnTimerInvisible",vWD_gnTimerInvisible);



// LOCAL

// 	SetZoom(CAM_Capture..Zoom, faux)
fWD_setZoom(mWD_CAM_Capture.getProp(EWDPropriete.PROP_ZOOM),new WDBooleen(false));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
 public WDObjet vWD_gnTimerInvisible = WDVarNonAllouee.ref;
}
public GWDLIB_ZOOM mWD_LIB_ZOOM;

/**
 * IMG_Fermer
 */
class GWDIMG_Fermer extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°24 de FEN_Mobile_Camera_UI.IMG_Fermer
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2391683758939713628l);

super.setChecksum("601699921");

super.setNom("IMG_Fermer");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(32, 32);

super.setTailleInitiale(32, 32);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\CM_CroixBlanche.svg");

super.setPlans(new int[]{1, 2});

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(24);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click IMG_Fermer
 */
public void clicSurBoutonGauche()
// ferme
{
super.clicSurBoutonGauche();

// ferme

try
{
// ferme
WDAPIFenetre.ferme();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_Fermer mWD_IMG_Fermer;


////////////////////////////////////////////////////////////////////////////
// Procédures utilisateur de FEN_Mobile_Camera_UI
////////////////////////////////////////////////////////////////////////////
// Prend une photo à partir du champ caméra
// procédure PrendrePhoto()
public void fWD_prendrePhoto()
{
// procédure PrendrePhoto()
initExecProcLocale("PrendrePhoto");



try
{
final WDProcedureInterne []fWDI_surCapturePhoto = new WDProcedureInterne[1];
fWDI_surCapturePhoto[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "SurCapturePhoto";
}
@Override
public WDObjet executer(WDObjet... parametres)
{
verifNbParametres(parametres.length, 2);
return fWD_surCapturePhoto(parametres[0], parametres[1]);
}
// procédure interne SurCapturePhoto(bOK, sCheminPhoto)
public WDObjet fWD_surCapturePhoto( WDObjet vWD_bOK , WDObjet vWD_sCheminPhoto )
{
// procédure interne SurCapturePhoto(bOK, sCheminPhoto)
initExecProcInterne();



try
{

try
{
// 	si pas bOK ALORS
if((!vWD_bOK.getBoolean()))
{
// 		ToastAffiche("Erreur rencontrée lors de la prise de photo." + [RC] + Erreurinfo())
WDAPIToast.toastAffiche(new WDChaineU(WDChaineMultilangue.getString("Erreur rencontrée lors de la prise de photo.", "Error taking the photo.", "Error al tomar la foto.")
).opPlus(new WDChaineOptionnelle("\r\n")).opPlus(WDAPIVM.erreurInfo()));

// 		renvoyer faux
return new WDBooleen(false);

}

// 	gsChemin = sCheminPhoto
vWD_gsChemin.setValeur(vWD_sCheminPhoto);

// 	IMG_Aperçu=sCheminPhoto
mWD_IMG_Apercu.setValeur(vWD_sCheminPhoto);

// 	MaFenetre..Plan = PLAN_Aperçu_de_photo
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Apercu_de_photo);

// 	DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

// 	renvoyer vrai
return new WDBooleen(true);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return eCatch.getValeurRetour();
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_surCapturePhoto[0]);

try
{
// cameraphoto(CAM_Capture, SurCapturePhoto)
WDAPICamera.cameraPhoto(mWD_CAM_Capture,fWDI_surCapturePhoto[0]);

// procédure interne SurCapturePhoto(bOK, sCheminPhoto)

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcLocale();

}
}



// Démarre l'enregistrement d'une vidéo à partir du champ caméra
// procédure DémarrerEnregistrementVidéo()
public void fWD_demarrerEnregistrementVideo()
{
// procédure DémarrerEnregistrementVidéo()
initExecProcLocale("DémarrerEnregistrementVidéo");



try
{
final WDProcedureInterne []fWDI_surFinEnregistrementVideo = new WDProcedureInterne[1];
fWDI_surFinEnregistrementVideo[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "SurFinEnregistrementVidéo";
}
@Override
public WDObjet executer(WDObjet... parametres)
{
verifNbParametres(parametres.length, 2);
return fWD_surFinEnregistrementVideo(parametres[0], parametres[1]);
}
// PROCÉDURE INTERNE SurFinEnregistrementVidéo(bOK, sCheminVidéo)
public WDObjet fWD_surFinEnregistrementVideo( WDObjet vWD_bOK , WDObjet vWD_sCheminVideo )
{
// PROCÉDURE INTERNE SurFinEnregistrementVidéo(bOK, sCheminVidéo)
initExecProcInterne();



try
{

try
{
// 	SI PAS bOK ALORS
if((!vWD_bOK.getBoolean()))
{
// 		ToastAffiche("Erreur rencontrée lors de l'enregistrement de la vidéo." + [RC] + Erreurinfo())
WDAPIToast.toastAffiche(new WDChaineU(WDChaineMultilangue.getString("Erreur rencontrée lors de l'enregistrement de la vidéo.", "Error recording the video.", "Error al grabar el video.")
).opPlus(new WDChaineOptionnelle("\r\n")).opPlus(WDAPIVM.erreurInfo()));

// 		RENVOYER Faux
return new WDBooleen(false);

}

// 	gsChemin	= sCheminVidéo
vWD_gsChemin.setValeur(vWD_sCheminVideo);

// 	MM_Aperçu=gsChemin
mWD_MM_Apercu.setValeur(vWD_gsChemin);

// 	MaFenetre..Plan	= PLAN_Aperçu_de_vidéo
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Apercu_de_video);

// 	DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

// 	RENVOYER Vrai
return new WDBooleen(true);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return eCatch.getValeurRetour();
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_surFinEnregistrementVideo[0]);

try
{
// CaméraVidéoDémarre(CAM_Capture,SurFinEnregistrementVidéo)
WDAPICamera.cameraVideoDemarre(mWD_CAM_Capture,fWDI_surFinEnregistrementVideo[0]);

// PROCÉDURE INTERNE SurFinEnregistrementVidéo(bOK, sCheminVidéo)

// MaFenetre..Plan = PLAN_Enregistrement_en_cours
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Enregistrement_en_cours);

// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcLocale();

}
}



// procédure ModifieZoom(dValeurZoomBouton)
public void fWD_modifieZoom( WDObjet vWD_dValeurZoomBouton )
{
// procédure ModifieZoom(dValeurZoomBouton)
initExecProcLocale("ModifieZoom");



try
{

try
{
// si CAM_Capture..Zoom<>dValeurZoomBouton ALORS
if(mWD_CAM_Capture.getProp(EWDPropriete.PROP_ZOOM).opDiff(vWD_dValeurZoomBouton))
{
// 	CAM_Capture..Zoom=dValeurZoomBouton	
mWD_CAM_Capture.setProp(EWDPropriete.PROP_ZOOM,vWD_dValeurZoomBouton);

}

// LIB_ZOOM.SetZoom(CAM_Capture..Zoom)
mWD_LIB_ZOOM.fWD_setZoom(mWD_CAM_Capture.getProp(EWDPropriete.PROP_ZOOM));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcLocale();

}
}



// procédure RetourHaptique()
public void fWD_retourHaptique()
{
// procédure RetourHaptique()
initExecProcLocale("RetourHaptique");



try
{

try
{
// <COMPILE SI TypeConfiguration=Android>
// 	VibrationDéclenche(10ms)
WDAPIVibration.vibrationDeclenche((new WDDuree("0000000010")));


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcLocale();

}
}




/**
 * Traitement: Global declarations of FEN_Mobile_Camera_UI
 */
// PROCEDURE MaFenêtre(LOCAL bAutoriseVidéo = vrai)
public void declarerGlobale(WDObjet[] WD_tabParam)
{
// PROCEDURE MaFenêtre(LOCAL bAutoriseVidéo = vrai)
super.declarerGlobale(WD_tabParam, 0, 1);
int WD_ntabParamLen = 0;
if(WD_tabParam!=null) WD_ntabParamLen = WD_tabParam.length;

// Traitement du paramètre n°0
if(0<WD_ntabParamLen) 
{
vWD_bAutoriseVideo = WD_tabParam[0];
}
else { vWD_bAutoriseVideo = new WDBooleen(true); }



vWD_bAutoriseVideo = WDParametre.traiterParametre(vWD_bAutoriseVideo, 1, 0, 0x1);

super.ajouterVariableGlobale("bAutoriseVidéo",vWD_bAutoriseVideo);



try
{
// CAM_Capture..Caméra=camCaméraDorsale
mWD_CAM_Capture.setProp(EWDPropriete.PROP_CAMERA,1);

// CAM_Capture..Zoom=1
mWD_CAM_Capture.setProp(EWDPropriete.PROP_ZOOM,1);

// CAM_Capture..Flash=camFlashAuto
mWD_CAM_Capture.setProp(EWDPropriete.PROP_FLASH,2);

// CAM_Capture..Torche=camTorcheOff
mWD_CAM_Capture.setProp(EWDPropriete.PROP_TORCHE,0);

// mafenetre..Plan=PLAN_Capture_Photo
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,mWD_PLAN_Capture_Photo);

// IMG_ModeVidéo..Visible = bAutoriseVidéo
mWD_IMG_ModeVideo.setProp(EWDPropriete.PROP_VISIBLE,vWD_bAutoriseVideo);

// gnTimerVidéoTempsEcoulé est un entier = -1
vWD_gnTimerVideoTempsEcoule = new WDEntier4();

vWD_gnTimerVideoTempsEcoule.setValeur(-1);

super.ajouterVariableGlobale("gnTimerVidéoTempsEcoulé",vWD_gnTimerVideoTempsEcoule);



// gsChemin est une chaîne
vWD_gsChemin = new WDChaineU();

super.ajouterVariableGlobale("gsChemin",vWD_gsChemin);



}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: End of initialization of FEN_Mobile_Camera_UI
 */
public void init()
// Initialise l'UI sur le 1er affichage
// les différents éléments de l'UI ont automatiquement ajouté des procédures interne pour se mettre a jour
// // Initialise l'UI sur le 1er affichage
{
super.init();

// // Initialise l'UI sur le 1er affichage

try
{
// DemandeMiseAJourUI()
WDAPIVM.demandeMiseAJourUI();

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: Request for refreshing the display of FEN_Mobile_Camera_UI
 */
public void demandeMAJAffichage()
// Calcul du temps écoulé dans un enregistrement vidéo
// // Calcul du temps écoulé dans un enregistrement vidéo
{
super.demandeMAJAffichage();

// // Calcul du temps écoulé dans un enregistrement vidéo

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
final WDObjet vWD_dhDebutEnregistrement = new WDDateHeure();



final WDProcedureInterne []fWDI_surTempsEcoule = new WDProcedureInterne[1];
fWDI_surTempsEcoule[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "SurTempsEcoulé";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_surTempsEcoule();
}
// 	 	procédure interne SurTempsEcoulé
public void fWD_surTempsEcoule()
{
// 	 	procédure interne SurTempsEcoulé
initExecProcInterne();



try
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_dhMaintenant = new WDDateHeure();

WDObjet vWD_dureeEcoulee = new WDDuree();




try
{
// 	 		dhMaintenant est une dateheure


// 	 		duréeEcoulée est une durée = dhMaintenant - dhDébutEnregistrement		

vWD_dureeEcoulee.setValeur(vWD_dhMaintenant.opMoins(vWD_dhDebutEnregistrement));


// 	 		LIB_TempsEcoulé = DuréeVersChaîne(duréeEcoulée,"HH:MM:SS")
mWD_LIB_TempsEcoule.setValeur(WDAPIDate.dureeVersChaine(vWD_dureeEcoulee,new WDChaineU("HH:MM:SS")));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_surTempsEcoule[0]);

try
{
// dhDébutEnregistrement est une DateHeure


// si MaFenetre..Plan = PLAN_Enregistrement_en_cours ALORS
if(WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_PLAN).opEgal(mWD_PLAN_Enregistrement_en_cours, 0))
{
// 	si gnTimerVidéoTempsEcoulé = -1 ALORS		
if(vWD_gnTimerVideoTempsEcoule.opEgal(-1, 0))
{
// 	 	gnTimerVidéoTempsEcoulé = TimerSys(SurTempsEcoulé,250ms)
vWD_gnTimerVideoTempsEcoule.setValeur(WDAPITimer.timerSys(fWDI_surTempsEcoule[0],(new WDDuree("0000000250"))));

// 	 	procédure interne SurTempsEcoulé

// 	 	SurTempsEcoulé()
fWDI_surTempsEcoule[0].executer_void();

}

}
else if(vWD_gnTimerVideoTempsEcoule.opSup(-1))
{
// 	FinTimerSys(gnTimerVidéoTempsEcoulé)
WDAPITimer.finTimerSys(vWD_gnTimerVideoTempsEcoule.getInt());

// 	gnTimerVidéoTempsEcoulé = -1
vWD_gnTimerVideoTempsEcoule.setValeur(-1);

}

// Si CAM_Capture..Torche=camTorcheOn _et_ MaFenêtre..Plan<>PLAN_Capture_Vidéo _et_ MaFenêtre..Plan<>PLAN_Enregistrement_en_cours  alors
if(((mWD_CAM_Capture.getProp(EWDPropriete.PROP_TORCHE).opEgal(1, 0) && WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_PLAN).opDiff(mWD_PLAN_Capture_Video)) && WDContexte.getMaFenetre().getProp(EWDPropriete.PROP_PLAN).opDiff(mWD_PLAN_Enregistrement_en_cours)))
{
// 	CAM_Capture..Torche=camTorcheOff
mWD_CAM_Capture.setProp(EWDPropriete.PROP_TORCHE,0);

}

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




/**
 * Traitement: Close FEN_Mobile_Camera_UI
 */
public void fermetureFenetre()
// arret de la torche quoi qu'il arrive
// // arret de la torche quoi qu'il arrive
{
super.fermetureFenetre();

// // arret de la torche quoi qu'il arrive

try
{
// CAM_Capture..Torche=camTorcheOff
mWD_CAM_Capture.setProp(EWDPropriete.PROP_TORCHE,0);

// renvoyer gsChemin
setValeurRenvoyee(vWD_gsChemin);
return;

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
 public WDObjet vWD_bAutoriseVideo = WDVarNonAllouee.ref;
 public WDObjet vWD_gnTimerVideoTempsEcoule = WDVarNonAllouee.ref;
 public WDObjet vWD_gsChemin = WDVarNonAllouee.ref;
////////////////////////////////////////////////////////////////////////////
// Création des champs de la fenêtre FEN_Mobile_Camera_UI
////////////////////////////////////////////////////////////////////////////
protected void creerChamps()
{
mWD_LIB_Fond = new GWDLIB_Fond();
mWD_CAM_Capture = new GWDCAM_Capture();
mWD_IMG_ModeVideo = new GWDIMG_ModeVideo();
mWD_LIB_TempsEcoule = new GWDLIB_TempsEcoule();
mWD_IMG_ModePhoto = new GWDIMG_ModePhoto();
mWD_IMG_Flash = new GWDIMG_Flash();
mWD_IMG_Torche = new GWDIMG_Torche();
mWD_IMG_ChangerCamera = new GWDIMG_ChangerCamera();
mWD_IMG_ActionPhoto = new GWDIMG_ActionPhoto();
mWD_IMG_ActionVideo = new GWDIMG_ActionVideo();
mWD_IMG_ArretEnregistrement = new GWDIMG_ArretEnregistrement();
mWD_MM_Apercu = new GWDMM_Apercu();
mWD_BTN_ReprendreVideo = new GWDBTN_ReprendreVideo();
mWD_IMG_Apercu = new GWDIMG_Apercu();
mWD_BTN_ReprendrePhoto = new GWDBTN_ReprendrePhoto();
mWD_BTN_OK = new GWDBTN_OK();
mWD_BTN_Zoom100 = new GWDBTN_Zoom100();
mWD_BTN_Zoom200 = new GWDBTN_Zoom200();
mWD_BTN_Zoom400 = new GWDBTN_Zoom400();
mWD_IMG_FlashOff = new GWDIMG_FlashOff();
mWD_IMG_FlashOn = new GWDIMG_FlashOn();
mWD_IMG_FlashAuto = new GWDIMG_FlashAuto();
mWD_LIB_ZOOM = new GWDLIB_ZOOM();
mWD_IMG_Fermer = new GWDIMG_Fermer();

}
////////////////////////////////////////////////////////////////////////////
// Initialisation de la fenêtre FEN_Mobile_Camera_UI
////////////////////////////////////////////////////////////////////////////
public  void initialiserObjet()
{
super.setQuid(2360771700186374419l);

super.setChecksum("-1704529098");

super.setNom("FEN_Mobile_Camera_UI");

super.setType(1);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setCurseurSouris(0);

super.setNote("", "");

super.setCouleur(getCouleur_GEN(0xff000000));

super.setCouleurFond(getCouleur_GEN(0xffffffff));

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 360);

super.setTitre("Mobile Camera UI");

super.setTailleMin(-1, -1);

super.setTailleMax(20000, 20000);

super.setVisibleInitial(true);

super.setPositionFenetre(3);

super.setPersistant(true);

super.setGFI(true);

super.setAnimationFenetre(0);

super.setImageFond("", 1, 0, 1);

super.setCouleurTexteAutomatique(getCouleur_GEN(0xf4000000, true));

super.setCouleurBarreSysteme(getCouleur_GEN(0xff000001, true));

super.setCopieEcranAutorisee(true);

super.setAncrageAuContenu(0);


activerEcoute();

////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de FEN_Mobile_Camera_UI
////////////////////////////////////////////////////////////////////////////
mWD_CAM_Capture.initialiserObjet();
super.ajouter("CAM_Capture", mWD_CAM_Capture);
mWD_LIB_Fond.initialiserObjet();
super.ajouter("LIB_Fond", mWD_LIB_Fond);
mWD_IMG_ModeVideo.initialiserObjet();
super.ajouter("IMG_ModeVidéo", mWD_IMG_ModeVideo);
mWD_LIB_TempsEcoule.initialiserObjet();
super.ajouter("LIB_TempsEcoulé", mWD_LIB_TempsEcoule);
mWD_IMG_ModePhoto.initialiserObjet();
super.ajouter("IMG_ModePhoto", mWD_IMG_ModePhoto);
mWD_IMG_Flash.initialiserObjet();
super.ajouter("IMG_Flash", mWD_IMG_Flash);
mWD_IMG_Torche.initialiserObjet();
super.ajouter("IMG_Torche", mWD_IMG_Torche);
mWD_IMG_ChangerCamera.initialiserObjet();
super.ajouter("IMG_ChangerCaméra", mWD_IMG_ChangerCamera);
mWD_IMG_ActionPhoto.initialiserObjet();
super.ajouter("IMG_ActionPhoto", mWD_IMG_ActionPhoto);
mWD_IMG_ActionVideo.initialiserObjet();
super.ajouter("IMG_ActionVidéo", mWD_IMG_ActionVideo);
mWD_IMG_ArretEnregistrement.initialiserObjet();
super.ajouter("IMG_ArrêtEnregistrement", mWD_IMG_ArretEnregistrement);
mWD_MM_Apercu.initialiserObjet();
super.ajouter("MM_Aperçu", mWD_MM_Apercu);
mWD_BTN_ReprendreVideo.initialiserObjet();
super.ajouter("BTN_ReprendreVidéo", mWD_BTN_ReprendreVideo);
mWD_IMG_Apercu.initialiserObjet();
super.ajouter("IMG_Aperçu", mWD_IMG_Apercu);
mWD_BTN_ReprendrePhoto.initialiserObjet();
super.ajouter("BTN_ReprendrePhoto", mWD_BTN_ReprendrePhoto);
mWD_BTN_OK.initialiserObjet();
super.ajouter("BTN_OK", mWD_BTN_OK);
mWD_BTN_Zoom100.initialiserObjet();
super.ajouter("BTN_Zoom100", mWD_BTN_Zoom100);
mWD_BTN_Zoom200.initialiserObjet();
super.ajouter("BTN_Zoom200", mWD_BTN_Zoom200);
mWD_BTN_Zoom400.initialiserObjet();
super.ajouter("BTN_Zoom400", mWD_BTN_Zoom400);
mWD_IMG_FlashOff.initialiserObjet();
super.ajouter("IMG_FlashOff", mWD_IMG_FlashOff);
mWD_IMG_FlashOn.initialiserObjet();
super.ajouter("IMG_FlashOn", mWD_IMG_FlashOn);
mWD_IMG_FlashAuto.initialiserObjet();
super.ajouter("IMG_FlashAuto", mWD_IMG_FlashAuto);
mWD_LIB_ZOOM.initialiserObjet();
super.ajouter("LIB_ZOOM", mWD_LIB_ZOOM);
mWD_IMG_Fermer.initialiserObjet();
super.ajouter("IMG_Fermer", mWD_IMG_Fermer);

super.terminerInitialisation();
}

////////////////////////////////////////////////////////////////////////////
public boolean isUniteAffichageLogique()
{
return false;
}

public WDProjet getProjet()
{
return GWDPintegrationSdk.getInstance();
}

public IWDEnsembleElement getEnsemble()
{
return GWDPintegrationSdk.getInstance();
}
public int getModeContexteHF()
{
return 1;
}
/**
* Retourne le mode d'affichage de l'ActionBar de la fenêtre.
*/
public int getModeActionBar()
{
return 0;
}
/**
* Retourne vrai si la fenêtre est maximisée, faux sinon.
*/
public boolean isMaximisee()
{
return true;
}
/**
* Retourne vrai si la fenêtre a une barre de titre, faux sinon.
*/
public boolean isAvecBarreDeTitre()
{
return false;
}
/**
* Retourne le mode d'affichage de la barre système de la fenêtre.
*/
public int getModeBarreSysteme()
{
return 0;
}
/**
* Retourne vrai si la fenêtre est munie d'ascenseurs automatique, faux sinon.
*/
public boolean isAvecAscenseurAuto()
{
return true;
}
/**
* Retourne Vrai si on doit appliquer un theme "dark" (sombre) ou Faux si on doit appliquer "light" (clair) à la fenêtre.
* Ce choix se base sur la couleur du libellé par défaut dans le gabarit de la fenêtre.
*/
public boolean isGabaritSombre()
{
return false;
}
public boolean isIgnoreModeNuit()
{
return true;
}
/**
* Retourne vrai si l'option de masquage automatique de l'ActionBar lorsqu'on scrolle dans un champ de la fenêtre a été activée.
*/
public boolean isMasquageAutomatiqueActionBar()
{
return false;
}
public static class WDActiviteFenetre extends WDActivite
{
protected WDFenetre getFenetre()
{
return GWDPintegrationSdk.getInstance().mWD_FEN_Mobile_Camera_UI;
}
}
/**
* Retourne le nom du gabarit associée à la fenêtre.
*/
public String getNomGabarit()
{
return "250 PHOENIX#WM";
}
/**
* Retourne le nom de la palette associe a la fenetre.
*/
public String getNomPalette()
{
return "";
}
}
