// CryptoID Mobile Application - Admin Login Window
// WLanguage code for WinDev Mobile

// Admin login window initialization
PROCEDURE WIN_AdminLogin_Initialize()
	// Clear input fields
	EDT_Username = ""
	EDT_Password = ""
	
	// Set focus to username field
	SetFocus(EDT_Username)
	
	// Configure window
	WIN_AdminLogin..Title = "Admin Login"
END

// Login button click event
PROCEDURE BTN_Login_Click()
	LOCAL sUsername AS STRING
	LOCAL sPassword AS STRING
	LOCAL nAdminID AS INT
	
	// Get input values
	sUsername = EDT_Username
	sPassword = EDT_Password
	
	// Validate inputs
	IF sUsername = "" THEN
		Error("Please enter username")
		SetFocus(EDT_Username)
		RETURN
	END
	
	IF sPassword = "" THEN
		Error("Please enter password")
		SetFocus(EDT_Password)
		RETURN
	END
	
	// Authenticate admin
	nAdminID = AuthenticateAdmin(sUsername, sPassword)
	
	IF nAdminID > 0 THEN
		// Login successful
		gsCurrentAdmin = sUsername
		gnCurrentAdminID = nAdminID
		gbIsAdminLoggedIn = True
		
		// Update last login time
		UpdateLastLogin(nAdminID)
		
		// Close login window and open category selection
		Close()
		Open(WIN_IDCategorySelection)
	ELSE
		// Login failed
		Error("Invalid username or password")
		EDT_Password = ""
		SetFocus(EDT_Username)
	END
END

// Cancel button click event
PROCEDURE BTN_Cancel_Click()
	Close()
END

// Authenticate admin function
PROCEDURE AuthenticateAdmin(sUsername AS STRING, sPassword AS STRING) : INT
	LOCAL nAdminID AS INT = 0
	LOCAL sQuery AS STRING
	
	// In production, password should be hashed and compared with stored hash
	sQuery = StringBuild("SELECT AdminID FROM AdminUsers WHERE Username = '%1' AND Password = '%2' AND IsActive = 1", sUsername, sPassword)
	
	IF HExecuteQuery("QRY_AdminAuth", sQuery) THEN
		IF HReadFirst("QRY_AdminAuth") THEN
			nAdminID = QRY_AdminAuth.AdminID
		END
	END
	
	RESULT nAdminID
END

// Update last login time
PROCEDURE UpdateLastLogin(nAdminID AS INT)
	LOCAL sQuery AS STRING
	
	sQuery = StringBuild("UPDATE AdminUsers SET LastLogin = '%1' WHERE AdminID = %2", 
		DateTimeToString(DateTimeSys(), "YYYY-MM-DD HH:MM:SS"), nAdminID)
	
	HExecuteQuery("QRY_UpdateLogin", sQuery)
END

// Handle Enter key press for quick login
PROCEDURE EDT_Password_Exit()
	// If Enter is pressed in password field, attempt login
	BTN_Login_Click()
END
