/**
 * Code generated by WINDEV Mobile - DO NOT MODIFY!
 * WINDEV Mobile object: Fenêtre
 * Android class: run
 * Version of wdjava64.dll: 30.0.302.8
 */


package com.amlacameroon.sandbox.wdgen;


import com.amlacameroon.sandbox.*;
import fr.pcsoft.wdjava.core.types.*;
import fr.pcsoft.wdjava.core.*;
import fr.pcsoft.wdjava.ui.champs.fenetre.*;
import fr.pcsoft.wdjava.ui.champs.bouton.*;
import fr.pcsoft.wdjava.ui.cadre.*;
import fr.pcsoft.wdjava.core.erreur.*;
import fr.pcsoft.wdjava.api.*;
import fr.pcsoft.wdjava.core.application.*;
import fr.pcsoft.wdjava.core.context.*;
import fr.pcsoft.wdjava.ui.champs.image.*;
import fr.pcsoft.wdjava.ui.champs.saisie.*;
import fr.pcsoft.wdjava.core.types.collection.tableau.*;
import fr.pcsoft.wdjava.core.parcours.*;
import fr.pcsoft.wdjava.core.parcours.chaine.*;
import fr.pcsoft.wdjava.ui.champs.libelle.*;
import fr.pcsoft.wdjava.ui.champs.fenetreinterne.*;
import fr.pcsoft.wdjava.ui.champs.combo.*;
import fr.pcsoft.wdjava.ui.champs.zr.*;
import fr.pcsoft.wdjava.net.http.*;
import fr.pcsoft.wdjava.core.poo.*;
import fr.pcsoft.wdjava.json.*;
import fr.pcsoft.wdjava.core.application.executor.*;
import fr.pcsoft.wdjava.ui.actionbar.*;
import fr.pcsoft.wdjava.ui.activite.*;
/*Imports trouvés dans le code WL*/
/*Fin Imports trouvés dans le code WL*/



public class GWDFrun extends WDFenetre
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs de run
////////////////////////////////////////////////////////////////////////////

/**
 * Simple
 */
class GWDSimple extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de run.Simple
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2633563733025415517l);

super.setChecksum("887174336");

super.setNom("Simple");

super.setType(4);

super.setBulle("");

super.setLibelle("Get T5AirSnap SDK Version");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(74, 48);

super.setTailleInitiale(213, 48);

super.setPlan(1);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple
 */
public void clicSurBoutonGauche()
// <compile si Configuration="Application Android">
{
super.clicSurBoutonGauche();

// <compile si Configuration="Application Android">

try
{
// <compile si Configuration="Application Android">
// 	testSdk()
GWDCPSDKInt.testSdk();


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple mWD_Simple;

/**
 * OuvreCaméra
 */
class GWDOuvreCamera extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de run.OuvreCaméra
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2633604380415674792l);

super.setChecksum("706953731");

super.setNom("OuvreCaméra");

super.setType(4);

super.setBulle("");

super.setLibelle("Cliquer pour accorder les permissions requises");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(8, 593);

super.setTailleInitiale(340, 48);

super.setPlan(1);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(7);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 1, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click OuvreCaméra
 */
public void clicSurBoutonGauche()
// Version 1
// Description 
// Champ caméra
// Autorise ou non la prise de vidéo
// // Version 1
{
super.clicSurBoutonGauche();

// // Version 1

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_bAutoriseVideo = new WDBooleen();



final WDProcedureInterne []fWDI_apresCapture = new WDProcedureInterne[1];
fWDI_apresCapture[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "AprèsCapture";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 1);
fWD_apresCapture(parametres[0]);
}
// procédure interne AprèsCapture(sCheminComplet<utile>)
public void fWD_apresCapture( WDObjet vWD_sCheminComplet )
{
// procédure interne AprèsCapture(sCheminComplet<utile>)
initExecProcInterne();



try
{

try
{
// 	si sCheminComplet<>"" ALORS		
if(vWD_sCheminComplet.opDiff(""))
{
// 		ToastAffiche("Enregistrement effectué.")		
WDAPIToast.toastAffiche(new WDChaineU("Enregistrement effectué."));

}

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}


};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_apresCapture[0]);

try
{
// soit bAutoriseVidéo = vrai

vWD_bAutoriseVideo.setValeur(true);


// OuvreAsynchrone(FEN_Mobile_Camera_UI,(bAutoriseVidéo),AprèsCapture)
WDAPIFenetre.ouvreAsynchrone(GWDPintegrationSdk.getInstance().mWD_FEN_Mobile_Camera_UI,new WDObjet[]{vWD_bAutoriseVideo},fWDI_apresCapture[0]);

// procédure interne AprèsCapture(sCheminComplet<utile>)

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDOuvreCamera mWD_OuvreCamera;

/**
 * Simple3
 */
class GWDSimple3 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°3 de run.Simple3
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2637215094910758387l);

super.setChecksum("621640251");

super.setNom("Simple3");

super.setType(4);

super.setBulle("");

super.setLibelle("Create Cryptographe");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(74, 116);

super.setTailleInitiale(213, 48);

super.setPlan(1);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(3);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(2);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple3
 */
public void clicSurBoutonGauche()
// MaFenêtre..Plan = 3
{
super.clicSurBoutonGauche();

// MaFenêtre..Plan = 3

try
{
// MaFenêtre..Plan = 3
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,3);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple3 mWD_Simple3;

/**
 * Simple4
 */
class GWDSimple4 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°4 de run.Simple4
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2637215107795794332l);

super.setChecksum("621774311");

super.setNom("Simple4");

super.setType(4);

super.setBulle("");

super.setLibelle("Test SDKS");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(74, 184);

super.setTailleInitiale(213, 48);

super.setPlan(1);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(4);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(3);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple4
 */
public void clicSurBoutonGauche()
// <compile si Configuration="Application Android">
{
super.clicSurBoutonGauche();

// <compile si Configuration="Application Android">

try
{
// <compile si Configuration="Application Android">
// MaFenêtre..plan = 2
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,2);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple4 mWD_Simple4;

/**
 * Image1
 */
class GWDImage1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°5 de run.Image1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2637331707610233112l);

super.setChecksum("664090255");

super.setNom("Image1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(16, 346);

super.setTailleInitiale(69, 49);

super.setValeurInitiale("https://my.kyvala.com/img/crypto-Test.PNG");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(5);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDImage1 mWD_Image1;

/**
 * TexteInterne
 */
class GWDTexteInterne extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°6 de run.TexteInterne
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,236,21);
super.setRectCompPrincipal(0,21,236,40);

super.setQuid(2637335195129404992l);

super.setChecksum("669815859");

super.setNom("TexteInterne");

super.setType(20001);

super.setBulle("");

super.setLibelle("Texte");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(62, 413);

super.setTailleInitiale(236, 63);

super.setValeurInitiale("");

super.setPlan(1);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(6);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(6);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of TexteInterne
 */
public void init()
// Version 1
// Description 
// Saisie de texte
// MoiMême = Image1
// // Version 1
{
super.init();

// // Version 1

try
{
// MoiMême = cryptoPath
WDContexte.getMoiMeme().setValeur(vWD_cryptoPath);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDTexteInterne mWD_TexteInterne;

/**
 * Image2
 */
class GWDImage2 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°7 de run.Image2
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2637600400723769821l);

super.setChecksum("623651764");

super.setNom("Image2");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(12, 356);

super.setTailleInitiale(69, 49);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\crypto Test JUMP.png");

super.setPlan(1);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(7);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDImage2 mWD_Image2;

/**
 * BTN_Prise_empreinte_pouce_droit
 */
class GWDBTN_Prise_empreinte_pouce_droit extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°8 de run.BTN_Prise_empreinte_pouce_droit
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2645752600938192167l);

super.setChecksum("724844096");

super.setNom("BTN_Prise_empreinte_pouce_droit");

super.setType(4);

super.setBulle("");

super.setLibelle("Right Thumb");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 571);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(8);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(14);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_pouce_droit
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_pouce_droit..Visible = Vrai
mWD_IMG_validation_pouce_droit.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinte = Vrai
vWD_gbIsEmpreinte.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_pouce_droit mWD_BTN_Prise_empreinte_pouce_droit;

/**
 * BTN_Prise_empreinte_main_droite
 */
class GWDBTN_Prise_empreinte_main_droite extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°9 de run.BTN_Prise_empreinte_main_droite
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2645752600938323271l);

super.setChecksum("724975200");

super.setNom("BTN_Prise_empreinte_main_droite");

super.setType(4);

super.setBulle("");

super.setLibelle("Right Hand");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 513);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(9);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(13);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_main_droite
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_main_droite..Visible = vrai
mWD_IMG_validation_main_droite.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinte = Vrai
vWD_gbIsEmpreinte.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_main_droite mWD_BTN_Prise_empreinte_main_droite;

/**
 * BTN_Prise_empreinte_pouce_gauche
 */
class GWDBTN_Prise_empreinte_pouce_gauche extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°10 de run.BTN_Prise_empreinte_pouce_gauche
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2645752600938454358l);

super.setChecksum("725106287");

super.setNom("BTN_Prise_empreinte_pouce_gauche");

super.setType(4);

super.setBulle("");

super.setLibelle("Left Thumb");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 457);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(10);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(12);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_pouce_gauche
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_pouce_gauche..Visible = Vrai
mWD_IMG_validation_pouce_gauche.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinte = Vrai
vWD_gbIsEmpreinte.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_pouce_gauche mWD_BTN_Prise_empreinte_pouce_gauche;

/**
 * IMG_validation_main_droite
 */
class GWDIMG_validation_main_droite extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°11 de run.IMG_validation_main_droite
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2645752600938585446l);

super.setChecksum("725239199");

super.setNom("IMG_validation_main_droite");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 520);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(11);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_main_droite mWD_IMG_validation_main_droite;

/**
 * IMG_validation_pouce_droit
 */
class GWDIMG_validation_pouce_droit extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°12 de run.IMG_validation_pouce_droit
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2645752600938847605l);

super.setChecksum("725501358");

super.setNom("IMG_validation_pouce_droit");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 577);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(12);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_pouce_droit mWD_IMG_validation_pouce_droit;

/**
 * IMG_validation_pouce_gauche
 */
class GWDIMG_validation_pouce_gauche extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°13 de run.IMG_validation_pouce_gauche
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2645752600939109765l);

super.setChecksum("725763518");

super.setNom("IMG_validation_pouce_gauche");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 464);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(13);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_pouce_gauche mWD_IMG_validation_pouce_gauche;

/**
 * BTN_Prise_empreinte_main_gauche
 */
class GWDBTN_Prise_empreinte_main_gauche extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°14 de run.BTN_Prise_empreinte_main_gauche
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2645752600939371925l);

super.setChecksum("726023854");

super.setNom("BTN_Prise_empreinte_main_gauche");

super.setType(4);

super.setBulle("");

super.setLibelle("Left Hand");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 399);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(14);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(11);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_main_gauche
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_main_gauche..Visible = vrai
mWD_IMG_validation_main_gauche.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinte = Vrai
vWD_gbIsEmpreinte.setValeur(true);

// 	StartFingerCaptureLeftHand(gsRefCapture)
GWDCPSDKInt.StartFingerCaptureLeftHand(vWD_gsRefCapture.getString());


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_main_gauche mWD_BTN_Prise_empreinte_main_gauche;

/**
 * IMG_validation_main_gauche
 */
class GWDIMG_validation_main_gauche extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°15 de run.IMG_validation_main_gauche
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2645752600939503012l);

super.setChecksum("726156765");

super.setNom("IMG_validation_main_gauche");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 406);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(15);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_main_gauche mWD_IMG_validation_main_gauche;

/**
 * BTN_Selfie_identite
 */
class GWDBTN_Selfie_identite extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°16 de run.BTN_Selfie_identite
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2645752600939765188l);

super.setChecksum("726417117");

super.setNom("BTN_Selfie_identite");

super.setType(4);

super.setBulle("");

super.setLibelle("SELFIE D'IDENTITE FRONT");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(20, 341);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(16);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(10);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Selfie_identite
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_valider_selfie..Visible	= Vrai
mWD_IMG_valider_selfie.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsSelfie = Vrai
vWD_gbIsSelfie.setValeur(true);

// 	StartFaceSelfieCapture(gsRefCapture)
GWDCPSDKInt.StartFaceSelfieCapture(vWD_gsRefCapture.getString());


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Selfie_identite mWD_BTN_Selfie_identite;

/**
 * IMG_valider_selfie
 */
class GWDIMG_valider_selfie extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°17 de run.IMG_valider_selfie
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2645752600939896275l);

super.setChecksum("726550028");

super.setNom("IMG_valider_selfie");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 348);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(17);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_valider_selfie mWD_IMG_valider_selfie;

/**
 * Simple5
 */
class GWDSimple5 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°18 de run.Simple5
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2645753404155066646l);

super.setChecksum("782834410");

super.setNom("Simple5");

super.setType(4);

super.setBulle("");

super.setLibelle("Decode cryptographe");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(184, 826);

super.setTailleInitiale(161, 42);

super.setPlan(2);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(18);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(19);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple5
 */
public void clicSurBoutonGauche()
// <compile si Configuration="Application Android">
{
super.clicSurBoutonGauche();

// <compile si Configuration="Application Android">

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_pos1 = new WDChaineU();

WDObjet vWD_pos2 = new WDChaineU();

WDObjet vWD_pos3 = new WDChaineU();

WDObjet vWD_pos4 = new WDChaineU();

WDObjet vWD_pos5 = new WDChaineU();

WDObjet vWD_pos6 = new WDChaineU();

WDObjet vWD_pos7 = new WDChaineU();

WDObjet vWD_pos8 = new WDChaineU();

WDObjet vWD_pos9 = new WDChaineU();

WDObjet vWD_pos10 = new WDChaineU();

WDObjet vWD_sGsAi_selfie = new WDChaineU();

WDObjet vWD_sInfoRepertoire = new WDChaineU();




try
{
// <compile si Configuration="Application Android">
// 	pos1, pos2, pos3, pos4, pos5, pos6, pos7, pos8, pos9, pos10, sGsAi_selfie, sInfoRépertoire						est une chaîne













// 	sInfoRépertoire	= SysRepStockageExterne(0,sseAppDocument)
vWD_sInfoRepertoire.setValeur(WDAPIStorage.sysRepStockageExterne(0,6));

// 	sInfoRépertoire	= ChaîneSupprime(sInfoRépertoire, [fSep()]+"Documents")
vWD_sInfoRepertoire.setValeur(WDAPIChaine.chaineSupprime(vWD_sInfoRepertoire,new WDChaineOptionnelle(WDAPIFichier.fSep()).opPlus("Documents")));

// 	sInfoRépertoire	= sInfoRépertoire+[fSep()]
vWD_sInfoRepertoire.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())));

// 	SI fRepExiste(sInfoRépertoire) ALORS
if(WDAPIFichier.fRepertoireExiste(vWD_sInfoRepertoire.getString()).getBoolean())
{
// 		UnFichier, ResListeFichier sont des chaînes
WDObjet vWD_UnFichier = new WDChaineU();


WDObjet vWD_ResListeFichier = new WDChaineU();



// 		ResListeFichier = fListeFichier(sInfoRépertoire+[fSep()]+"*.png")
vWD_ResListeFichier.setValeur(WDAPIFichier.fListeFichier(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus("*.png").getString()));

// 		sPrefixNomFichierEmpreinte	est une chaîne
WDObjet vWD_sPrefixNomFichierEmpreinte = new WDChaineU();



// 		sPrefixNomFichierFace		est une chaîne
WDObjet vWD_sPrefixNomFichierFace = new WDChaineU();



// 		POUR TOUTE CHAÎNE UnFichier DE ResListeFichier SÉPARÉE PAR RC
IWDParcours parcours1 = null;
try
{
parcours1 = WDParcoursSousChaine.pourTout(vWD_UnFichier, null, null, vWD_ResListeFichier, "\r\n", 0x2);
while(parcours1.testParcours())
{
// 			sNomSansExtension	est une chaîne	= ExtraitChaîne(UnFichier, 1, ".png")
WDObjet vWD_sNomSansExtension = new WDChaineU();


vWD_sNomSansExtension.setValeur(WDAPIChaine.extraitChaine(parcours1.getVariableParcours(),1,new WDChaineU(".png")));


// 			sPrefixNomFichierEmpreinte	= Droite(sNomSansExtension,Taille(gsRefCapture+"_1674640945537_finger_10"))
vWD_sPrefixNomFichierEmpreinte.setValeur(WDAPIChaine.droite(vWD_sNomSansExtension,WDAPIChaine.taille(vWD_gsRefCapture.opPlus("_1674640945537_finger_10")).getInt()));

// 			sPrefixNomFichierFace		= Droite(sNomSansExtension,Taille(gsRefCapture+"_1674640945537_face_20"))
vWD_sPrefixNomFichierFace.setValeur(WDAPIChaine.droite(vWD_sNomSansExtension,WDAPIChaine.taille(vWD_gsRefCapture.opPlus("_1674640945537_face_20")).getInt()));

// 			sPrefixNomFichierEmpreinte	= Remplace(sPrefixNomFichierEmpreinte,"/","")
vWD_sPrefixNomFichierEmpreinte.setValeur(WDAPIChaine.remplace(vWD_sPrefixNomFichierEmpreinte,new WDChaineU("/"),new WDChaineU("")));

// 			tabVal		est un tableau de chaînes	= ChaîneDécoupe(sPrefixNomFichierEmpreinte,"_")
WDObjet vWD_tabVal = WDVarNonAllouee.ref;
vWD_tabVal = new WDTableauSimple(1, new int[]{0}, 0, 16, 0);
vWD_tabVal.setValeur(WDAPIChaine.chaineDecoupe(vWD_sPrefixNomFichierEmpreinte,new WDObjet[] {new WDChaineU("_")} ));


// 			SI tabVal[1] = gsRefCapture ALORS
if(vWD_tabVal.get(1).opEgal(vWD_gsRefCapture, 0))
{
// 				SELON tabVal[4]
// Délimiteur de visibilité pour ne pas étendre la visibilité de la variable temporaire _WDExpSelon
{
// 				SELON tabVal[4]
WDObjet _WDExpSelon0 = vWD_tabVal.get(4);
// 				SELON tabVal[4]
if(_WDExpSelon0.opEgal(1, 0))
{
// 						pos1 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos1.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(2, 0))
{
// 						pos2 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos2.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(3, 0))
{
// 						pos3 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos3.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(4, 0))
{
// 						pos4 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos4.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(5, 0))
{
// 						pos5 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos5.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(6, 0))
{
// 						pos6 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos6.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(7, 0))
{
// 						pos7 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos7.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(8, 0))
{
// 						pos8 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos8.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(9, 0))
{
// 						pos9 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos9.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(10, 0))
{
// 						pos10 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos10.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 				SELON tabVal[4]
else if(_WDExpSelon0.opEgal(20, 0))
{
// 						sGsAi_selfie = sInfoRépertoire+[fSep()]+sPrefixNomFichierFace+".png"
vWD_sGsAi_selfie.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierFace).opPlus(".png"));

}
// 				SELON tabVal[4]
else {
}

}

}

}

}
finally
{
if(parcours1 != null)
{
parcours1.finParcours();
}
}


}
else
{
// 		Erreur("Le répertoire n'existe pas!!")
WDAPIDialogue.erreur("Le répertoire n'existe pas!!");

}


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple5 mWD_Simple5;

/**
 * Simple6
 */
class GWDSimple6 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°19 de run.Simple6
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2645753803592950863l);

super.setChecksum("788760192");

super.setNom("Simple6");

super.setType(4);

super.setBulle("");

super.setLibelle("Return");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(25, 826);

super.setTailleInitiale(132, 42);

super.setPlan(2);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(19);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(18);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(4);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>", 0, 1, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff000000));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff000000));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff000000));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(getCouleur_GEN(0xffff9999), new Object[] {getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1}, new int[] {0, 0, 0, 0, 0, 0, 0, 0}, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(getCouleur_GEN(0xffff9999), new Object[] {getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1}, new int[] {0, 0, 0, 0, 0, 0, 0, 0}, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(getCouleur_GEN(0xffff9999), new Object[] {getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1}, new int[] {0, 0, 0, 0, 0, 0, 0, 0}, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(getCouleur_GEN(0xffff9999), new Object[] {getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1, getCouleur_GEN(0xff505050), 0, 1}, new int[] {0, 0, 0, 0, 0, 0, 0, 0}, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple6
 */
public void clicSurBoutonGauche()
// MaFenêtre..Plan = 1
{
super.clicSurBoutonGauche();

// MaFenêtre..Plan = 1

try
{
// MaFenêtre..Plan = 1
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,1);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple6 mWD_Simple6;

/**
 * Image3
 */
class GWDImage3 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°20 de run.Image3
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2645756865999913781l);

super.setChecksum("884043599");

super.setNom("Image3");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(80, 2);

super.setTailleInitiale(200, 200);

super.setValeurInitiale("");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(20);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDImage3 mWD_Image3;

/**
 * TexteInterne1
 */
class GWDTexteInterne1 extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°21 de run.TexteInterne1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,320,21);
super.setRectCompPrincipal(0,21,320,40);

super.setQuid(2645759099439592428l);

super.setChecksum("940726110");

super.setNom("TexteInterne1");

super.setType(20001);

super.setBulle("");

super.setLibelle("Your Crypto File path is");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(1);

super.setPositionInitiale(20, 205);

super.setTailleInitiale(320, 63);

super.setValeurInitiale("");

super.setPlan(2);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(21);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(8);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of TexteInterne1
 */
public void init()
// Version 1
// Description 
// Saisie de texte
// // Version 1
{
super.init();

// // Version 1

try
{
// MoiMême = Image3
WDContexte.getMoiMeme().setValeur(mWD_Image3);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDTexteInterne1 mWD_TexteInterne1;

/**
 * sai_nom_crypto
 */
class GWDsai_nom_crypto extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°22 de run.sai_nom_crypto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,40);

super.setQuid(2648459272236722500l);

super.setChecksum("813933697");

super.setNom("sai_nom_crypto");

super.setType(20001);

super.setBulle("");

super.setLibelle("Nom");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 19);

super.setTailleInitiale(309, 63);

super.setValeurInitiale("");

super.setPlan(3);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(22);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(20);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDsai_nom_crypto mWD_sai_nom_crypto;

/**
 * sai_prenom_crypto
 */
class GWDsai_prenom_crypto extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°23 de run.sai_prenom_crypto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,40);

super.setQuid(2648459358140544681l);

super.setChecksum("818409978");

super.setNom("sai_prenom_crypto");

super.setType(20001);

super.setBulle("");

super.setLibelle("Prénom");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 82);

super.setTailleInitiale(309, 63);

super.setValeurInitiale("");

super.setPlan(3);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(23);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(21);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDsai_prenom_crypto mWD_sai_prenom_crypto;

/**
 * sai_dateNaissance_crypto
 */
class GWDsai_dateNaissance_crypto extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°24 de run.sai_dateNaissance_crypto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,40);

super.setQuid(2648459426862396682l);

super.setChecksum("820785259");

super.setNom("sai_dateNaissance_crypto");

super.setType(20001);

super.setBulle("");

super.setLibelle("Date de naissance");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 146);

super.setTailleInitiale(309, 63);

super.setValeurInitiale("");

super.setPlan(3);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(24);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(22);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDsai_dateNaissance_crypto mWD_sai_dateNaissance_crypto;

/**
 * sai_lieuNaissance_crypto
 */
class GWDsai_lieuNaissance_crypto extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°25 de run.sai_lieuNaissance_crypto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,40);

super.setQuid(2648459474107113677l);

super.setChecksum("820862009");

super.setNom("sai_lieuNaissance_crypto");

super.setType(20001);

super.setBulle("");

super.setLibelle("Lieu de naissance");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 210);

super.setTailleInitiale(309, 63);

super.setValeurInitiale("");

super.setPlan(3);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(25);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(23);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDsai_lieuNaissance_crypto mWD_sai_lieuNaissance_crypto;

/**
 * sai_email_crypto
 */
class GWDsai_email_crypto extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°26 de run.sai_email_crypto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,40);

super.setQuid(2648459529941832503l);

super.setChecksum("821006000");

super.setNom("sai_email_crypto");

super.setType(20001);

super.setBulle("");

super.setLibelle("Email");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 275);

super.setTailleInitiale(309, 63);

super.setValeurInitiale("");

super.setPlan(3);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(26);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(24);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDsai_email_crypto mWD_sai_email_crypto;

/**
 * sai_telephone_crypto
 */
class GWDsai_telephone_crypto extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°27 de run.sai_telephone_crypto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,40);

super.setQuid(2648459946558737467l);

super.setChecksum("826083349");

super.setNom("sai_telephone_crypto");

super.setType(20001);

super.setBulle("");

super.setLibelle("Téléphone");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 339);

super.setTailleInitiale(309, 63);

super.setValeurInitiale("");

super.setPlan(3);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(27);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(25);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDsai_telephone_crypto mWD_sai_telephone_crypto;

/**
 * sai_nationalite_crypto
 */
class GWDsai_nationalite_crypto extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°28 de run.sai_nationalite_crypto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,40);

super.setQuid(2648460376060614042l);

super.setChecksum("831230424");

super.setNom("sai_nationalite_crypto");

super.setType(20001);

super.setBulle("");

super.setLibelle("Nationalité");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 404);

super.setTailleInitiale(309, 63);

super.setValeurInitiale("");

super.setPlan(3);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(28);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(26);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDsai_nationalite_crypto mWD_sai_nationalite_crypto;

/**
 * Simple7
 */
class GWDSimple7 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°29 de run.Simple7
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648460994558396145l);

super.setChecksum("853722959");

super.setNom("Simple7");

super.setType(4);

super.setBulle("");

super.setLibelle("Suivant");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(203, 507);

super.setTailleInitiale(127, 48);

super.setPlan(3);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(29);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(28);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple7
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// MaFenêtre..plan = 4
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,4);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple7 mWD_Simple7;

/**
 * Simple8
 */
class GWDSimple8 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°30 de run.Simple8
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461016033368697l);

super.setChecksum("853859036");

super.setNom("Simple8");

super.setType(4);

super.setBulle("");

super.setLibelle("Retour");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(26, 507);

super.setTailleInitiale(127, 48);

super.setPlan(3);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(30);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(27);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple8
 */
public void clicSurBoutonGauche()
// MaFenêtre..plan = 1
{
super.clicSurBoutonGauche();

// MaFenêtre..plan = 1

try
{
// MaFenêtre..plan = 1
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,1);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple8 mWD_Simple8;

/**
 * BTN_Prise_empreinte_main_droite1
 */
class GWDBTN_Prise_empreinte_main_droite1 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°31 de run.BTN_Prise_empreinte_main_droite1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461351049088269l);

super.setChecksum("862129598");

super.setNom("BTN_Prise_empreinte_main_droite1");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture Right Hand");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 231);

super.setTailleInitiale(321, 48);

super.setPlan(4);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(31);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(32);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_main_droite1
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_main_droite1..Visible = vrai
mWD_IMG_validation_main_droite1.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinteCrMD = Vrai
vWD_gbIsEmpreinteCrMD.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_main_droite1 mWD_BTN_Prise_empreinte_main_droite1;

/**
 * BTN_Prise_empreinte_pouce_gauche1
 */
class GWDBTN_Prise_empreinte_pouce_gauche1 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°32 de run.BTN_Prise_empreinte_pouce_gauche1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461351049219341l);

super.setChecksum("862260670");

super.setNom("BTN_Prise_empreinte_pouce_gauche1");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture Left Thumb");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 175);

super.setTailleInitiale(321, 48);

super.setPlan(4);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(32);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(31);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_pouce_gauche1
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_pouce_gauche1..Visible = Vrai
mWD_IMG_validation_pouce_gauche1.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinteCrPG = Vrai
vWD_gbIsEmpreinteCrPG.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_pouce_gauche1 mWD_BTN_Prise_empreinte_pouce_gauche1;

/**
 * BTN_Prise_empreinte_main_gauche1
 */
class GWDBTN_Prise_empreinte_main_gauche1 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°33 de run.BTN_Prise_empreinte_main_gauche1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461351049350429l);

super.setChecksum("862391758");

super.setNom("BTN_Prise_empreinte_main_gauche1");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture Left Hand");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 117);

super.setTailleInitiale(321, 48);

super.setPlan(4);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(33);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(30);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_main_gauche1
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_main_gauche1..Visible = vrai
mWD_IMG_validation_main_gauche1.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinteCrMG = Vrai
vWD_gbIsEmpreinteCrMG.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_main_gauche1 mWD_BTN_Prise_empreinte_main_gauche1;

/**
 * BTN_Selfie_identite1
 */
class GWDBTN_Selfie_identite1 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°34 de run.BTN_Selfie_identite1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461351049481516l);

super.setChecksum("862522845");

super.setNom("BTN_Selfie_identite1");

super.setType(4);

super.setBulle("");

super.setLibelle("SELFIE D'IDENTITE");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 59);

super.setTailleInitiale(321, 48);

super.setPlan(4);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(34);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(29);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Selfie_identite1
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_valider_selfie1..Visible	= Vrai
mWD_IMG_valider_selfie1.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsSelfie = Vrai
vWD_gbIsSelfie.setValeur(true);

// 	StartFaceSelfieCapture(gsRefCapture)
GWDCPSDKInt.StartFaceSelfieCapture(vWD_gsRefCapture.getString());


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Selfie_identite1 mWD_BTN_Selfie_identite1;

/**
 * BTN_Prise_empreinte_pouce_droit1
 */
class GWDBTN_Prise_empreinte_pouce_droit1 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°35 de run.BTN_Prise_empreinte_pouce_droit1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461351049612604l);

super.setChecksum("862653933");

super.setNom("BTN_Prise_empreinte_pouce_droit1");

super.setType(4);

super.setBulle("");

super.setLibelle("Capture Right Thumb");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 289);

super.setTailleInitiale(321, 48);

super.setPlan(4);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(35);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(33);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_pouce_droit1
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_pouce_droit1..Visible = Vrai
mWD_IMG_validation_pouce_droit1.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinteCrPD = Vrai
vWD_gbIsEmpreinteCrPD.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_pouce_droit1 mWD_BTN_Prise_empreinte_pouce_droit1;

/**
 * IMG_validation_main_droite1
 */
class GWDIMG_validation_main_droite1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°36 de run.IMG_validation_main_droite1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2648461355344711019l);

super.setChecksum("862786877");

super.setNom("IMG_validation_main_droite1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(250, 238);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(4);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(36);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_main_droite1 mWD_IMG_validation_main_droite1;

/**
 * IMG_validation_pouce_gauche1
 */
class GWDIMG_validation_pouce_gauche1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°37 de run.IMG_validation_pouce_gauche1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2648461355344973194l);

super.setChecksum("863049052");

super.setNom("IMG_validation_pouce_gauche1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(250, 182);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(4);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(37);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_pouce_gauche1 mWD_IMG_validation_pouce_gauche1;

/**
 * IMG_validation_main_gauche1
 */
class GWDIMG_validation_main_gauche1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°38 de run.IMG_validation_main_gauche1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2648461355345235369l);

super.setChecksum("863311227");

super.setNom("IMG_validation_main_gauche1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(250, 124);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(4);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(38);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_main_gauche1 mWD_IMG_validation_main_gauche1;

/**
 * IMG_valider_selfie1
 */
class GWDIMG_valider_selfie1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°39 de run.IMG_valider_selfie1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2648461355345497529l);

super.setChecksum("863573387");

super.setNom("IMG_valider_selfie1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(249, 66);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(4);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(39);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_valider_selfie1 mWD_IMG_valider_selfie1;

/**
 * IMG_validation_pouce_droit1
 */
class GWDIMG_validation_pouce_droit1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°40 de run.IMG_validation_pouce_droit1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2648461355345759704l);

super.setChecksum("863835562");

super.setNom("IMG_validation_pouce_droit1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(250, 295);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(4);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(40);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_pouce_droit1 mWD_IMG_validation_pouce_droit1;

/**
 * Biometrie
 */
class GWDBiometrie extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°41 de run.Biometrie
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461436969556156l);

super.setChecksum("883251129");

super.setNom("Biometrie");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Biometrie");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(20, 10);

super.setTailleInitiale(321, 28);

super.setPlan(4);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(41);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -11.000000f, 2, 1, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBiometrie mWD_Biometrie;

/**
 * Simple9
 */
class GWDSimple9 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°42 de run.Simple9
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461673209721177l);

super.setChecksum("900215381");

super.setNom("Simple9");

super.setType(4);

super.setBulle("");

super.setLibelle("Générer mon cryptographe");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(189, 390);

super.setTailleInitiale(151, 48);

super.setPlan(4);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(42);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(35);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple9
 */
public void clicSurBoutonGauche()
// si gbIsSelfie <> vrai alors 
{
super.clicSurBoutonGauche();

// si gbIsSelfie <> vrai alors 

try
{
// si gbIsSelfie <> vrai alors 
if(vWD_gbIsSelfie.opDiff(true))
{
// 	ToastAffiche("Veuillez prendre votre selfie SVP",toastLong,cvHaut,chCentre)
WDAPIToast.toastAffiche("Veuillez prendre votre selfie SVP",1,0,1);

}

// si gbIsEmpreinteCrPD <> vrai alors 
if(vWD_gbIsEmpreinteCrPD.opDiff(true))
{
// 	ToastAffiche("Veuillez capturer les empreintes de votre pouce droit SVP",toastLong,cvHaut,chCentre)
WDAPIToast.toastAffiche("Veuillez capturer les empreintes de votre pouce droit SVP",1,0,1);

}

// si gbIsEmpreinteCrMD <> vrai alors 
if(vWD_gbIsEmpreinteCrMD.opDiff(true))
{
// 	ToastAffiche("Veuillez capturer les empreintes de votre main droite SVP",toastLong,cvHaut,chCentre)
WDAPIToast.toastAffiche("Veuillez capturer les empreintes de votre main droite SVP",1,0,1);

}

// si gbIsEmpreinteCrMG <> vrai alors 
if(vWD_gbIsEmpreinteCrMG.opDiff(true))
{
// 	ToastAffiche("Veuillez capturer les empreintes de votre main gauche SVP",toastLong,cvHaut,chCentre)
WDAPIToast.toastAffiche("Veuillez capturer les empreintes de votre main gauche SVP",1,0,1);

}

// si gbIsEmpreinteCrPG <> vrai alors 
if(vWD_gbIsEmpreinteCrPG.opDiff(true))
{
// 	ToastAffiche("Veuillez capturer les empreintes de votre pouce gauche SVP",toastLong,cvHaut,chCentre)
WDAPIToast.toastAffiche("Veuillez capturer les empreintes de votre pouce gauche SVP",1,0,1);

}

// ThreadExécute("threadEnvoiDonneeOriginal", threadNormal, genererCryptographe)
WDAPIThread.threadExecute("threadEnvoiDonneeOriginal",0,(new WDChaineU("run.genererCryptographe")));

// MaFenêtre..Plan = 100
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,100);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple9 mWD_Simple9;

/**
 * Simple10
 */
class GWDSimple10 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°43 de run.Simple10
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648461698979661510l);

super.setChecksum("900351944");

super.setNom("Simple10");

super.setType(4);

super.setBulle("");

super.setLibelle("Retour");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 390);

super.setTailleInitiale(146, 48);

super.setPlan(4);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(43);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(34);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple10
 */
public void clicSurBoutonGauche()
// MaFenêtre..plan = 3
{
super.clicSurBoutonGauche();

// MaFenêtre..plan = 3

try
{
// MaFenêtre..plan = 3
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,3);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple10 mWD_Simple10;

/**
 * IMG_SansNom32
 */
class GWDIMG_SansNom32 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°44 de run.IMG_SansNom32
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2648479828456074959l);

super.setChecksum("**********");

super.setNom("IMG_SansNom32");

super.setType(30001);

super.setBulle("");

super.setLibelle("Traitement en cours. Veuillez patienter SVP");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(56, 337);

super.setTailleInitiale(225, 216);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\IMG-Chargement-09.svg");

super.setPlan(100);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(44);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(false);

super.setParamAnimation(1, 24, false, 100, true, false);

super.setAnimationInitiale(true);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_SansNom32 mWD_IMG_SansNom32;

/**
 * LIB_Titre_de_fenêtre
 */
class GWDLIB_Titre_de_fenetre extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°45 de run.LIB_Titre_de_fenêtre
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648479828456337119l);

super.setChecksum("1320074902");

super.setNom("LIB_Titre_de_fenêtre");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Projet D'intégration");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(30, 191);

super.setTailleInitiale(301, 39);

super.setPlan(100);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(45);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Trebuchet MS", -16.000000f, 2, 1, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff2d2d2d), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff603c0c), getCouleur_GEN(0xff000000), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDLIB_Titre_de_fenetre mWD_LIB_Titre_de_fenetre;

/**
 * LIB_Traitement_en_cours
 */
class GWDLIB_Traitement_en_cours extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°46 de run.LIB_Traitement_en_cours
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648479828456468206l);

super.setChecksum("**********");

super.setNom("LIB_Traitement_en_cours");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Traitement en cours\r\nVeuillez patienter SVP");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(30, 600);

super.setTailleInitiale(301, 78);

super.setPlan(100);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(46);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Trebuchet MS", -11.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff2d2d2d), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 20.000000, 20.000000, 1, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDLIB_Traitement_en_cours mWD_LIB_Traitement_en_cours;

/**
 * Votre_cryptographe
 */
class GWDVotre_cryptographe extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°47 de run.Votre_cryptographe
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648480116260762962l);

super.setChecksum("1361691980");

super.setNom("Votre_cryptographe");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Votre cryptographe");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(28, 12);

super.setTailleInitiale(304, 28);

super.setPlan(99);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(47);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -11.000000f, 2, 1, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDVotre_cryptographe mWD_Votre_cryptographe;

/**
 * Image4
 */
class GWDImage4 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°48 de run.Image4
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2648480279474800731l);

super.setChecksum("1366974819");

super.setNom("Image4");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(28, 48);

super.setTailleInitiale(304, 218);

super.setValeurInitiale("");

super.setPlan(99);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(48);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDImage4 mWD_Image4;

/**
 * Titre_de_fenêtre
 */
class GWDTitre_de_fenetre extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°49 de run.Titre_de_fenêtre
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2648480373985533906l);

super.setChecksum("1388425224");

super.setNom("Titre_de_fenêtre");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Titre de fenêtre");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(28, 278);

super.setTailleInitiale(304, 72);

super.setPlan(99);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(49);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -11.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDTitre_de_fenetre mWD_Titre_de_fenetre;

/**
 * BTN_Selfie_identite2
 */
class GWDBTN_Selfie_identite2 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°50 de run.BTN_Selfie_identite2
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2694411586727190150l);

super.setChecksum("653696694");

super.setNom("BTN_Selfie_identite2");

super.setType(4);

super.setBulle("");

super.setLibelle("SELFIE D'IDENTITE BACK");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 283);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(50);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(9);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Selfie_identite2
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_valider_selfie2..Visible	= Vrai
mWD_IMG_valider_selfie2.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsSelfie = Vrai
vWD_gbIsSelfie.setValeur(true);

// 	StartFaceBackCapture(gsRefCapture)
GWDCPSDKInt.StartFaceBackCapture(vWD_gsRefCapture.getString());


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Selfie_identite2 mWD_BTN_Selfie_identite2;

/**
 * IMG_valider_selfie2
 */
class GWDIMG_valider_selfie2 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°51 de run.IMG_valider_selfie2
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2694411586727321238l);

super.setChecksum("653829606");

super.setNom("IMG_valider_selfie2");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(270, 290);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(51);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_valider_selfie2 mWD_IMG_valider_selfie2;

/**
 * BTN_Prise_empreinte_pouce_droit2
 */
class GWDBTN_Prise_empreinte_pouce_droit2 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°52 de run.BTN_Prise_empreinte_pouce_droit2
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2694412140805756526l);

super.setChecksum("681482015");

super.setNom("BTN_Prise_empreinte_pouce_droit2");

super.setType(4);

super.setBulle("");

super.setLibelle("Left Index");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 626);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(52);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(15);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_pouce_droit2
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_pouce_droit2..Visible = Vrai
mWD_IMG_validation_pouce_droit2.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinte = Vrai
vWD_gbIsEmpreinte.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_pouce_droit2 mWD_BTN_Prise_empreinte_pouce_droit2;

/**
 * IMG_validation_pouce_droit2
 */
class GWDIMG_validation_pouce_droit2 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°53 de run.IMG_validation_pouce_droit2
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2694412140805887598l);

super.setChecksum("681614911");

super.setNom("IMG_validation_pouce_droit2");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 632);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(53);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_pouce_droit2 mWD_IMG_validation_pouce_droit2;

/**
 * BTN_Prise_empreinte_pouce_droit3
 */
class GWDBTN_Prise_empreinte_pouce_droit3 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°54 de run.BTN_Prise_empreinte_pouce_droit3
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2694412445753682839l);

super.setChecksum("686730383");

super.setNom("BTN_Prise_empreinte_pouce_droit3");

super.setType(4);

super.setBulle("");

super.setLibelle("Right Index");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 678);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(54);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(16);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_pouce_droit3
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_pouce_droit3..Visible = Vrai
mWD_IMG_validation_pouce_droit3.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinte = Vrai
vWD_gbIsEmpreinte.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_pouce_droit3 mWD_BTN_Prise_empreinte_pouce_droit3;

/**
 * IMG_validation_pouce_droit3
 */
class GWDIMG_validation_pouce_droit3 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°55 de run.IMG_validation_pouce_droit3
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2694412445753813927l);

super.setChecksum("686863295");

super.setNom("IMG_validation_pouce_droit3");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 684);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(55);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_pouce_droit3 mWD_IMG_validation_pouce_droit3;

/**
 * BTN_Prise_empreinte_pouce_droit4
 */
class GWDBTN_Prise_empreinte_pouce_droit4 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°56 de run.BTN_Prise_empreinte_pouce_droit4
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2694412664807158417l);

super.setChecksum("696873916");

super.setNom("BTN_Prise_empreinte_pouce_droit4");

super.setType(4);

super.setBulle("");

super.setLibelle("Left and Right Thumb");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(19, 735);

super.setTailleInitiale(321, 48);

super.setPlan(2);

super.setImageEtat(5);

super.setImageFondEtat(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(56);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(17);

super.setLettreAppel(65535);

super.setTypeBouton(1);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>?E5", 0, 1, 5, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleSurvol(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc47400), getCouleur_GEN(0xff440000), getCouleur_GEN(0xfffbe1bd), 10.000000, 10.000000, 1, 1, 0, null));

super.setImageFond("", 1, 0, 2, 6);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click BTN_Prise_empreinte_pouce_droit4
 */
public void clicSurBoutonGauche()
// <COMPILE SI Configuration = "Application Android">
{
super.clicSurBoutonGauche();

// <COMPILE SI Configuration = "Application Android">

try
{
// <COMPILE SI Configuration = "Application Android">
// 	IMG_validation_pouce_droit4..Visible = Vrai
mWD_IMG_validation_pouce_droit4.setProp(EWDPropriete.PROP_VISIBLE,true);

// 	gbIsEmpreinte = Vrai
vWD_gbIsEmpreinte.setValeur(true);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDBTN_Prise_empreinte_pouce_droit4 mWD_BTN_Prise_empreinte_pouce_droit4;

/**
 * IMG_validation_pouce_droit4
 */
class GWDIMG_validation_pouce_droit4 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°57 de run.IMG_validation_pouce_droit4
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2694412664807289489l);

super.setChecksum("697006812");

super.setNom("IMG_validation_pouce_droit4");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(271, 741);

super.setTailleInitiale(36, 34);

super.setValeurInitiale("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\<EMAIL>");

super.setPlan(2);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(57);

super.setAncrageInitial(4, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff2d2d2d), creerPolice_GEN("Trebuchet MS", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff2d2d2d));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDIMG_validation_pouce_droit4 mWD_IMG_validation_pouce_droit4;

/**
 * InternalWindow1
 */
class GWDInternalWindow1 extends WDChampFenetreInterne
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°58 de run.InternalWindow1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2903357012676552802l);

super.setChecksum("755280748");

super.setNom("InternalWindow1");

super.setType(31);

super.setLibelle("");

super.setNote("", "");

super.setPositionInitiale(5, 20);

super.setTailleInitiale(351, 549);

super.setValeurInitiale("");

super.setPlan(5);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setNumTab(36);

super.setAltitude(58);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setPersistant(false);

super.setFenetreInterne("InternalWindow1");

super.setTauxParallaxe(0, 0, false);

super.setCouleurTexteAutomatique(getCouleur_GEN(0xff000001, true));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xf1000000, true), getCouleur_GEN(0xf3000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(25, 26, 300);
super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
protected boolean isAvecAscenseurAuto()
{
return true;
}

protected boolean isBalayageVertical()
{
return false;
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDInternalWindow1 mWD_InternalWindow1;

/**
 * Simple11
 */
class GWDSimple11 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°59 de run.Simple11
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2903357141533924629l);

super.setChecksum("763621413");

super.setNom("Simple11");

super.setType(4);

super.setBulle("");

super.setLibelle("Lire mon cryptographe");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(190, 589);

super.setTailleInitiale(151, 48);

super.setPlan(5);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(59);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(38);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple11 mWD_Simple11;

/**
 * Simple12
 */
class GWDSimple12 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°60 de run.Simple12
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2903357141534055873l);

super.setChecksum("763752657");

super.setNom("Simple12");

super.setType(4);

super.setBulle("");

super.setLibelle("Retour");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(20, 589);

super.setTailleInitiale(146, 48);

super.setPlan(5);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(60);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(37);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple12
 */
public void clicSurBoutonGauche()
// MaFenêtre..plan = 1
{
super.clicSurBoutonGauche();

// MaFenêtre..plan = 1

try
{
// MaFenêtre..plan = 1
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,1);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple12 mWD_Simple12;

/**
 * Simple13
 */
class GWDSimple13 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°61 de run.Simple13
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2903357386356554685l);

super.setChecksum("773115654");

super.setNom("Simple13");

super.setType(4);

super.setBulle("");

super.setLibelle("Test  NEW SDKS");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(74, 252);

super.setTailleInitiale(213, 48);

super.setPlan(1);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(61);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(4);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple13
 */
public void clicSurBoutonGauche()
// <compile si Configuration="Application Android">
{
super.clicSurBoutonGauche();

// <compile si Configuration="Application Android">

try
{
// <compile si Configuration="Application Android">
// MaFenêtre..plan = 5
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,5);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple13 mWD_Simple13;

/**
 * Simple14
 */
class GWDSimple14 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°62 de run.Simple14
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2903716948214245173l);

super.setChecksum("853770627");

super.setNom("Simple14");

super.setType(4);

super.setBulle("");

super.setLibelle("scan cryptographe");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(20, 666);

super.setTailleInitiale(151, 48);

super.setPlan(5);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(62);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(39);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple14
 */
public void clicSurBoutonGauche()
// <compile if Configuration="Application Android">
{
super.clicSurBoutonGauche();

// <compile if Configuration="Application Android">

try
{
// <compile if Configuration="Application Android">
// ScanCryptoDemodecoded()
GWDCPSDKInt.ScanCryptoDemodecoded();


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple14 mWD_Simple14;

/**
 * Simple15
 */
class GWDSimple15 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°63 de run.Simple15
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905160628394069834l);

super.setChecksum("791825053");

super.setNom("Simple15");

super.setType(4);

super.setBulle("");

super.setLibelle("Test  NEW SDKS");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(74, 252);

super.setTailleInitiale(213, 48);

super.setPlan(1);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(63);

super.setAncrageInitial(5, 1000, 1000, 500, 500, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(5);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple15
 */
public void clicSurBoutonGauche()
// <compile si Configuration="Application Android">
{
super.clicSurBoutonGauche();

// <compile si Configuration="Application Android">

try
{
// <compile si Configuration="Application Android">
// MaFenêtre..plan = 5
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,5);


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple15 mWD_Simple15;

/**
 * Simple16
 */
class GWDSimple16 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°64 de run.Simple16
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905160688523757222l);

super.setChecksum("791970311");

super.setNom("Simple16");

super.setType(4);

super.setBulle("");

super.setLibelle("Retour API");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(190, 669);

super.setTailleInitiale(151, 48);

super.setPlan(5);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(64);

super.setAncrageInitial(4, 1000, 1000, 500, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(40);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple16
 */
public void clicSurBoutonGauche()
// mafenetre.plan = 6
{
super.clicSurBoutonGauche();

// mafenetre.plan = 6

try
{
// mafenetre.plan = 6
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,6);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple16 mWD_Simple16;

/**
 * edit_retour_API
 */
class GWDedit_retour_API extends WDChampSaisieMultiLigne
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°65 de run.edit_retour_API
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,344,21);
super.setRectCompPrincipal(0,21,339,113);

super.setQuid(2905160911995015794l);

super.setChecksum("924936489");

super.setNom("edit_retour_API");

super.setType(20001);

super.setBulle("");

super.setLibelle("Texte");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(8, 23);

super.setTailleInitiale(344, 136);

super.setValeurInitiale("");

super.setPlan(6);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(65);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(41);

super.setModeAscenseur(2, 1);

super.setEffacementAutomatique(false);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDedit_retour_API mWD_edit_retour_API;

/**
 * Simple17
 */
class GWDSimple17 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°66 de run.Simple17
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905161053734016068l);

super.setChecksum("930009082");

super.setNom("Simple17");

super.setType(4);

super.setBulle("");

super.setLibelle("Retour");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(22, 565);

super.setTailleInitiale(146, 48);

super.setPlan(6);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(66);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(45);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple17
 */
public void clicSurBoutonGauche()
// edit_retour_API = retourapi
{
super.clicSurBoutonGauche();

// edit_retour_API = retourapi

try
{
// edit_retour_API = retourapi
mWD_edit_retour_API.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi);

// MaFenêtre..plan = 5
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,5);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple17 mWD_Simple17;

/**
 * Simple18
 */
class GWDSimple18 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°67 de run.Simple18
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905161105278747178l);

super.setChecksum("935132652");

super.setNom("Simple18");

super.setType(4);

super.setBulle("");

super.setLibelle("Afficher retour API");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(186, 565);

super.setTailleInitiale(146, 48);

super.setPlan(6);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(67);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(46);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple18
 */
public void clicSurBoutonGauche()
// edit_retour_API = retourapi
// edit_retour_API = "ceci est le retour des données démographiques"
// edit_retour_API = retourapi.demographics.content
// //edit_retour_API = retourapi
{
super.clicSurBoutonGauche();

// //edit_retour_API = retourapi

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_demo = new WDChaineU();




try
{
// demo est une chaine = retourapi.demographics.content

vWD_demo.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("demographics").get("content"));


// edit_retour_API = Décrypte(demo,"",compresseAucun,encodeBASE64)
mWD_edit_retour_API.setValeur(WDAPICrypt.decrypte(vWD_demo,"",0,true));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple18 mWD_Simple18;

/**
 * img_selfie
 */
class GWDimg_selfie extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°68 de run.img_selfie
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2905209342178369920l);

super.setChecksum("1057067073");

super.setNom("img_selfie");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(13, 638);

super.setTailleInitiale(202, 202);

super.setValeurInitiale("");

super.setPlan(6);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(68);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc00000), getCouleur_GEN(0xff400000), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 1, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDimg_selfie mWD_img_selfie;

/**
 * Simple19
 */
class GWDSimple19 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°69 de run.Simple19
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905239011871066686l);

super.setChecksum("1115688155");

super.setNom("Simple19");

super.setType(4);

super.setBulle("");

super.setLibelle("decoded demo");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(243, 632);

super.setTailleInitiale(89, 48);

super.setPlan(6);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(69);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(47);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple19
 */
public void clicSurBoutonGauche()
// edit_retour_API = retourapi
// edit_retour_API = "ceci est le retour des données démographiques"
// edit_retour_API = retourapi.demographics.content
// demo est une chaine = retourapi.demographics.content
// chSDecodeBase64 est un Buffer  = Décrypte(demo,"",compresseAucun,encodeBASE64)
// edit_retour_API = chSDecodeBase64
// //edit_retour_API = Decode(retourapi.demographics.content, encodeBASE64)
// //edit_retour_API = retourapi
{
super.clicSurBoutonGauche();

// //edit_retour_API = retourapi

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_donneesDemo = new WDChaineU();

WDObjet vWD_tabDonneeDemo = WDVarNonAllouee.ref;
WDObjet vWD_pos = new WDChaineU();

WDObjet vWD_MyArray = WDVarNonAllouee.ref;



try
{
// edit_retour_API = retourapi.processedData.decodedDemographics
mWD_edit_retour_API.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("processedData").get("decodedDemographics"));

// Expiration_crpto = retourapi.originalResult.expiryTime
mWD_Expiration_crpto.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("originalResult").get("expiryTime"));

// donneesDemo	est une chaîne	= retourapi.processedData.decodedDemographics

vWD_donneesDemo.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("processedData").get("decodedDemographics"));


// tabDonneeDemo	est un tableau de chaînes
vWD_tabDonneeDemo = new WDTableauSimple(1, new int[]{0}, 0, 16, 0);

// tabDonneeDemo = ChaîneDécoupe(donneesDemo, ",")
vWD_tabDonneeDemo.setValeur(WDAPIChaine.chaineDecoupe(vWD_donneesDemo,new WDObjet[] {new WDChaineU(",")} ));

// LooperDeleteAll(Zr_donnees_demo)
WDAPIZoneRepetee.zoneRepeteeSupprimeTout(mWD_Zr_donnees_demo);

// POUR nIndice = 1 _À_ tabDonneeDemo.Occurrence
// Délimiteur de visibilité pour ne pas étendre la visibilité des variables temporaires _WDExpBorneMax et _WDExpPas
{
WDObjet _WDExpBorneMax0 = new WDEntier4(vWD_tabDonneeDemo.getProp(EWDPropriete.PROP_OCCURRENCE));
// POUR nIndice = 1 _À_ tabDonneeDemo.Occurrence
for(WDObjet vWD_nIndice = new WDEntier4(1);vWD_nIndice.opInfEgal(_WDExpBorneMax0);vWD_nIndice.opInc())
{
// 	SWITCH nIndice
// Délimiteur de visibilité pour ne pas étendre la visibilité de la variable temporaire _WDExpSelon
{
// 	SWITCH nIndice
WDObjet _WDExpSelon1 = vWD_nIndice;
// 	SWITCH nIndice
if(_WDExpSelon1.opEgal(1, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Nom", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Nom"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(2, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Date de naissance", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Date de naissance"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(3, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Pays", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Pays"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(4, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Email", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Email"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(5, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Sexe", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Sexe"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(6, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Statut matrimonial", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Statut matrimonial"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(7, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Groupe Sanguin", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Groupe Sanguin"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(8, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Permis", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Permis"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}
// 	SWITCH nIndice
else if(_WDExpSelon1.opEgal(9, 0))
{
// 			ZoneRépétéeAjouteLigne(Zr_donnees_demo,"Profession", tabDonneeDemo[nIndice])
WDAPIZoneRepetee.zoneRepeteeAjouteLigne(mWD_Zr_donnees_demo,new WDObjet[] {new WDChaineU("Profession"),vWD_tabDonneeDemo.get(vWD_nIndice)} );

}

}

}
}

// faceImageedit = retourapi.processedData.fingerprintPositions
mWD_faceImageedit.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("processedData").get("fingerprintPositions"));

// pos est une chaine = retourapi.processedData.fingerprintPositions

vWD_pos.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("processedData").get("fingerprintPositions"));


// MyArray est un tableau de chaînes
vWD_MyArray = new WDTableauSimple(1, new int[]{0}, 0, 16, 0);

// MyArray = ChaîneDécoupe(pos, ",")
vWD_MyArray.setValeur(WDAPIChaine.chaineDecoupe(vWD_pos,new WDObjet[] {new WDChaineU(",")} ));

// POUR nIndice = 1 _À_ MyArray.occurrence
// Délimiteur de visibilité pour ne pas étendre la visibilité des variables temporaires _WDExpBorneMax et _WDExpPas
{
WDObjet _WDExpBorneMax2 = new WDEntier4(vWD_MyArray.getProp(EWDPropriete.PROP_OCCURRENCE));
// POUR nIndice = 1 _À_ MyArray.occurrence
for(WDObjet vWD_nIndice = new WDEntier4(1);vWD_nIndice.opInfEgal(_WDExpBorneMax2);vWD_nIndice.opInc())
{
// 	SWITCH MyArray[nIndice]
// Délimiteur de visibilité pour ne pas étendre la visibilité de la variable temporaire _WDExpSelon
{
// 	SWITCH MyArray[nIndice]
WDObjet _WDExpSelon3 = vWD_MyArray.get(vWD_nIndice);
// 	SWITCH MyArray[nIndice]
if(_WDExpSelon3.opEgal(1, 0))
{
// 			ListeAjoute(Combo_empreinte, "Pouce droit" + gValeurMémorisée("RT"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Pouce droit").opPlus(WDAPIDessin.gLien("RT")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Pouce droit" + gValeurMémorisée("RT"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Pouce droit").opPlus(WDAPIDessin.gLien("RT")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(2, 0))
{
// 			ListeAjoute(Combo_empreinte, "Index droit" + gValeurMémorisée("RI"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Index droit").opPlus(WDAPIDessin.gLien("RI")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Index droit" + gValeurMémorisée("RI"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Index droit").opPlus(WDAPIDessin.gLien("RI")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(3, 0))
{
// 			ListeAjoute(Combo_empreinte, "Majeur droit" + gValeurMémorisée("RM"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Majeur droit").opPlus(WDAPIDessin.gLien("RM")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Majeur droit" + gValeurMémorisée("RM"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Majeur droit").opPlus(WDAPIDessin.gLien("RM")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(4, 0))
{
// 			ListeAjoute(Combo_empreinte, "Annulaire droit" + gValeurMémorisée("RR"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Annulaire droit").opPlus(WDAPIDessin.gLien("RR")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Annulaire droit" + gValeurMémorisée("RR"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Annulaire droit").opPlus(WDAPIDessin.gLien("RR")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(5, 0))
{
// 			ListeAjoute(Combo_empreinte, "Auriculaire droit" + gValeurMémorisée("RL"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Auriculaire droit").opPlus(WDAPIDessin.gLien("RL")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Auriculaire droit" + gValeurMémorisée("RL"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Auriculaire droit").opPlus(WDAPIDessin.gLien("RL")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(6, 0))
{
// 			ListeAjoute(Combo_empreinte, "Pouce gauche" + gValeurMémorisée("LT"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Pouce gauche").opPlus(WDAPIDessin.gLien("LT")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Pouce gauche" + gValeurMémorisée("LT"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Pouce gauche").opPlus(WDAPIDessin.gLien("LT")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(7, 0))
{
// 			ListeAjoute(Combo_empreinte, "Index gauche" + gValeurMémorisée("LI"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Index gauche").opPlus(WDAPIDessin.gLien("LI")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Index gauche" + gValeurMémorisée("LI"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Index gauche").opPlus(WDAPIDessin.gLien("LI")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(8, 0))
{
// 			ListeAjoute(Combo_empreinte, "Majeur gauche" + gValeurMémorisée("LM"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Majeur gauche").opPlus(WDAPIDessin.gLien("LM")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Majeur gauche" + gValeurMémorisée("LM"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Majeur gauche").opPlus(WDAPIDessin.gLien("LM")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(9, 0))
{
// 			ListeAjoute(Combo_empreinte, "Annulaire gauche" + gValeurMémorisée("LR"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Annulaire gauche").opPlus(WDAPIDessin.gLien("LR")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Annulaire gauche" + gValeurMémorisée("LR"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Annulaire gauche").opPlus(WDAPIDessin.gLien("LR")).getString());

}
// 	SWITCH MyArray[nIndice]
else if(_WDExpSelon3.opEgal(10, 0))
{
// 			ListeAjoute(Combo_empreinte, "Auriculaire gauche" + gValeurMémorisée("LL"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte,new WDChaineU("Auriculaire gauche").opPlus(WDAPIDessin.gLien("LL")).getString());

// 			ListeAjoute(Combo_empreinte_decode, "Auriculaire gauche" + gValeurMémorisée("LL"))
WDAPIListe.listeAjoute(mWD_Combo_empreinte_decode,new WDChaineU("Auriculaire gauche").opPlus(WDAPIDessin.gLien("LL")).getString());

}

}

}
}

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple19 mWD_Simple19;

/**
 * Simple20
 */
class GWDSimple20 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°70 de run.Simple20
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905239630377565343l);

super.setChecksum("1146896332");

super.setNom("Simple20");

super.setType(4);

super.setBulle("");

super.setLibelle("global");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(243, 691);

super.setTailleInitiale(89, 48);

super.setPlan(6);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(70);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(48);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple20
 */
public void clicSurBoutonGauche()
// edit_retour_API = retourapi
// edit_retour_API = "ceci est le retour des données démographiques"
// edit_retour_API = retourapi.demographics.content
// demo est une chaine = retourapi.demographics.content
// chSDecodeBase64 est un Buffer  = Décrypte(demo,"",compresseAucun,encodeBASE64)
// edit_retour_API = chSDecodeBase64
// //edit_retour_API = retourapi
{
super.clicSurBoutonGauche();

// //edit_retour_API = retourapi

try
{
// edit_retour_API = JSONToString(retourapi)
mWD_edit_retour_API.setValeur(WDAPIJSON.jsonVersChaine(GWDPintegrationSdk.getInstance().vWD_retourapi));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple20 mWD_Simple20;

/**
 * Simple21
 */
class GWDSimple21 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°71 de run.Simple21
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905239810776785508l);

super.setChecksum("1157490107");

super.setNom("Simple21");

super.setType(4);

super.setBulle("");

super.setLibelle("selfie");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(243, 754);

super.setTailleInitiale(89, 48);

super.setPlan(6);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(71);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(49);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple21
 */
public void clicSurBoutonGauche()
// edit_retour_API = retourapi
// edit_retour_API = "ceci est le retour des données démographiques"
// edit_retour_API = retourapi.demographics.content
// demo est une chaine = retourapi.demographics.content
// chSDecodeBase64 est un Buffer  = Décrypte(demo,"",compresseAucun,encodeBASE64)
// edit_retour_API = chSDecodeBase64
// edit_retour_API = Decode(AnsiToUnicode(retourapi.demographics.content), encodeBASE64)
// //edit_retour_API = retourapi
{
super.clicSurBoutonGauche();

// //edit_retour_API = retourapi

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_MonBuffer = new WDBuffer();




try
{
// MonBuffer est un Buffer


// MonBuffer = decompressedFaceString // Load the Base64 string into a buffer
vWD_MonBuffer.setValeur(GWDPintegrationSdk.getInstance().vWD_decompressedFaceString.getDonneeBinaire());

// faceImageedit = decompressedFaceString
mWD_faceImageedit.setValeur(GWDPintegrationSdk.getInstance().vWD_decompressedFaceString);

// run.img_selfie	= MonBuffer.Decode(encodeBASE64)
mWD_img_selfie.setValeur(WDAPIEncode.decode(vWD_MonBuffer,2));

// run.img_selfie1	= MonBuffer.Decode(encodeBASE64)
mWD_img_selfie1.setValeur(WDAPIEncode.decode(vWD_MonBuffer,2));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple21 mWD_Simple21;

/**
 * Simple22
 */
class GWDSimple22 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°72 de run.Simple22
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905241911071660126l);

super.setChecksum("1213357470");

super.setNom("Simple22");

super.setType(4);

super.setBulle("");

super.setLibelle("coded demo");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(243, 810);

super.setTailleInitiale(89, 48);

super.setPlan(6);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(72);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(50);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple22
 */
public void clicSurBoutonGauche()
// edit_retour_API = retourapi
// edit_retour_API = "ceci est le retour des données démographiques"
// edit_retour_API = retourapi.demographics.content
// demo est une chaine = retourapi.demographics.content
// chSDecodeBase64 est un Buffer  = Décrypte(demo,"",compresseAucun,encodeBASE64)
// edit_retour_API = chSDecodeBase64
// //edit_retour_API = retourapi
{
super.clicSurBoutonGauche();

// //edit_retour_API = retourapi

try
{
// edit_retour_API = retourapi.demographics.content
mWD_edit_retour_API.setValeur(GWDPintegrationSdk.getInstance().vWD_retourapi.get("demographics").get("content"));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple22 mWD_Simple22;

/**
 * Combo_empreinte
 */
class GWDCombo_empreinte extends WDCombo
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°73 de run.Combo_empreinte
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,334,21);
super.setRectCompPrincipal(0,21,334,40);

super.setQuid(2905890944762885101l);

super.setChecksum("927627093");

super.setNom("Combo_empreinte");

super.setType(10002);

super.setBulle("");

super.setLibelle("Empreinte présente");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(13, 182);

super.setTailleInitiale(334, 63);

super.setValeurInitiale("");

super.setPlan(6);

super.setCadrageHorizontal(0);

super.setContenuInitial("Selectionner");

super.setTriee(false);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(73);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(42);

super.setLettreAppel(65535);

super.setRetourneValeurProgrammation(false);

super.setPersistant(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_32_6_42_6", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {6, 42, 6, 32}, getCouleur_GEN(0xffffffff), 5, 1, null));

super.setStyleElement(getCouleur_GEN(0xff000000), getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 48);

super.setStyleSelection(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xffffffff, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleBouton(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true));

super.setParamSaisieObligatoire(false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCombo_empreinte mWD_Combo_empreinte;

/**
 * Zr_donnees_demo
 */
class GWDZr_donnees_demo extends WDZoneRepetee
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°74 de run.Zr_donnees_demo
////////////////////////////////////////////////////////////////////////////

/**
 * att_titre
 */
class GWDatt_titre extends WDAttributZR
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de run.Zr_donnees_demo.att_titre
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setNom("att_titre");

super.setChampAssocie(mWD_titre_libelle);

super.setProprieteAssocie(EWDPropriete.PROP_VALEUR);

activerEcoute();
super.terminerInitialisation();
}
// Pas de traitement pour le champ run.Zr_donnees_demo.att_titre

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDatt_titre mWD_att_titre = new GWDatt_titre();

/**
 * att_donnee
 */
class GWDatt_donnee extends WDAttributZR
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de run.Zr_donnees_demo.att_donnee
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setNom("att_donnee");

super.setChampAssocie(mWD_donnee_libelle);

super.setProprieteAssocie(EWDPropriete.PROP_VALEUR);

activerEcoute();
super.terminerInitialisation();
}
// Pas de traitement pour le champ run.Zr_donnees_demo.att_donnee

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDatt_donnee mWD_att_donnee = new GWDatt_donnee();

/**
 * titre_libelle
 */
class GWDtitre_libelle extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°3 de run.Zr_donnees_demo.titre_libelle
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905901218495214228l);

super.setChecksum("1092357551");

super.setNom("titre_libelle");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Libellé");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(8, 8);

super.setTailleInitiale(339, 20);

super.setPlan(0);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDtitre_libelle mWD_titre_libelle = new GWDtitre_libelle();

/**
 * donnee_libelle
 */
class GWDdonnee_libelle extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°4 de run.Zr_donnees_demo.donnee_libelle
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905901278624901538l);

super.setChecksum("1092502731");

super.setNom("donnee_libelle");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Libellé");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(10, 34);

super.setTailleInitiale(339, 20);

super.setPlan(0);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 1, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDdonnee_libelle mWD_donnee_libelle = new GWDdonnee_libelle();

/**
 * Initialise tous les champs de run.Zr_donnees_demo
 */
public void initialiserSousObjets()
{
////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de run.Zr_donnees_demo
////////////////////////////////////////////////////////////////////////////
super.initialiserSousObjets();
mWD_att_titre.initialiserObjet();
super.ajouterAttributZR(mWD_att_titre);
mWD_att_donnee.initialiserObjet();
super.ajouterAttributZR(mWD_att_donnee);
mWD_titre_libelle.initialiserObjet();
super.ajouterChamp("titre_libelle",mWD_titre_libelle);
mWD_donnee_libelle.initialiserObjet();
super.ajouterChamp("donnee_libelle",mWD_donnee_libelle);
creerAttributAuto();
}
public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectCompPrincipal(0,0,360,64);

super.setQuid(2905901128290656021l);

super.setChecksum("1082124851");

super.setNom("Zr_donnees_demo");

super.setType(30);

super.setBulle("");

super.setLibelle("Zone répétée");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(0, 265);

super.setTailleInitiale(360, 171);

super.setValeurInitiale("");

super.setPlan(6);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(74);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(43);

super.setModeAscenseur(1, 1);

super.setModeSelection(99);

super.setSaisieEnCascade(false);

super.setLettreAppel(65535);

super.setEnregistrementSortieLigne(true);

super.setPersistant(false);

super.setParamAffichage(0, 0, 1, 360, 64);

super.setBtnEnrouleDeroule(true);

super.setScrollRapide(false, null);

super.setDeplacementParDnd(0);

super.setSwipe(0, "", false, false, "", false, false);

super.setRecyclageChamp(true);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setStyleLibelle(getCouleur_GEN(0xff262626), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff808080));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setStyleSeparateurVerticaux(false, getCouleur_GEN(0xff000000, true));

super.setStyleSeparateurHorizontaux(0, getCouleur_GEN(0xff000000, true));

super.setDessinerLigneVide(false);

super.setCouleurCellule(getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000), getCouleur_GEN(0xffbababa), getCouleur_GEN(0xff000000, true));

super.setImagePlusMoins("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E2_4O");

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
initialiserSousObjets();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDZr_donnees_demo mWD_Zr_donnees_demo;

/**
 * faceImageedit
 */
class GWDfaceImageedit extends WDChampSaisieMultiLigne
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°75 de run.faceImageedit
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,344,21);
super.setRectCompPrincipal(0,21,339,67);

super.setQuid(2905905496414774158l);

super.setChecksum("1224499063");

super.setNom("faceImageedit");

super.setType(20001);

super.setBulle("");

super.setLibelle("Face image string");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(8, 438);

super.setTailleInitiale(344, 90);

super.setValeurInitiale("");

super.setPlan(6);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(75);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(44);

super.setModeAscenseur(2, 1);

super.setEffacementAutomatique(false);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDfaceImageedit mWD_faceImageedit;

/**
 * Expiration_crpto
 */
class GWDExpiration_crpto extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°76 de run.Expiration_crpto
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905908253837438595l);

super.setChecksum("1278152708");

super.setNom("Expiration_crpto");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Libellé");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(10, 536);

super.setTailleInitiale(339, 20);

super.setPlan(6);

super.setCadrageHorizontal(1);

super.setCadrageVertical(1);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(76);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 1, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDExpiration_crpto mWD_Expiration_crpto;

/**
 * Combo_empreinte_decode
 */
class GWDCombo_empreinte_decode extends WDCombo
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°77 de run.Combo_empreinte_decode
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,334,21);
super.setRectCompPrincipal(0,21,334,40);

super.setQuid(2905925880591796340l);

super.setChecksum("1493904802");

super.setNom("Combo_empreinte_decode");

super.setType(10002);

super.setBulle("");

super.setLibelle("Empreinte présente");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(13, 224);

super.setTailleInitiale(334, 63);

super.setValeurInitiale("");

super.setPlan(7);

super.setCadrageHorizontal(0);

super.setContenuInitial("");

super.setTriee(false);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(77);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(52);

super.setLettreAppel(65535);

super.setRetourneValeurProgrammation(false);

super.setPersistant(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_32_6_42_6", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {6, 42, 6, 32}, getCouleur_GEN(0xffffffff), 5, 1, null));

super.setStyleElement(getCouleur_GEN(0xff000000), getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 48);

super.setStyleSelection(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xffffffff, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleBouton(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true));

super.setParamSaisieObligatoire(false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCombo_empreinte_decode mWD_Combo_empreinte_decode;

/**
 * img_selfie1
 */
class GWDimg_selfie1 extends WDChampImage
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°78 de run.img_selfie1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );

super.setQuid(2905925957901291688l);

super.setChecksum("1486818091");

super.setNom("img_selfie1");

super.setType(30001);

super.setBulle("");

super.setLibelle("");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(false);

super.setEtatInitial(0);

super.setPositionInitiale(79, 3);

super.setTailleInitiale(202, 202);

super.setValeurInitiale("");

super.setPlan(7);

super.setTailleMin(0, 0);

super.setTailleMax(**********, **********);

super.setVisibleInitial(true);

super.setAltitude(78);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setTransparence(1);

super.setParamImage(2097158, 0, true, 100);

super.setSymetrie(0);

super.setZoneClicage(true);

super.setPCodeMultitouch(false);

super.setChargementEnTacheDeFond(false);

super.setOrientationExif(true);

super.setParamAnimation(1, 1, false, 300, true, false);

super.setAnimationInitiale(false);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(false);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), -1, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xffc00000), getCouleur_GEN(0xff400000), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 1, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDimg_selfie1 mWD_img_selfie1;

/**
 * Simple23
 */
class GWDSimple23 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°79 de run.Simple23
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905926095347027368l);

super.setChecksum("1493598507");

super.setNom("Simple23");

super.setType(4);

super.setBulle("");

super.setLibelle("capturer");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(107, 295);

super.setTailleInitiale(146, 48);

super.setPlan(7);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(79);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(53);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple23
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_cryptoFp = new WDChaineU();




try
{
// <COMPILE IF Configuration="Application Android">
// cryptoFp est une chaine = JSONToString(retourapi.originalResult.fingerprints)

vWD_cryptoFp.setValeur(WDAPIJSON.jsonVersChaine(GWDPintegrationSdk.getInstance().vWD_retourapi.get("originalResult").get("fingerprints")));


// ScanAndCompareFingerPrints(cryptoFp,  Combo_empreinte_decode..StoredValue)
GWDCPSDKInt.ScanAndCompareFingerPrints(vWD_cryptoFp.getString(),mWD_Combo_empreinte_decode.getProp(EWDPropriete.PROP_VALEURMEMORISEE).getString());


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple23 mWD_Simple23;

/**
 * Simple24
 */
class GWDSimple24 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°80 de run.Simple24
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905926095347158534l);

super.setChecksum("1493729673");

super.setNom("Simple24");

super.setType(4);

super.setBulle("");

super.setLibelle("Retour");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(18, 502);

super.setTailleInitiale(146, 48);

super.setPlan(7);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(80);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(55);

super.setLettreAppel(65535);

super.setTypeBouton(3);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple24
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

try
{
// MaFenêtre..plan = 6
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,6);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple24 mWD_Simple24;

/**
 * Simple25
 */
class GWDSimple25 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°81 de run.Simple25
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905926649451611224l);

super.setChecksum("1547401308");

super.setNom("Simple25");

super.setType(4);

super.setBulle("");

super.setLibelle("comparer");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(192, 502);

super.setTailleInitiale(146, 48);

super.setPlan(7);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(81);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(56);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple25
 */
public void clicSurBoutonGauche()
// 
{
super.clicSurBoutonGauche();

// 

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_chaineTemplateTosend = new WDChaineU();




try
{
// chaineTemplateTosend est une chaine = JSONToString(retourapi.originalResult.fingerprints)

vWD_chaineTemplateTosend.setValeur(WDAPIJSON.jsonVersChaine(GWDPintegrationSdk.getInstance().vWD_retourapi.get("originalResult").get("fingerprints")));


// fp_string = "Contenu est : " +fpBase64Encoded
mWD_fp_string.setValeur(new WDChaineU("Contenu est : ").opPlus(GWDPintegrationSdk.getInstance().vWD_fpBase64Encoded));

// <compile if Configuration="Application Android">
// 	CryptoFingerAndTemplateCompare(chaineTemplateTosend, fpBase64Encoded)
GWDCPSDKInt.CryptoFingerAndTemplateCompare(vWD_chaineTemplateTosend.getString(),GWDPintegrationSdk.getInstance().vWD_fpBase64Encoded.getString());


}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple25 mWD_Simple25;

/**
 * fp_string
 */
class GWDfp_string extends WDChampSaisieSimple
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°82 de run.fp_string
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setRectLibelle(0,0,309,21);
super.setRectCompPrincipal(0,21,304,75);

super.setQuid(2905929845079334677l);

super.setChecksum("1719456369");

super.setNom("fp_string");

super.setType(20001);

super.setBulle("");

super.setLibelle("Fp string");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setTaille(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(18, 362);

super.setTailleInitiale(309, 98);

super.setValeurInitiale("");

super.setPlan(7);

super.setCadrageHorizontal(0);

super.setMotDePasse(false);

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(82);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setIndication("");

super.setNumTab(54);

super.setModeAscenseur(2, 2);

super.setEffacementAutomatique(true);

super.setFinSaisieAutomatique(false);

super.setLettreAppel(65535);

super.setSelectionEnAffichage(true);

super.setPersistant(false);

super.setClavierEnSaisie(true);

super.setMasqueAffichage(new WDChaineU(""));

super.setParamBtnActionClavier(0, "");

super.setMiseABlancSiZero(false);

super.setVerifieOrthographe(true);

super.setTauxParallaxe(0, 0, false);

super.setBoutonSuppression(0);

super.setPresenceLibelle(true);

super.setOpacite(100);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000));

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setCadreInterne(WDCadreFactory.creerCadre_GEN("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\<EMAIL>?E5_3NP_8_8_8_8", new int[] {1,4,1,2,2,2,1,4,1}, new int[] {8, 8, 8, 8}, getCouleur_GEN(0xffffffff), 5, 1, null), 2, 0, 0, 0);

super.setStyleSaisie(getCouleur_GEN(0xff000000), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStyleTexteIndication(getCouleur_GEN(0xff8b8680), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0);

super.setStyleJeton(WDCadreFactory.creerCadre_GEN(27, getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff740500), getCouleur_GEN(0xffffffff), 16.000000, 16.000000, 1, 1, 0, null), getCouleur_GEN(0xfff48542), getCouleur_GEN(0xff000001, true), "", 1);

super.setParamSaisieObligatoire(false, true);

super.setParamErreurSaisieInvalide("", true, false, false);

super.setParamErreurSaisieObligatoire("", true, false, false);

super.setParamIndicationSaisieObligatoire(true, false, false);

super.setVisualisationMarkdown(false);

super.setStyleChampErreurSaisieInvalide(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieInvalide(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieInvalide(21, 141);

super.setStyleChampErreurSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleErreurSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoErreurSaisieObligatoire(21, 141);

super.setStyleChampIndicationSaisieObligatoire(null, null, getCouleur_GEN(0xff0000ff), getCouleur_GEN(0xff0000ff));

super.setStyleLibelleIndicationSaisieObligatoire(getCouleur_GEN(0xff68635f), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f));

super.setStylePictoIndicationSaisieObligatoire(21, 141);

super.setParamAnimationChamp(42, 1, 200);

super.setParamBoutonActionGauche("", 1, 0, 0, false);

super.setParamBoutonActionDroit("", 1, 0, 0, false);

super.setMargeBoutonActionGauche(2, 2, 2, 2);

super.setMargeBoutonActionDroit(2, 2, 2, 2);

super.setRemplissageAuto(1);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDfp_string mWD_fp_string;

/**
 * Simple26
 */
class GWDSimple26 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°83 de run.Simple26
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905930347617888285l);

super.setChecksum("1746837374");

super.setNom("Simple26");

super.setType(4);

super.setBulle("");

super.setLibelle("comparer");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(136, 849);

super.setTailleInitiale(89, 48);

super.setPlan(6);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(83);

super.setAncrageInitial(4, 1000, 1000, 500, 0, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(51);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Simple26
 */
public void clicSurBoutonGauche()
// mafenetre..plan = 7
{
super.clicSurBoutonGauche();

// mafenetre..plan = 7

try
{
// mafenetre..plan = 7
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,7);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDSimple26 mWD_Simple26;


////////////////////////////////////////////////////////////////////////////
// Procédures utilisateur de run
////////////////////////////////////////////////////////////////////////////
// Résumé : <indiquez ici ce que fait la procédure>
// Syntaxe :
// genererCryptographe ()
// 
// Paramètres :
// Aucun
// Valeur de retour :
// Aucune
// 
// Exemple :
// <Indiquez ici un exemple d'utilisation>
// 
// procédure genererCryptographe()
public void fWD_genererCryptographe()
{
// procédure genererCryptographe()
initExecProcLocale("genererCryptographe");



try
{
final WDProcedureInterne []fWDI_retourplan = new WDProcedureInterne[1];
fWDI_retourplan[0] = new WDProcedureInterne()
{
@Override
public String getNom()
{
return "retourplan";
}
@Override
public void executer_void(WDObjet... parametres)
{
verifNbParametres(parametres.length, 0);
fWD_retourplan();
}
// 	PROCÉDURE INTERNE retourplan()<thread principal >
public void fWD_retourplan_autoWX()
{
// 	PROCÉDURE INTERNE retourplan()<thread principal >
initExecProcInterne();



try
{

try
{
// 		MaFenêtre..Plan = 99 //1 initialement
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,99);

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcInterne();

}
}

public void fWD_retourplan()
{
WDProcExecutorUIThread.getInstance().initExecAutoProcedure_GEN(fWDI_retourplan[0], 1, 0l, 0, 0);
}

};
WDAppelContexte.getContexte().declarerProcedureInterne(fWDI_retourplan[0]);

try
{
// ToastAffiche("Préparation et sécurisation des données...", toastLong, cvMilieu, chCentre)
WDAPIToast.toastAffiche("Préparation et sécurisation des données...",1,1,1);

// <COMPILE SI Configuration="Application Android">
// 	SI Ping("www.checktatoo.com") = Vrai ALORS
if(WDAPINet.ping("www.checktatoo.com").opEgal(true, 0))
{
// 		pos1, pos2, pos3, pos4, pos5, pos6, pos7, pos8, pos9, pos10, sGsAi_selfie					est une chaîne
WDObjet vWD_pos1 = new WDChaineU();


WDObjet vWD_pos2 = new WDChaineU();


WDObjet vWD_pos3 = new WDChaineU();


WDObjet vWD_pos4 = new WDChaineU();


WDObjet vWD_pos5 = new WDChaineU();


WDObjet vWD_pos6 = new WDChaineU();


WDObjet vWD_pos7 = new WDChaineU();


WDObjet vWD_pos8 = new WDChaineU();


WDObjet vWD_pos9 = new WDChaineU();


WDObjet vWD_pos10 = new WDChaineU();


WDObjet vWD_sGsAi_selfie = new WDChaineU();



// 		sInfoRépertoire																	est une chaîne	= SysRepStockageExterne(0,sseAppDocument)
WDObjet vWD_sInfoRepertoire = new WDChaineU();


vWD_sInfoRepertoire.setValeur(WDAPIStorage.sysRepStockageExterne(0,6));


// 		sInfoRépertoire	= ChaîneSupprime(sInfoRépertoire, [fSep()]+"Documents")
vWD_sInfoRepertoire.setValeur(WDAPIChaine.chaineSupprime(vWD_sInfoRepertoire,new WDChaineOptionnelle(WDAPIFichier.fSep()).opPlus("Documents")));

// 		sInfoRépertoire	= sInfoRépertoire+[fSep()]
vWD_sInfoRepertoire.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())));

// 		SI fRepExiste(sInfoRépertoire) ALORS
if(WDAPIFichier.fRepertoireExiste(vWD_sInfoRepertoire.getString()).getBoolean())
{
// 			UnFichier, ResListeFichier sont des chaînes
WDObjet vWD_UnFichier = new WDChaineU();


WDObjet vWD_ResListeFichier = new WDChaineU();



// 			ResListeFichier = fListeFichier(sInfoRépertoire+[fSep()]+"*.png")
vWD_ResListeFichier.setValeur(WDAPIFichier.fListeFichier(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus("*.png").getString()));

// 			sPrefixNomFichierEmpreinte	est une chaîne
WDObjet vWD_sPrefixNomFichierEmpreinte = new WDChaineU();



// 			POUR TOUTE CHAÎNE UnFichier DE ResListeFichier SÉPARÉE PAR RC
IWDParcours parcours3 = null;
try
{
parcours3 = WDParcoursSousChaine.pourTout(vWD_UnFichier, null, null, vWD_ResListeFichier, "\r\n", 0x2);
while(parcours3.testParcours())
{
// 				sNomSansExtension	est une chaîne	= ExtraitChaîne(UnFichier, 1, ".png")
WDObjet vWD_sNomSansExtension = new WDChaineU();


vWD_sNomSansExtension.setValeur(WDAPIChaine.extraitChaine(parcours3.getVariableParcours(),1,new WDChaineU(".png")));


// 				sPrefixNomFichierEmpreinte	= Droite(sNomSansExtension,Taille(gsRefCapture+"_1674640945537_finger_10"))
vWD_sPrefixNomFichierEmpreinte.setValeur(WDAPIChaine.droite(vWD_sNomSansExtension,WDAPIChaine.taille(vWD_gsRefCapture.opPlus("_1674640945537_finger_10")).getInt()));

// 				sPrefixNomFichierEmpreinte	= Remplace(sPrefixNomFichierEmpreinte,"/","")
vWD_sPrefixNomFichierEmpreinte.setValeur(WDAPIChaine.remplace(vWD_sPrefixNomFichierEmpreinte,new WDChaineU("/"),new WDChaineU("")));

// 				tabVal		est un tableau de chaînes	= ChaîneDécoupe(sPrefixNomFichierEmpreinte,"_")
WDObjet vWD_tabVal = WDVarNonAllouee.ref;
vWD_tabVal = new WDTableauSimple(1, new int[]{0}, 0, 16, 0);
vWD_tabVal.setValeur(WDAPIChaine.chaineDecoupe(vWD_sPrefixNomFichierEmpreinte,new WDObjet[] {new WDChaineU("_")} ));


// 				SI tabVal[1] = gsRefCapture ALORS 
if(vWD_tabVal.get(1).opEgal(vWD_gsRefCapture, 0))
{
// 					SELON tabVal[4] 
// Délimiteur de visibilité pour ne pas étendre la visibilité de la variable temporaire _WDExpSelon
{
// 					SELON tabVal[4] 
WDObjet _WDExpSelon1 = vWD_tabVal.get(4);
// 					SELON tabVal[4] 
if(_WDExpSelon1.opEgal(1, 0))
{
// 							pos1 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"						
vWD_pos1.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(2, 0))
{
// 							pos2 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos2.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(3, 0))
{
// 							pos3 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos3.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(4, 0))
{
// 							pos4 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos4.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(5, 0))
{
// 							pos5 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos5.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(6, 0))
{
// 							pos6 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos6.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(7, 0))
{
// 							pos7 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos7.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(8, 0))
{
// 							pos8 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos8.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(9, 0))
{
// 							pos9 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos9.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(10, 0))
{
// 							pos10 = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_pos10.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else if(_WDExpSelon1.opEgal(20, 0))
{
// 							sGsAi_selfie = sInfoRépertoire+[fSep()]+sPrefixNomFichierEmpreinte+".png"
vWD_sGsAi_selfie.setValeur(vWD_sInfoRepertoire.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())).opPlus(vWD_sPrefixNomFichierEmpreinte).opPlus(".png"));

}
// 					SELON tabVal[4] 
else {
}

}

}

}

}
finally
{
if(parcours3 != null)
{
parcours3.finParcours();
}
}


}
else
{
// 			Erreur("Le répertoire n'existe pas!!")
WDAPIDialogue.erreur("Le répertoire n'existe pas!!");

}

// 		QUAND EXCEPTION DANS

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables locales au traitement
// (En WLangage les variables sont encore visibles après la fin du bloc dans lequel elles sont déclarées)
////////////////////////////////////////////////////////////////////////////
WDObjet vWD_oRequete = WDVarNonAllouee.ref;
WDObjet vWD_oReponse = WDVarNonAllouee.ref;
WDObjet vWD_Contenu = WDVarNonAllouee.ref;

WDException.local();
try
{
// 			oRequête	est une httpRequête
vWD_oRequete = new WDInstance( new WDHTTPRequete() );


// 			oRéponse	est une httpRéponse
vWD_oReponse = new WDInstance( new WDHTTPReponse() );


// 			oRequête..URL			= gUrl+"/person/new/json"
vWD_oRequete.setProp(EWDPropriete.PROP_URL,GWDPintegrationSdk.getInstance().vWD_gUrl.opPlus("/person/new/json"));

// 			oRequête..Méthode		= httpPost
vWD_oRequete.setProp(EWDPropriete.PROP_METHODE,2);

// 			SI HTTPTimeOut() > 300s ALORS HTTPTimeOut(300s)
if(WDAPIHttp.HTTPTimeout().opSup((new WDDuree("0000500000"))))
{
// 			SI HTTPTimeOut() > 300s ALORS HTTPTimeOut(300s)
WDAPIHttp.HTTPTimeout((new WDDuree("0000500000")));

}

// 			oRequête.TimeoutConnexion	= 300s
vWD_oRequete.setProp(EWDPropriete.PROP_TIMEOUTCONNEXION,(new WDDuree("0000500000")));

// 			oRequête.DuréeNonRéponse	= 300s
vWD_oRequete.setProp(EWDPropriete.PROP_DUREENONREPONSE,(new WDDuree("0000500000")));

// 			oRequête..ContentType		= "multipart/form-data"
vWD_oRequete.setProp(EWDPropriete.PROP_CONTENTTYPE,"multipart/form-data");

// 			oRequête..Contenu			= ""
vWD_oRequete.setProp(EWDPropriete.PROP_CONTENU,"");

// 			HTTPCréeFormulaire("FormData")
WDAPIHttp.HTTPCreeFormulaire("FormData");

// 			HTTPAjouteParamètre("FormData", "nom", ChaîneVersUTF8(sai_nom_crypto))
WDAPIHttp.HTTPAjouteParametre("FormData","nom",WDAPIChaine.chaineVersUTF8(mWD_sai_nom_crypto));

// 			HTTPAjouteParamètre("FormData", "prenom", ChaîneVersUTF8(sai_prenom_crypto))
WDAPIHttp.HTTPAjouteParametre("FormData","prenom",WDAPIChaine.chaineVersUTF8(mWD_sai_prenom_crypto));

// 			HTTPAjouteParamètre("FormData", "telephone", ChaîneVersUTF8(sai_telephone_crypto))
WDAPIHttp.HTTPAjouteParametre("FormData","telephone",WDAPIChaine.chaineVersUTF8(mWD_sai_telephone_crypto));

// 			HTTPAjouteParamètre("FormData", "nationalite", ChaîneVersUTF8(sai_nationalite_crypto))
WDAPIHttp.HTTPAjouteParametre("FormData","nationalite",WDAPIChaine.chaineVersUTF8(mWD_sai_nationalite_crypto));

// 			HTTPAjouteParamètre("FormData", "dateNaiss", ChaîneVersUTF8(sai_dateNaissance_crypto))
WDAPIHttp.HTTPAjouteParametre("FormData","dateNaiss",WDAPIChaine.chaineVersUTF8(mWD_sai_dateNaissance_crypto));

// 			HTTPAjouteParamètre("FormData", "lieuNaiss", ChaîneVersUTF8(sai_lieuNaissance_crypto))
WDAPIHttp.HTTPAjouteParametre("FormData","lieuNaiss",WDAPIChaine.chaineVersUTF8(mWD_sai_lieuNaissance_crypto));

// 			HTTPAjouteParamètre("FormData", "email", ChaîneVersUTF8(sai_email_crypto))
WDAPIHttp.HTTPAjouteParametre("FormData","email",WDAPIChaine.chaineVersUTF8(mWD_sai_email_crypto));

// 			HTTPAjouteParamètre("FormData", "ref", ChaîneVersUTF8(gsRefCapture))
WDAPIHttp.HTTPAjouteParametre("FormData","ref",WDAPIChaine.chaineVersUTF8(vWD_gsRefCapture));

// 			SI sGsAi_selfie <> "" ALORS 
if(vWD_sGsAi_selfie.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "face", sGsAi_selfie)
WDAPIHttp.HTTPAjouteFichier("FormData","face",vWD_sGsAi_selfie.getString());

}

// 			SI pos1 <> "" ALORS 
if(vWD_pos1.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpr1", pos1)
WDAPIHttp.HTTPAjouteFichier("FormData","fpr1",vWD_pos1.getString());

}

// 			SI pos2 <> "" ALORS 				
if(vWD_pos2.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpr2", pos2)
WDAPIHttp.HTTPAjouteFichier("FormData","fpr2",vWD_pos2.getString());

}

// 			SI pos3 <> "" ALORS 	
if(vWD_pos3.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpr3", pos3)
WDAPIHttp.HTTPAjouteFichier("FormData","fpr3",vWD_pos3.getString());

}

// 			SI pos4 <> "" ALORS 	
if(vWD_pos4.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpr4", pos4)
WDAPIHttp.HTTPAjouteFichier("FormData","fpr4",vWD_pos4.getString());

}

// 			SI pos5 <> "" ALORS 
if(vWD_pos5.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpr5", pos5)
WDAPIHttp.HTTPAjouteFichier("FormData","fpr5",vWD_pos5.getString());

}

// 			SI pos6 <> "" ALORS 
if(vWD_pos6.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpl1", pos6)
WDAPIHttp.HTTPAjouteFichier("FormData","fpl1",vWD_pos6.getString());

}

// 			SI pos7 <> "" ALORS 
if(vWD_pos7.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpl2", pos7)
WDAPIHttp.HTTPAjouteFichier("FormData","fpl2",vWD_pos7.getString());

}

// 			SI pos8 <> "" ALORS 
if(vWD_pos8.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpl3", pos8)
WDAPIHttp.HTTPAjouteFichier("FormData","fpl3",vWD_pos8.getString());

}

// 			SI pos9 <> "" ALORS 
if(vWD_pos9.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpl4", pos9)
WDAPIHttp.HTTPAjouteFichier("FormData","fpl4",vWD_pos9.getString());

}

// 			SI pos10 <> "" ALORS 
if(vWD_pos10.opDiff(""))
{
// 				HTTPAjouteFichier("FormData", "fpl5", pos10)
WDAPIHttp.HTTPAjouteFichier("FormData","fpl5",vWD_pos10.getString());

}

// 			ToastAffiche("Transmission des données pour génération du cryptographe...", toastLong, cvMilieu, chCentre)
WDAPIToast.toastAffiche("Transmission des données pour génération du cryptographe...",1,1,1);

// 			oRéponse = HTTPEnvoieFormulaire("FormData", oRequête)
vWD_oReponse.setValeur(WDAPIHttp.HTTPEnvoieFormulaire("FormData",vWD_oRequete));

// 			Contenu	est un JSON
vWD_Contenu = new WDInstance( new WDJSON() );


// 			SI ErreurDétectée ALORS
if(WDObjet.ErreurDetectee.getBoolean())
{
// 				Erreur(ErreurInfo(errComplet))
WDAPIDialogue.erreur(WDAPIVM.erreurInfo(19).getString());

}
else
{
// 				SI oRéponse.CodeEtat = 200 ALORS // Requête traitée avec succès
if(vWD_oReponse.getProp(EWDPropriete.PROP_CODEETAT).opEgal(200, 0))
{
// 					Contenu = oRéponse.Contenu
vWD_Contenu.setValeur(vWD_oReponse.getProp(EWDPropriete.PROP_CONTENU));

// 					ToastAffiche(Contenu.Message, toastLong, cvMilieu, chCentre)
WDAPIToast.toastAffiche(vWD_Contenu.get("Message").getString(),1,1,1);

}
else
{
// 					Info("Une erreur est survenue " +  ErreurInfo() + oRéponse.CodeEtat + oRéponse.DescriptionCodeEtat)
WDAPIDialogue.info(new WDChaineU("Une erreur est survenue ").opPlus(WDAPIVM.erreurInfo()).opPlus(vWD_oReponse.getProp(EWDPropriete.PROP_CODEETAT)).opPlus(vWD_oReponse.getProp(EWDPropriete.PROP_DESCRIPTIONCODEETAT)).getString());

}

}

}
catch(WDException eCatch)
{
eCatch.catch_GEN();
// 			Erreur(HErreurInfo(hErrComplet))
WDAPIDialogue.erreur(WDAPIHF.hErreurInfo(1089).getString());

// 			Erreur(ErreurInfo(errComplet))
WDAPIDialogue.erreur(WDAPIVM.erreurInfo(19).getString());

// 			Erreur(ExceptionInfo(errComplet))
WDAPIDialogue.erreur(WDAPIVM.exceptionInfo(19).getString());

// 			ExceptionAffiche()
WDAPIVM.exceptionAffiche();

// 			Error("A fatal error was detected.", ExceptionInfo(), "", "Stop the procedure.")
WDAPIDialogue.erreur("A fatal error was detected.",new String[] {WDAPIVM.exceptionInfo().getString(),"","Stop the procedure."} );

}

finally
{
WDException.reset();
}

// 		retourplan()
fWDI_retourplan[0].executer_void();

}
else
{
// 		Info(connextionMessage)// message de application hors réseau
WDAPIDialogue.info(GWDPintegrationSdk.getInstance().vWD_connextionMessage.getString());

}


// 	PROCÉDURE INTERNE retourplan()<thread principal >

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}
finally
{
finExecProcLocale();

}
}




/**
 * ActionBar
 */
class GWDActionBar extends WDActionBar
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de run.ActionBar
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setNom("ActionBar");

super.setNote("", "");

super.setParamBoutonGauche(true, 1, "", "");

super.setParamBoutonDroit(false, 0, "", "");

super.setStyleActionBar(getCouleur_GEN(0xffffffff), getCouleur_GEN(0xff616161), true, true);

super.setImageFond("");

super.setStyleBarreNavigation(getCouleur_GEN(0xff000001, true), getCouleur_GEN(0xff808080));

super.setHauteurBarre(56);

super.terminerInitialisation();
}

////////////////////////////////////////////////////////////////////////////
public boolean isBarrePersonnalisee()
{
return false;
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDActionBar mWD_ActionBar;

/**
 * Traitement: Global declarations of run
 */
// PROCEDURE MaFenêtre()
public void declarerGlobale(WDObjet[] WD_tabParam)
{
// PROCEDURE MaFenêtre()
super.declarerGlobale(WD_tabParam, 0, 0);
int WD_ntabParamLen = 0;
if(WD_tabParam!=null) WD_ntabParamLen = WD_tabParam.length;





try
{
// MaFenêtre..Plan = 1
WDContexte.getMaFenetre().setProp(EWDPropriete.PROP_PLAN,1);

// gsRefCapture						est une chaîne	= Today()+Left(Now(), 4)+Random(9999)
vWD_gsRefCapture = new WDChaineU();

vWD_gsRefCapture.setValeur(WDAPIDate.dateDuJour().opPlus(WDAPIChaine.gauche(WDAPIDate.maintenant(),4)).opPlus(WDAPIDiversSTD.hasard(9999)));

super.ajouterVariableGlobale("gsRefCapture",vWD_gsRefCapture);



// IMG_validation_pouce_droit..Visible		= Faux
mWD_IMG_validation_pouce_droit.setProp(EWDPropriete.PROP_VISIBLE,false);

// IMG_validation_main_droite..Visible		= Faux
mWD_IMG_validation_main_droite.setProp(EWDPropriete.PROP_VISIBLE,false);

// IMG_validation_main_gauche..Visible		= Faux
mWD_IMG_validation_main_gauche.setProp(EWDPropriete.PROP_VISIBLE,false);

// IMG_validation_pouce_gauche..Visible	= Faux
mWD_IMG_validation_pouce_gauche.setProp(EWDPropriete.PROP_VISIBLE,false);

// IMG_valider_selfie..Visible				= Faux
mWD_IMG_valider_selfie.setProp(EWDPropriete.PROP_VISIBLE,false);

// gnUmEpreinte	est un entier
vWD_gnUmEpreinte = new WDEntier4();

super.ajouterVariableGlobale("gnUmEpreinte",vWD_gnUmEpreinte);



// gbIsSelfie		est un booléen	= Faux
vWD_gbIsSelfie = new WDBooleen();

vWD_gbIsSelfie.setValeur(false);

super.ajouterVariableGlobale("gbIsSelfie",vWD_gbIsSelfie);



// gbIsEmpreinte	est un booléen	= Faux
vWD_gbIsEmpreinte = new WDBooleen();

vWD_gbIsEmpreinte.setValeur(false);

super.ajouterVariableGlobale("gbIsEmpreinte",vWD_gbIsEmpreinte);



// gbIsEmpreinteCrPD	est un booléen	= Faux
vWD_gbIsEmpreinteCrPD = new WDBooleen();

vWD_gbIsEmpreinteCrPD.setValeur(false);

super.ajouterVariableGlobale("gbIsEmpreinteCrPD",vWD_gbIsEmpreinteCrPD);



// gbIsEmpreinteCrMD	est un booléen	= Faux
vWD_gbIsEmpreinteCrMD = new WDBooleen();

vWD_gbIsEmpreinteCrMD.setValeur(false);

super.ajouterVariableGlobale("gbIsEmpreinteCrMD",vWD_gbIsEmpreinteCrMD);



// gbIsEmpreinteCrMG	est un booléen	= Faux
vWD_gbIsEmpreinteCrMG = new WDBooleen();

vWD_gbIsEmpreinteCrMG.setValeur(false);

super.ajouterVariableGlobale("gbIsEmpreinteCrMG",vWD_gbIsEmpreinteCrMG);



// gbIsEmpreinteCrPG	est un booléen	= Faux
vWD_gbIsEmpreinteCrPG = new WDBooleen();

vWD_gbIsEmpreinteCrPG.setValeur(false);

super.ajouterVariableGlobale("gbIsEmpreinteCrPG",vWD_gbIsEmpreinteCrPG);



// cryptoPath est une chaine
vWD_cryptoPath = new WDChaineU();

super.ajouterVariableGlobale("cryptoPath",vWD_cryptoPath);



// <compile si Configuration="Application Android">
// sInfoRép											est une chaîne	= SysRepStockageExterne(0,sseAppDocument)
vWD_sInfoRep = new WDChaineU();

vWD_sInfoRep.setValeur(WDAPIStorage.sysRepStockageExterne(0,6));

super.ajouterVariableGlobale("sInfoRép",vWD_sInfoRep);



// sInfoRép	= ChaîneSupprime(sInfoRép, [fSep()]+"Documents")
vWD_sInfoRep.setValeur(WDAPIChaine.chaineSupprime(vWD_sInfoRep,new WDChaineOptionnelle(WDAPIFichier.fSep()).opPlus("Documents")));

// sInfoRép	= sInfoRép+[fSep()]
vWD_sInfoRep.setValeur(vWD_sInfoRep.opPlus(new WDChaineOptionnelle(WDAPIFichier.fSep())));

// cryptoPath = sInfoRép+"crypto-tech-5.PNG"
vWD_cryptoPath.setValeur(vWD_sInfoRep.opPlus("crypto-tech-5.PNG"));

// Image3 = cryptoPath
mWD_Image3.setValeur(vWD_cryptoPath);

// Res est une chaîne = Encode(fChargeBuffer(sInfoRép+"crypto-tech-5.PNG"), encodeBASE64SansRC)
vWD_Res = new WDChaineU();

vWD_Res.setValeur(WDAPIEncode.encode(WDAPIFichier.fChargeBuffer(vWD_sInfoRep.opPlus("crypto-tech-5.PNG").getString()),11));

super.ajouterVariableGlobale("Res",vWD_Res);




}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
 public WDObjet vWD_gsRefCapture = WDVarNonAllouee.ref;
 public WDObjet vWD_gnUmEpreinte = WDVarNonAllouee.ref;
 public WDObjet vWD_gbIsSelfie = WDVarNonAllouee.ref;
 public WDObjet vWD_gbIsEmpreinte = WDVarNonAllouee.ref;
 public WDObjet vWD_gbIsEmpreinteCrPD = WDVarNonAllouee.ref;
 public WDObjet vWD_gbIsEmpreinteCrMD = WDVarNonAllouee.ref;
 public WDObjet vWD_gbIsEmpreinteCrMG = WDVarNonAllouee.ref;
 public WDObjet vWD_gbIsEmpreinteCrPG = WDVarNonAllouee.ref;
 public WDObjet vWD_cryptoPath = WDVarNonAllouee.ref;
 public WDObjet vWD_sInfoRep = WDVarNonAllouee.ref;
 public WDObjet vWD_Res = WDVarNonAllouee.ref;
////////////////////////////////////////////////////////////////////////////
// Création des champs de la fenêtre run
////////////////////////////////////////////////////////////////////////////
protected void creerChamps()
{
mWD_Simple = new GWDSimple();
mWD_OuvreCamera = new GWDOuvreCamera();
mWD_Simple3 = new GWDSimple3();
mWD_Simple4 = new GWDSimple4();
mWD_Image1 = new GWDImage1();
mWD_TexteInterne = new GWDTexteInterne();
mWD_Image2 = new GWDImage2();
mWD_BTN_Prise_empreinte_pouce_droit = new GWDBTN_Prise_empreinte_pouce_droit();
mWD_BTN_Prise_empreinte_main_droite = new GWDBTN_Prise_empreinte_main_droite();
mWD_BTN_Prise_empreinte_pouce_gauche = new GWDBTN_Prise_empreinte_pouce_gauche();
mWD_IMG_validation_main_droite = new GWDIMG_validation_main_droite();
mWD_IMG_validation_pouce_droit = new GWDIMG_validation_pouce_droit();
mWD_IMG_validation_pouce_gauche = new GWDIMG_validation_pouce_gauche();
mWD_BTN_Prise_empreinte_main_gauche = new GWDBTN_Prise_empreinte_main_gauche();
mWD_IMG_validation_main_gauche = new GWDIMG_validation_main_gauche();
mWD_BTN_Selfie_identite = new GWDBTN_Selfie_identite();
mWD_IMG_valider_selfie = new GWDIMG_valider_selfie();
mWD_Simple5 = new GWDSimple5();
mWD_Simple6 = new GWDSimple6();
mWD_Image3 = new GWDImage3();
mWD_TexteInterne1 = new GWDTexteInterne1();
mWD_sai_nom_crypto = new GWDsai_nom_crypto();
mWD_sai_prenom_crypto = new GWDsai_prenom_crypto();
mWD_sai_dateNaissance_crypto = new GWDsai_dateNaissance_crypto();
mWD_sai_lieuNaissance_crypto = new GWDsai_lieuNaissance_crypto();
mWD_sai_email_crypto = new GWDsai_email_crypto();
mWD_sai_telephone_crypto = new GWDsai_telephone_crypto();
mWD_sai_nationalite_crypto = new GWDsai_nationalite_crypto();
mWD_Simple7 = new GWDSimple7();
mWD_Simple8 = new GWDSimple8();
mWD_BTN_Prise_empreinte_main_droite1 = new GWDBTN_Prise_empreinte_main_droite1();
mWD_BTN_Prise_empreinte_pouce_gauche1 = new GWDBTN_Prise_empreinte_pouce_gauche1();
mWD_BTN_Prise_empreinte_main_gauche1 = new GWDBTN_Prise_empreinte_main_gauche1();
mWD_BTN_Selfie_identite1 = new GWDBTN_Selfie_identite1();
mWD_BTN_Prise_empreinte_pouce_droit1 = new GWDBTN_Prise_empreinte_pouce_droit1();
mWD_IMG_validation_main_droite1 = new GWDIMG_validation_main_droite1();
mWD_IMG_validation_pouce_gauche1 = new GWDIMG_validation_pouce_gauche1();
mWD_IMG_validation_main_gauche1 = new GWDIMG_validation_main_gauche1();
mWD_IMG_valider_selfie1 = new GWDIMG_valider_selfie1();
mWD_IMG_validation_pouce_droit1 = new GWDIMG_validation_pouce_droit1();
mWD_Biometrie = new GWDBiometrie();
mWD_Simple9 = new GWDSimple9();
mWD_Simple10 = new GWDSimple10();
mWD_IMG_SansNom32 = new GWDIMG_SansNom32();
mWD_LIB_Titre_de_fenetre = new GWDLIB_Titre_de_fenetre();
mWD_LIB_Traitement_en_cours = new GWDLIB_Traitement_en_cours();
mWD_Votre_cryptographe = new GWDVotre_cryptographe();
mWD_Image4 = new GWDImage4();
mWD_Titre_de_fenetre = new GWDTitre_de_fenetre();
mWD_BTN_Selfie_identite2 = new GWDBTN_Selfie_identite2();
mWD_IMG_valider_selfie2 = new GWDIMG_valider_selfie2();
mWD_BTN_Prise_empreinte_pouce_droit2 = new GWDBTN_Prise_empreinte_pouce_droit2();
mWD_IMG_validation_pouce_droit2 = new GWDIMG_validation_pouce_droit2();
mWD_BTN_Prise_empreinte_pouce_droit3 = new GWDBTN_Prise_empreinte_pouce_droit3();
mWD_IMG_validation_pouce_droit3 = new GWDIMG_validation_pouce_droit3();
mWD_BTN_Prise_empreinte_pouce_droit4 = new GWDBTN_Prise_empreinte_pouce_droit4();
mWD_IMG_validation_pouce_droit4 = new GWDIMG_validation_pouce_droit4();
mWD_InternalWindow1 = new GWDInternalWindow1();
mWD_Simple11 = new GWDSimple11();
mWD_Simple12 = new GWDSimple12();
mWD_Simple13 = new GWDSimple13();
mWD_Simple14 = new GWDSimple14();
mWD_Simple15 = new GWDSimple15();
mWD_Simple16 = new GWDSimple16();
mWD_edit_retour_API = new GWDedit_retour_API();
mWD_Simple17 = new GWDSimple17();
mWD_Simple18 = new GWDSimple18();
mWD_img_selfie = new GWDimg_selfie();
mWD_Simple19 = new GWDSimple19();
mWD_Simple20 = new GWDSimple20();
mWD_Simple21 = new GWDSimple21();
mWD_Simple22 = new GWDSimple22();
mWD_Combo_empreinte = new GWDCombo_empreinte();
mWD_Zr_donnees_demo = new GWDZr_donnees_demo();
mWD_faceImageedit = new GWDfaceImageedit();
mWD_Expiration_crpto = new GWDExpiration_crpto();
mWD_Combo_empreinte_decode = new GWDCombo_empreinte_decode();
mWD_img_selfie1 = new GWDimg_selfie1();
mWD_Simple23 = new GWDSimple23();
mWD_Simple24 = new GWDSimple24();
mWD_Simple25 = new GWDSimple25();
mWD_fp_string = new GWDfp_string();
mWD_Simple26 = new GWDSimple26();
mWD_ActionBar = new GWDActionBar();

}
////////////////////////////////////////////////////////////////////////////
// Initialisation de la fenêtre run
////////////////////////////////////////////////////////////////////////////
public  void initialiserObjet()
{
super.setQuid(2633563702951528347l);

super.setChecksum("887536416");

super.setNom("run");

super.setType(1);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setCurseurSouris(0);

super.setNote("", "");

super.setCouleur(getCouleur_GEN(0xff000000));

super.setCouleurFond(getCouleur_GEN(0xffffffff));

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 912);

super.setTitre("run");

super.setTailleMin(-1, -1);

super.setTailleMax(20000, 20000);

super.setVisibleInitial(true);

super.setPositionFenetre(1);

super.setPersistant(true);

super.setGFI(true);

super.setAnimationFenetre(0);

super.setImageFond("", 1, 0, 1);

super.setCouleurTexteAutomatique(getCouleur_GEN(0xff68635f));

super.setCouleurBarreSysteme(getCouleur_GEN(0xff000001, true));

super.setCopieEcranAutorisee(true);

super.setAncrageAuContenu(0);


activerEcoute();

////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de run
////////////////////////////////////////////////////////////////////////////
mWD_Simple.initialiserObjet();
super.ajouter("Simple", mWD_Simple);
mWD_OuvreCamera.initialiserObjet();
super.ajouter("OuvreCaméra", mWD_OuvreCamera);
mWD_Simple3.initialiserObjet();
super.ajouter("Simple3", mWD_Simple3);
mWD_Simple4.initialiserObjet();
super.ajouter("Simple4", mWD_Simple4);
mWD_Image1.initialiserObjet();
super.ajouter("Image1", mWD_Image1);
mWD_TexteInterne.initialiserObjet();
super.ajouter("TexteInterne", mWD_TexteInterne);
mWD_Image2.initialiserObjet();
super.ajouter("Image2", mWD_Image2);
mWD_BTN_Prise_empreinte_pouce_droit.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_pouce_droit", mWD_BTN_Prise_empreinte_pouce_droit);
mWD_BTN_Prise_empreinte_main_droite.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_main_droite", mWD_BTN_Prise_empreinte_main_droite);
mWD_BTN_Prise_empreinte_pouce_gauche.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_pouce_gauche", mWD_BTN_Prise_empreinte_pouce_gauche);
mWD_IMG_validation_main_droite.initialiserObjet();
super.ajouter("IMG_validation_main_droite", mWD_IMG_validation_main_droite);
mWD_IMG_validation_pouce_droit.initialiserObjet();
super.ajouter("IMG_validation_pouce_droit", mWD_IMG_validation_pouce_droit);
mWD_IMG_validation_pouce_gauche.initialiserObjet();
super.ajouter("IMG_validation_pouce_gauche", mWD_IMG_validation_pouce_gauche);
mWD_BTN_Prise_empreinte_main_gauche.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_main_gauche", mWD_BTN_Prise_empreinte_main_gauche);
mWD_IMG_validation_main_gauche.initialiserObjet();
super.ajouter("IMG_validation_main_gauche", mWD_IMG_validation_main_gauche);
mWD_BTN_Selfie_identite.initialiserObjet();
super.ajouter("BTN_Selfie_identite", mWD_BTN_Selfie_identite);
mWD_IMG_valider_selfie.initialiserObjet();
super.ajouter("IMG_valider_selfie", mWD_IMG_valider_selfie);
mWD_Simple5.initialiserObjet();
super.ajouter("Simple5", mWD_Simple5);
mWD_Simple6.initialiserObjet();
super.ajouter("Simple6", mWD_Simple6);
mWD_Image3.initialiserObjet();
super.ajouter("Image3", mWD_Image3);
mWD_TexteInterne1.initialiserObjet();
super.ajouter("TexteInterne1", mWD_TexteInterne1);
mWD_sai_nom_crypto.initialiserObjet();
super.ajouter("sai_nom_crypto", mWD_sai_nom_crypto);
mWD_sai_prenom_crypto.initialiserObjet();
super.ajouter("sai_prenom_crypto", mWD_sai_prenom_crypto);
mWD_sai_dateNaissance_crypto.initialiserObjet();
super.ajouter("sai_dateNaissance_crypto", mWD_sai_dateNaissance_crypto);
mWD_sai_lieuNaissance_crypto.initialiserObjet();
super.ajouter("sai_lieuNaissance_crypto", mWD_sai_lieuNaissance_crypto);
mWD_sai_email_crypto.initialiserObjet();
super.ajouter("sai_email_crypto", mWD_sai_email_crypto);
mWD_sai_telephone_crypto.initialiserObjet();
super.ajouter("sai_telephone_crypto", mWD_sai_telephone_crypto);
mWD_sai_nationalite_crypto.initialiserObjet();
super.ajouter("sai_nationalite_crypto", mWD_sai_nationalite_crypto);
mWD_Simple7.initialiserObjet();
super.ajouter("Simple7", mWD_Simple7);
mWD_Simple8.initialiserObjet();
super.ajouter("Simple8", mWD_Simple8);
mWD_BTN_Prise_empreinte_main_droite1.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_main_droite1", mWD_BTN_Prise_empreinte_main_droite1);
mWD_BTN_Prise_empreinte_pouce_gauche1.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_pouce_gauche1", mWD_BTN_Prise_empreinte_pouce_gauche1);
mWD_BTN_Prise_empreinte_main_gauche1.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_main_gauche1", mWD_BTN_Prise_empreinte_main_gauche1);
mWD_BTN_Selfie_identite1.initialiserObjet();
super.ajouter("BTN_Selfie_identite1", mWD_BTN_Selfie_identite1);
mWD_BTN_Prise_empreinte_pouce_droit1.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_pouce_droit1", mWD_BTN_Prise_empreinte_pouce_droit1);
mWD_IMG_validation_main_droite1.initialiserObjet();
super.ajouter("IMG_validation_main_droite1", mWD_IMG_validation_main_droite1);
mWD_IMG_validation_pouce_gauche1.initialiserObjet();
super.ajouter("IMG_validation_pouce_gauche1", mWD_IMG_validation_pouce_gauche1);
mWD_IMG_validation_main_gauche1.initialiserObjet();
super.ajouter("IMG_validation_main_gauche1", mWD_IMG_validation_main_gauche1);
mWD_IMG_valider_selfie1.initialiserObjet();
super.ajouter("IMG_valider_selfie1", mWD_IMG_valider_selfie1);
mWD_IMG_validation_pouce_droit1.initialiserObjet();
super.ajouter("IMG_validation_pouce_droit1", mWD_IMG_validation_pouce_droit1);
mWD_Biometrie.initialiserObjet();
super.ajouter("Biometrie", mWD_Biometrie);
mWD_Simple9.initialiserObjet();
super.ajouter("Simple9", mWD_Simple9);
mWD_Simple10.initialiserObjet();
super.ajouter("Simple10", mWD_Simple10);
mWD_IMG_SansNom32.initialiserObjet();
super.ajouter("IMG_SansNom32", mWD_IMG_SansNom32);
mWD_LIB_Titre_de_fenetre.initialiserObjet();
super.ajouter("LIB_Titre_de_fenêtre", mWD_LIB_Titre_de_fenetre);
mWD_LIB_Traitement_en_cours.initialiserObjet();
super.ajouter("LIB_Traitement_en_cours", mWD_LIB_Traitement_en_cours);
mWD_Votre_cryptographe.initialiserObjet();
super.ajouter("Votre_cryptographe", mWD_Votre_cryptographe);
mWD_Image4.initialiserObjet();
super.ajouter("Image4", mWD_Image4);
mWD_Titre_de_fenetre.initialiserObjet();
super.ajouter("Titre_de_fenêtre", mWD_Titre_de_fenetre);
mWD_BTN_Selfie_identite2.initialiserObjet();
super.ajouter("BTN_Selfie_identite2", mWD_BTN_Selfie_identite2);
mWD_IMG_valider_selfie2.initialiserObjet();
super.ajouter("IMG_valider_selfie2", mWD_IMG_valider_selfie2);
mWD_BTN_Prise_empreinte_pouce_droit2.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_pouce_droit2", mWD_BTN_Prise_empreinte_pouce_droit2);
mWD_IMG_validation_pouce_droit2.initialiserObjet();
super.ajouter("IMG_validation_pouce_droit2", mWD_IMG_validation_pouce_droit2);
mWD_BTN_Prise_empreinte_pouce_droit3.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_pouce_droit3", mWD_BTN_Prise_empreinte_pouce_droit3);
mWD_IMG_validation_pouce_droit3.initialiserObjet();
super.ajouter("IMG_validation_pouce_droit3", mWD_IMG_validation_pouce_droit3);
mWD_BTN_Prise_empreinte_pouce_droit4.initialiserObjet();
super.ajouter("BTN_Prise_empreinte_pouce_droit4", mWD_BTN_Prise_empreinte_pouce_droit4);
mWD_IMG_validation_pouce_droit4.initialiserObjet();
super.ajouter("IMG_validation_pouce_droit4", mWD_IMG_validation_pouce_droit4);
mWD_InternalWindow1.initialiserObjet();
super.ajouter("InternalWindow1", mWD_InternalWindow1);
mWD_Simple11.initialiserObjet();
super.ajouter("Simple11", mWD_Simple11);
mWD_Simple12.initialiserObjet();
super.ajouter("Simple12", mWD_Simple12);
mWD_Simple13.initialiserObjet();
super.ajouter("Simple13", mWD_Simple13);
mWD_Simple14.initialiserObjet();
super.ajouter("Simple14", mWD_Simple14);
mWD_Simple15.initialiserObjet();
super.ajouter("Simple15", mWD_Simple15);
mWD_Simple16.initialiserObjet();
super.ajouter("Simple16", mWD_Simple16);
mWD_edit_retour_API.initialiserObjet();
super.ajouter("edit_retour_API", mWD_edit_retour_API);
mWD_Simple17.initialiserObjet();
super.ajouter("Simple17", mWD_Simple17);
mWD_Simple18.initialiserObjet();
super.ajouter("Simple18", mWD_Simple18);
mWD_img_selfie.initialiserObjet();
super.ajouter("img_selfie", mWD_img_selfie);
mWD_Simple19.initialiserObjet();
super.ajouter("Simple19", mWD_Simple19);
mWD_Simple20.initialiserObjet();
super.ajouter("Simple20", mWD_Simple20);
mWD_Simple21.initialiserObjet();
super.ajouter("Simple21", mWD_Simple21);
mWD_Simple22.initialiserObjet();
super.ajouter("Simple22", mWD_Simple22);
mWD_Combo_empreinte.initialiserObjet();
super.ajouter("Combo_empreinte", mWD_Combo_empreinte);
mWD_Zr_donnees_demo.initialiserObjet();
super.ajouter("Zr_donnees_demo", mWD_Zr_donnees_demo);
mWD_faceImageedit.initialiserObjet();
super.ajouter("faceImageedit", mWD_faceImageedit);
mWD_Expiration_crpto.initialiserObjet();
super.ajouter("Expiration_crpto", mWD_Expiration_crpto);
mWD_Combo_empreinte_decode.initialiserObjet();
super.ajouter("Combo_empreinte_decode", mWD_Combo_empreinte_decode);
mWD_img_selfie1.initialiserObjet();
super.ajouter("img_selfie1", mWD_img_selfie1);
mWD_Simple23.initialiserObjet();
super.ajouter("Simple23", mWD_Simple23);
mWD_Simple24.initialiserObjet();
super.ajouter("Simple24", mWD_Simple24);
mWD_Simple25.initialiserObjet();
super.ajouter("Simple25", mWD_Simple25);
mWD_fp_string.initialiserObjet();
super.ajouter("fp_string", mWD_fp_string);
mWD_Simple26.initialiserObjet();
super.ajouter("Simple26", mWD_Simple26);
mWD_ActionBar.initialiserObjet();
super.ajouterActionBar(mWD_ActionBar);

super.terminerInitialisation();
}

////////////////////////////////////////////////////////////////////////////
public boolean isUniteAffichageLogique()
{
return false;
}

public WDProjet getProjet()
{
return GWDPintegrationSdk.getInstance();
}

public IWDEnsembleElement getEnsemble()
{
return GWDPintegrationSdk.getInstance();
}
public int getModeContexteHF()
{
return 1;
}
/**
* Retourne le mode d'affichage de l'ActionBar de la fenêtre.
*/
public int getModeActionBar()
{
return 1;
}
/**
* Retourne vrai si la fenêtre est maximisée, faux sinon.
*/
public boolean isMaximisee()
{
return true;
}
/**
* Retourne vrai si la fenêtre a une barre de titre, faux sinon.
*/
public boolean isAvecBarreDeTitre()
{
return true;
}
/**
* Retourne le mode d'affichage de la barre système de la fenêtre.
*/
public int getModeBarreSysteme()
{
return 1;
}
/**
* Retourne vrai si la fenêtre est munie d'ascenseurs automatique, faux sinon.
*/
public boolean isAvecAscenseurAuto()
{
return true;
}
/**
* Retourne Vrai si on doit appliquer un theme "dark" (sombre) ou Faux si on doit appliquer "light" (clair) à la fenêtre.
* Ce choix se base sur la couleur du libellé par défaut dans le gabarit de la fenêtre.
*/
public boolean isGabaritSombre()
{
return false;
}
public boolean isIgnoreModeNuit()
{
return false;
}
/**
* Retourne vrai si l'option de masquage automatique de l'ActionBar lorsqu'on scrolle dans un champ de la fenêtre a été activée.
*/
public boolean isMasquageAutomatiqueActionBar()
{
return false;
}
public static class WDActiviteFenetre extends WDActivite
{
protected WDFenetre getFenetre()
{
return GWDPintegrationSdk.getInstance().mWD_run;
}
}
/**
* Retourne le nom du gabarit associée à la fenêtre.
*/
public String getNomGabarit()
{
return "250 PHOENIX#WM";
}
/**
* Retourne le nom de la palette associe a la fenetre.
*/
public String getNomPalette()
{
return "";
}
}
