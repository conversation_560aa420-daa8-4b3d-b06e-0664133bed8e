<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.amlacameroon.sandbox"
    android:installLocation="auto"
    android:versionCode="354"
    android:versionName="0.0.354.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="true" />

    <queries xmlns:android="http://schemas.android.com/apk/res/android" >
        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />

            <data android:mimeType="image/*" />
        </intent>
        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />

            <data android:mimeType="video/*" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name="fr.pcsoft.wdjava.core.application.WDAndroidApp"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:extractNativeLibs="true"
        android:icon="@drawable/i_c_o_n_e________0"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.amlacameroon.sandbox.wdgen.GWDPintegrationSdk$WDLanceur"
            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar.Translucent" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.amlacameroon.sandbox.wdgen.GWDFFEN_Mobile_Camera_UI$WDActiviteFenetre"
            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
            android:hardwareAccelerated="false"
            android:screenOrientation="unspecified"
            android:theme="@android:style/Theme" />
        <activity
            android:name="com.amlacameroon.sandbox.wdgen.GWDFrun$WDActiviteFenetre"
            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
            android:hardwareAccelerated="false"
            android:screenOrientation="unspecified"
            android:theme="@android:style/Theme" />
        <activity
            android:name="com.amlacameroon.sandbox.wdgen.GWDFtt$WDActiviteFenetre"
            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
            android:hardwareAccelerated="false"
            android:screenOrientation="unspecified"
            android:theme="@android:style/Theme" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name="fr.pcsoft.wdjava.core.erreur.report.WDErrorReportActivity"
            android:theme="@android:style/Theme.DeviceDefault.Light" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.amlacameroon.sandbox.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <receiver
            android:name="fr.pcsoft.wdjava.core.utils.WDAppUtils$APKInstallBroadcastReceiver"
            android:exported="false" />

        <activity
            android:name="fr.pcsoft.wdjava.ui.activite.WDActivite$BlankActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar.Translucent" />

        <provider
            android:name="fr.pcsoft.wdjava.ui.searchbar.WDSearchHistory"
            android:authorities="com.amlacameroon.sandbox"
            android:exported="false" />

        <activity
            android:name="fr.pcsoft.wdjava.ui.searchbar.WDSearchActivity"
            android:exported="false"
            android:label="@string/app_name" >
            <intent-filter>
                <action android:name="android.intent.action.SEARCH" />
            </intent-filter>

            <meta-data
                android:name="android.app.searchable"
                android:resource="@xml/searchable" />
        </activity>

        <service android:name="fr.pcsoft.wdjava.core.service.WDServiceLocal" />

        <meta-data
            android:name="fr.pcsoft.first_window_name"
            android:value="run" />
        <meta-data
            android:name="fr.pcsoft.splash_orientation_phone"
            android:value="4" />
        <meta-data
            android:name="fr.pcsoft.splash_orientation_tablet"
            android:value="4" />

        <activity
            android:name="ai.tech5.pheonix.capture.activity.CaptureImageActivity"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" />
        <activity
            android:name="ai.tech5.finger.FingerCaptureActivity"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" />
        <activity
            android:name="ai.tech5.cryptograph.reader.api.ScanCryptographActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" />
        <activity
            android:name="ai.tech5.t5ncnn.MainActivity"
            android:exported="false" />
        <activity
            android:name="ai.tech5.t5opencv.MainActivity"
            android:exported="false" />
        <activity
            android:name="fr.pcsoft.wdandroid_wdl.wdgen.GWDFFEN_LICENCE_WX$WDActiviteFenetre"
            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
            android:hardwareAccelerated="false"
            android:theme="@android:style/Theme" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.amlacameroon.sandbox.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>