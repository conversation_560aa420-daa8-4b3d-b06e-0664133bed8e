# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.5.0"
  }
  digests {
    sha256: "\356<\221E(@\227\207\006\235\236\351\003$=\254\002\004\240\237\221\031\304\360\252\032*\251!\210\254\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.5.0"
  }
  digests {
    sha256: "5e{\331\033\v\002O\325\325~\271\267\3468\234\371l\230\320\246\315\257\215i\304x\276\020>\357%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.3.0"
  }
  digests {
    sha256: "\227\334E\257\357\343\241\344!\332B\270\266\351\371\004\221G|E\374ax >:^\212\005\356\205S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.8.0"
  }
  digests {
    sha256: "H\306J\025\354>\261\033\37333\236\\\353p\354\177\202\033\322\337\242\353\206u\353\32553\027\347\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.1.0"
  }
  digests {
    sha256: "\001W\336a\242\006@G\211j\005\200\200\363\375g\272W\255\232\224\205{?z66`$>?\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.0.0"
  }
  digests {
    sha256: "U\225\244\016\'\212{9\372x\240\224\220\343\327\363\372\251\\{\001DqH\2758\265\255\340`\\5"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.5.0"
  }
  digests {
    sha256: "\254\363\300\334R!\242\374n\362\327\003\251\376E\004\361q\271K\330C\234\266z\243\335guC\227#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.1.0"
  }
  digests {
    sha256: "\376\0227\277\002\235\006>\177)\3769\256\257s\357t\310\260\243e\204\206\374)\323\305C&e8\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.1.0"
  }
  digests {
    sha256: "\335wa[\323\335\'Z\373\021\266-\362[\256F\261\vJ\021|\323yC\257E\275\313\370uXR"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.5.0"
  }
  digests {
    sha256: " \255\025 \366%\317E^j\375r\220\230\203\006\323\251\210n\372\231>\b`\373\253\364\273?{\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.5.0"
  }
  digests {
    sha256: "M\340y5]\006\333\351\351\340a\271\006\357\224\026\232\331v<M\367\253\201D[:\327\325\035\374\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.7.10"
  }
  digests {
    sha256: "\347q\376t%\n\224>\217cFq2\001\377\035\214\271\\:]\032\221\242+e\251\340Oj\211\001"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.7.10"
  }
  digests {
    sha256: "\031\361\002\357\351b\237\216\253\3068S\255\025\3053\344|G\371\037\312\t(\\[\336\206\345\237\221\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.5.0"
  }
  digests {
    sha256: "\266\240y|\351\213\205\026U\304L\037\000%\313B2}\344y\':\340\257\v~2\'V\024\025\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\027\220\273\233\v>\376j\'\373\033\243\243S\v\v\232\246eNX\277\212\236\367\203\367g\345\r1\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.5.0"
  }
  digests {
    sha256: "|\0336<\342\001\304\312j\343&\201A\034Uc\316\205\372\216\t\274\371\344\332Ve\215\364p\371\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.0"
  }
  digests {
    sha256: "-\345(\326\211\216\225\357\002\r\"\331\377\337\235\037w\313\335\223\371-9\337\252]\\C\260\303\021\310"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\226\036\275\350\023\207y\242\231C\f\243%\250n(\304\220Rz\207\272Q\203b\372E\304L~~\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.1"
  }
  digests {
    sha256: ":\223\377\320R\204FC\300\376\371P\256Ux\333G\314\313\351\347\027mh\0233\030.#+\260\361"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.6.0"
  }
  digests {
    sha256: "\253\005G\304\225\2252\024\245\362\262\201P\001ON\002\0236x\325+w\327cu\352#^D=\275"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.6.0"
  }
  digests {
    sha256: "\207\r5\375&k-\257d\301\b\017\345\030$\323\303h\367\231S\204\250\327\305\374/\334@\353{:"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.5.0"
  }
  digests {
    sha256: "4Q\037\021v^\264\337\266\036{2\205\001\233d\210\261\017j\220\223\260(\252\020\214\240\323?\310\305"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.4.1"
  }
  digests {
    sha256: "\333d\233>\372$\343\020R\024S\020\260\002\333\221\3324k?\211\300\223\3548\303\004m\264^yN"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.1.0"
  }
  digests {
    sha256: "$.Dk\355=\263o\r\360\252\260\313\177\221\006\v\322\332\267\255\312\321\021z\337T\347$\315\035&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.8.0"
  }
  digests {
    sha256: "\255\276\304v\206\274\f\205\315\313*\236\301*$\371\302\256\336\027\027o\v\006\343h4y\333G\222\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.0"
  }
  digests {
    sha256: "\244X\247V#c\275Y\302\t\247\336\250G2j\016+&\263z\210=\303\342\220e\247\232\034x\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.0"
  }
  digests {
    sha256: "Il\214yfK\331p\306\246\276z\226\n \335\025}\374Z\242\361\216[\2033v\177\373\305\231\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-camera2"
    version: "1.0.1"
  }
  digests {
    sha256: "\376}x\332\311`\323\025]\355\'\363e}\r\247+\247\321\246rF\374\220*td\2712\203Xw"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-core"
    version: "1.0.1"
  }
  digests {
    sha256: "\326\275\275\n\242\236\370\322P\336\021x\017\224~V0\020!!\247\307g\036r:Q\001c\217dK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.2"
  }
  digests {
    sha256: "\207p\301\200\020>\v\214\004\240~\264\305\221S\257c\233\t\354\242]\352\351\275\315\257\206\235\036[k"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.auto.value"
    artifactId: "auto-value-annotations"
    version: "1.6.3"
  }
  digests {
    sha256: "\016\225\037\356\2141\366\002p\274FU:\205\206\000\033{\223\333\261*\354\0067:\251\232\025\003\222\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-lifecycle"
    version: "1.0.1"
  }
  digests {
    sha256: "\244T|I:\242\231{\263\243D`\241.b\177ho?\226!p\tR>\322\364\3661\361\265\333"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-view"
    version: "1.0.0-alpha28"
  }
  digests {
    sha256: "\"\377?\302\257\n>\353\260\207 H\347W*\275\232[eU\227q\307\333cn\215gQ:\203\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0-alpha03"
  }
  digests {
    sha256: "\377xO[\364)\320p\212\364\227\215\312@\001\000F\311<k\322\216\017\004[c\034\2060\022O\251"
  }
  repo_index {
  }
}
library {
  digests {
    sha256: "0\330\206~\034\360Z\200d\356-\225\367[\327\016[\206}\311\340\227D\314\331\2711\325\271\311Kp"
  }
}
library {
  digests {
    sha256: "\372\337\266[\266\017\214\304^\206H\237\037\260#\034\345\301i\340v~B\03637\257\216\3408C\357"
  }
}
library {
  digests {
    sha256: "\312T\017\207\254k5\026\0211j\326\366.\316\227$\314\230\352;FM\337\267\215\232\036\250\023\214\233"
  }
}
library {
  digests {
    sha256: "\260\360\245;l\324\255\244Hp\352)\214\376\372hd\346e\247\272.s}:<\rh\031U\337P"
  }
}
library {
  digests {
    sha256: "v;\037@j\315#Y\025BDijf\246!\234.\036\240\0010\304DT\210\353\016\204Ywt"
  }
}
library {
  digests {
    sha256: "\323\300E\351Yl\217\326\275\210\363#%\241\240O\345-D\224\370p\032\003\213\266On\275\305=b"
  }
}
library {
  digests {
    sha256: "\301\3668\274~\f\305\234x\223\226\373\327\266a\235I\277\256n\355\002\275\337\347S\3315\272\222]\263"
  }
}
library {
  digests {
    sha256: "\266(\273.=\355e\030\335\206\024Q{*\221p\253\036\001\325\211\366+\372\241\225\233\206qH\233\233"
  }
}
library {
  digests {
    sha256: "\234B6\245\025\352\214`\035Di\312\353)Au\301$\227\221\0019\031.\321C\023\004|Y\036\016"
  }
}
library {
  digests {
    sha256: "\307\202\032\344\324\177\210\331\224@{i\207\327\316@\2049\002\214\343D=iN1\226\304\273x\275\365"
  }
}
library {
  digests {
    sha256: "vi\257\333\277\254\016x\247\003\322[\254C\371n\220\031o\320\334\336\276E\211\200\274\024\365\345\275\376"
  }
}
library {
  digests {
    sha256: "\244\210!\206G\345\257-\222e\244\f]j\270\300J \222z\257\266\001\342\245\005A\222/\317XO"
  }
}
library {
  digests {
    sha256: "6M\316\372\302\272\277t\021\271}\001\327\224\037|7Fl\tK\375\204\3753\265v\301\031\357\207\334"
  }
}
library {
  digests {
    sha256: "\234\370\222!^Ts_\303\016\272B\350zG\253\017\357\367\212\"Ys\003\023\002\261\226\036\211\253\317"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 28
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 43
  library_dep_index: 20
  library_dep_index: 14
  library_dep_index: 28
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 17
  library_dep_index: 20
  library_dep_index: 27
  library_dep_index: 14
}
library_dependencies {
  library_index: 3
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 2
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 12
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 2
}
library_dependencies {
  library_index: 8
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 9
  library_dep_index: 2
}
library_dependencies {
  library_index: 10
  library_dep_index: 2
  library_dep_index: 9
}
library_dependencies {
  library_index: 11
  library_dep_index: 2
}
library_dependencies {
  library_index: 12
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
  library_dep_index: 14
  library_dep_index: 17
}
library_dependencies {
  library_index: 14
  library_dep_index: 15
  library_dep_index: 16
}
library_dependencies {
  library_index: 17
  library_dep_index: 2
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 13
  library_dep_index: 20
  library_dep_index: 14
  library_dep_index: 21
}
library_dependencies {
  library_index: 18
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 14
}
library_dependencies {
  library_index: 19
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 20
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 15
}
library_dependencies {
  library_index: 24
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 25
  library_dep_index: 14
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 14
}
library_dependencies {
  library_index: 27
  library_dep_index: 2
}
library_dependencies {
  library_index: 28
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 0
}
library_dependencies {
  library_index: 29
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 30
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 3
}
library_dependencies {
  library_index: 31
  library_dep_index: 2
}
library_dependencies {
  library_index: 32
  library_dep_index: 2
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 36
  library_dep_index: 8
  library_dep_index: 37
  library_dep_index: 2
}
library_dependencies {
  library_index: 37
  library_dep_index: 2
  library_dep_index: 27
}
library_dependencies {
  library_index: 38
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 35
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 1
  library_dep_index: 19
  library_dep_index: 13
  library_dep_index: 17
  library_dep_index: 20
  library_dep_index: 5
}
library_dependencies {
  library_index: 40
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 34
}
library_dependencies {
  library_index: 41
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 42
  library_dep_index: 13
}
library_dependencies {
  library_index: 42
  library_dep_index: 10
  library_dep_index: 19
  library_dep_index: 9
}
library_dependencies {
  library_index: 43
  library_dep_index: 2
}
library_dependencies {
  library_index: 44
  library_dep_index: 45
  library_dep_index: 2
  library_dep_index: 0
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 4
  library_dep_index: 33
  library_dep_index: 50
  library_dep_index: 5
  library_dep_index: 39
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 29
  library_dep_index: 57
}
library_dependencies {
  library_index: 46
  library_dep_index: 2
}
library_dependencies {
  library_index: 47
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 34
  library_dep_index: 3
}
library_dependencies {
  library_index: 48
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 49
}
library_dependencies {
  library_index: 50
  library_dep_index: 4
  library_dep_index: 3
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 52
  library_dep_index: 41
  library_dep_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 52
  library_dep_index: 2
}
library_dependencies {
  library_index: 53
  library_dep_index: 2
}
library_dependencies {
  library_index: 54
  library_dep_index: 2
}
library_dependencies {
  library_index: 55
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 34
  library_dep_index: 3
}
library_dependencies {
  library_index: 56
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 57
  library_dep_index: 2
  library_dep_index: 39
  library_dep_index: 55
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
  library_dep_index: 2
  library_dep_index: 5
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 61
}
library_dependencies {
  library_index: 59
  library_dep_index: 2
  library_dep_index: 5
  library_dep_index: 42
  library_dep_index: 7
  library_dep_index: 60
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 61
}
library_dependencies {
  library_index: 60
  library_dep_index: 2
}
library_dependencies {
  library_index: 62
  library_dep_index: 11
  library_dep_index: 7
  library_dep_index: 59
  library_dep_index: 4
  library_dep_index: 61
}
library_dependencies {
  library_index: 63
  library_dep_index: 11
  library_dep_index: 2
  library_dep_index: 59
  library_dep_index: 62
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 61
  library_dep_index: 0
}
library_dependencies {
  library_index: 65
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 31
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 44
  dependency_index: 48
  dependency_index: 58
  dependency_index: 62
  dependency_index: 63
  dependency_index: 59
  dependency_index: 64
  dependency_index: 65
  dependency_index: 60
  dependency_index: 66
  dependency_index: 67
  dependency_index: 68
  dependency_index: 69
  dependency_index: 70
  dependency_index: 71
  dependency_index: 72
  dependency_index: 73
  dependency_index: 74
  dependency_index: 75
  dependency_index: 76
  dependency_index: 77
  dependency_index: 78
  dependency_index: 79
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com/"
  }
}
