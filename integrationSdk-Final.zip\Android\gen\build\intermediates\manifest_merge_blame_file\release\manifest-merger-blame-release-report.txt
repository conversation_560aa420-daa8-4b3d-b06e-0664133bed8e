1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.amlacameroon.sandbox"
4    android:installLocation="auto"
5    android:versionCode="354"
6    android:versionName="0.0.354.0" >
7
8    <uses-sdk
9        android:minSdkVersion="24"
9-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
10        android:targetSdkVersion="34" />
10-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml
11
12    <supports-screens
12-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3549-3708
13        android:anyDensity="true"
13-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3681-3706
14        android:largeScreens="true"
14-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3624-3651
15        android:normalScreens="true"
15-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3595-3623
16        android:smallScreens="true"
16-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3567-3594
17        android:xlargeScreens="true" />
17-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3652-3680
18
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3708-3783
19-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3725-3781
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3783-3842
20-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3800-3840
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3842-3907
21-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3859-3905
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3907-3968
22-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3924-3966
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3968-4042
23-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3985-4040
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4042-4111
24-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4059-4109
25    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
25-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4111-4181
25-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4128-4179
26    <uses-permission android:name="android.permission.VIBRATE" />
26-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4181-4241
26-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4198-4239
27
28    <uses-feature
28-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4241-4329
29        android:name="android.hardware.camera.autofocus"
29-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4255-4303
30        android:required="true" />
30-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4304-4327
31    <uses-feature
31-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4329-4407
32        android:name="android.hardware.camera"
32-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4343-4381
33        android:required="true" />
33-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4382-4405
34    <uses-feature
34-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4407-4489
35        android:name="android.hardware.microphone"
35-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4421-4463
36        android:required="true" />
36-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:4464-4487
37
38    <queries xmlns:android="http://schemas.android.com/apk/res/android" >
38-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:13:5-24:15
38-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:13:14-72
39        <intent>
39-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:14:9-18:18
40            <action android:name="android.intent.action.GET_CONTENT" />
40-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:15:13-72
40-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:15:21-69
41
42            <data android:mimeType="image/*" />
42-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:17:13-48
42-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:17:19-45
43        </intent>
44        <intent>
44-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:19:9-23:18
45            <action android:name="android.intent.action.GET_CONTENT" />
45-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:15:13-72
45-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:15:21-69
46
47            <data android:mimeType="video/*" />
47-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:17:13-48
47-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:17:19-45
48        </intent>
49    </queries>
50
51    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
51-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:35:5-79
51-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:35:22-76
52
53    <application
53-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:241-3549
54        android:name="fr.pcsoft.wdjava.core.application.WDAndroidApp"
54-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:254-315
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
56        android:extractNativeLibs="true"
56-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:533-565
57        android:icon="@drawable/i_c_o_n_e________0"
57-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:349-392
58        android:label="@string/app_name"
58-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:316-348
59        android:largeHeap="true"
59-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:393-417
60        android:requestLegacyExternalStorage="true"
60-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:489-532
61        android:resizeableActivity="true"
61-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:419-452
62        android:usesCleartextTraffic="true" >
62-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:453-488
63        <activity
63-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:566-1008
64            android:name="com.amlacameroon.sandbox.wdgen.GWDPintegrationSdk$WDLanceur"
64-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:576-626
65            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout"
65-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:729-831
66            android:exported="true"
66-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:832-855
67            android:label="@string/app_name"
67-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:627-659
68            android:theme="@style/Theme.AppCompat.Light.NoActionBar.Translucent" >
68-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:660-728
69            <intent-filter>
69-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:856-997
70                <action android:name="android.intent.action.MAIN" />
70-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:871-922
70-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:879-920
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:922-981
72-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:932-979
73            </intent-filter>
74        </activity>
75        <activity
75-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1008-1307
76            android:name="com.amlacameroon.sandbox.wdgen.GWDFFEN_Mobile_Camera_UI$WDActiviteFenetre"
76-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1018-1082
77            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
77-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1083-1192
78            android:hardwareAccelerated="false"
78-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1270-1305
79            android:screenOrientation="unspecified"
79-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1230-1269
80            android:theme="@android:style/Theme" />
80-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1193-1229
81        <activity
81-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1307-1589
82            android:name="com.amlacameroon.sandbox.wdgen.GWDFrun$WDActiviteFenetre"
82-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1317-1364
83            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
83-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1365-1474
84            android:hardwareAccelerated="false"
84-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1552-1587
85            android:screenOrientation="unspecified"
85-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1512-1551
86            android:theme="@android:style/Theme" />
86-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1475-1511
87        <activity
87-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1589-1870
88            android:name="com.amlacameroon.sandbox.wdgen.GWDFtt$WDActiviteFenetre"
88-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1599-1645
89            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
89-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1646-1755
90            android:hardwareAccelerated="false"
90-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1833-1868
91            android:screenOrientation="unspecified"
91-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1793-1832
92            android:theme="@android:style/Theme" />
92-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1756-1792
93
94        <uses-library
94-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1870-1948
95            android:name="org.apache.http.legacy"
95-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1884-1921
96            android:required="false" />
96-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1922-1946
97
98        <activity
98-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1948-2110
99            android:name="fr.pcsoft.wdjava.core.erreur.report.WDErrorReportActivity"
99-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:1958-2030
100            android:theme="@android:style/Theme.DeviceDefault.Light" />
100-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2031-2087
101
102        <provider
102-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2110-2424
103            android:name="androidx.core.content.FileProvider"
103-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2120-2169
104            android:authorities="com.amlacameroon.sandbox.fileprovider"
104-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2170-2229
105            android:exported="false"
105-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2230-2254
106            android:grantUriPermissions="true" >
106-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2255-2289
107            <meta-data
107-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2311-2413
108                android:name="android.support.FILE_PROVIDER_PATHS"
108-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2322-2372
109                android:resource="@xml/provider_paths" />
109-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2373-2411
110        </provider>
111
112        <receiver
112-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2424-2563
113            android:name="fr.pcsoft.wdjava.core.utils.WDAppUtils$APKInstallBroadcastReceiver"
113-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2434-2515
114            android:exported="false" />
114-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2516-2540
115
116        <activity
116-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2563-2733
117            android:name="fr.pcsoft.wdjava.ui.activite.WDActivite$BlankActivity"
117-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2573-2641
118            android:theme="@style/Theme.AppCompat.Light.NoActionBar.Translucent" />
118-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2642-2710
119
120        <provider
120-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2733-2898
121            android:name="fr.pcsoft.wdjava.ui.searchbar.WDSearchHistory"
121-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2743-2803
122            android:authorities="com.amlacameroon.sandbox"
122-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2829-2875
123            android:exported="false" />
123-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2804-2828
124
125        <activity
125-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2898-3229
126            android:name="fr.pcsoft.wdjava.ui.searchbar.WDSearchActivity"
126-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2908-2969
127            android:exported="false"
127-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3024-3048
128            android:label="@string/app_name" >
128-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:2970-3002
129            <intent-filter>
129-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3049-3133
130                <action android:name="android.intent.action.SEARCH" />
130-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3064-3117
130-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3072-3115
131            </intent-filter>
132
133            <meta-data
133-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3133-3218
134                android:name="android.app.searchable"
134-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3144-3181
135                android:resource="@xml/searchable" />
135-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3182-3216
136        </activity>
137
138        <service android:name="fr.pcsoft.wdjava.core.service.WDServiceLocal" />
138-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3229-3299
138-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3238-3297
139
140        <meta-data
140-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3299-3374
141            android:name="fr.pcsoft.first_window_name"
141-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3310-3352
142            android:value="run" />
142-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3353-3372
143        <meta-data
143-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3374-3454
144            android:name="fr.pcsoft.splash_orientation_phone"
144-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3385-3434
145            android:value="4" />
145-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3435-3452
146        <meta-data
146-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3454-3535
147            android:name="fr.pcsoft.splash_orientation_tablet"
147-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3465-3515
148            android:value="4" />
148-->C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\AndroidManifest.xml:2:3516-3533
149
150        <activity
150-->[AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:9:9-13:74
151            android:name="ai.tech5.pheonix.capture.activity.CaptureImageActivity"
151-->[AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:10:13-82
152            android:hardwareAccelerated="true"
152-->[AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:11:13-47
153            android:screenOrientation="portrait"
153-->[AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:12:13-49
154            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" />
154-->[AirsnapFaceUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\e68fe6ab29c3f5985c4a495b897f5872\transformed\jetified-AirsnapFaceUI-release\AndroidManifest.xml:13:13-71
155        <activity
155-->[AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:8:9-12:74
156            android:name="ai.tech5.finger.FingerCaptureActivity"
156-->[AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:9:13-65
157            android:hardwareAccelerated="true"
157-->[AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:10:13-47
158            android:screenOrientation="portrait"
158-->[AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:11:13-49
159            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" />
159-->[AirsnapFingerUI-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\743c184c17ca1e34570ee043062be700\transformed\jetified-AirsnapFingerUI-release\AndroidManifest.xml:12:13-71
160        <activity
160-->[CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:8:9-12:74
161            android:name="ai.tech5.cryptograph.reader.api.ScanCryptographActivity"
161-->[CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:9:13-83
162            android:exported="true"
162-->[CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:10:13-36
163            android:screenOrientation="portrait"
163-->[CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:11:13-49
164            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" />
164-->[CryptographReader-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01d099e79dd7759b1f46c50031693b1e\transformed\jetified-CryptographReader-release\AndroidManifest.xml:12:13-71
165        <activity
165-->[t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:8:9-10:40
166            android:name="ai.tech5.t5ncnn.MainActivity"
166-->[t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:9:13-56
167            android:exported="false" />
167-->[t5ncnn-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a18bb5aafbfa85c04566835539efaf6c\transformed\jetified-t5ncnn-release\AndroidManifest.xml:10:13-37
168        <activity
168-->[t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:8:9-10:40
169            android:name="ai.tech5.t5opencv.MainActivity"
169-->[t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:9:13-58
170            android:exported="false" />
170-->[t5opencv-release.aar] C:\Users\<USER>\.gradle\caches\transforms-3\91888d9275e823c5f1cab6d617430648\transformed\jetified-t5opencv-release\AndroidManifest.xml:10:13-37
171        <activity
171-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:40:9-44:52
172            android:name="fr.pcsoft.wdandroid_wdl.wdgen.GWDFFEN_LICENCE_WX$WDActiviteFenetre"
172-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:41:13-94
173            android:configChanges="keyboardHidden|orientation|screenSize|keyboard|smallestScreenSize|screenLayout|uiMode"
173-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:42:13-122
174            android:hardwareAccelerated="false"
174-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:43:13-48
175            android:theme="@android:style/Theme" />
175-->[wd300android_wdl.aar] C:\Users\<USER>\.gradle\caches\transforms-3\fc564eba2532304e325808113d7aa917\transformed\jetified-wd300android_wdl\AndroidManifest.xml:44:13-49
176
177        <provider
177-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
178            android:name="androidx.startup.InitializationProvider"
178-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
179            android:authorities="com.amlacameroon.sandbox.androidx-startup"
179-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
180            android:exported="false" >
180-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
181            <meta-data
181-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
182                android:name="androidx.emoji2.text.EmojiCompatInitializer"
182-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
183                android:value="androidx.startup" />
183-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8c9f96e058eeab992770628d0528359\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
184            <meta-data
184-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
185                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
185-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
186                android:value="androidx.startup" />
186-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\27047c5d0bb513344ae4ceefe258867b\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
187        </provider>
188    </application>
189
190</manifest>
