C:\Users\<USER>\Documents\integrationSdk-Copy\Android\gen\prg.dat:35: Error: Obsolete ProGuard file; use -keepclasseswithmembers instead of -keepclasseswithmembernames [Proguard]
-keepclasseswithmembernames class * {
^

   Explanation for issues of type "Proguard":
   Using -keepclasseswithmembernames in a proguard config file is not correct;
   it can cause some symbols to be renamed which should not be.

   Earlier versions of ADT used to create proguard.cfg files with the wrong
   format. Instead of -keepclasseswithmembernames use -keepclasseswithmembers,
   since the old flags also implies "allow shrinking" which means symbols only
   referred to from XML and not Java (such as possibly CustomViews) can get
   deleted.

   https://issuetracker.google.com/36928077

1 errors, 0 warnings
