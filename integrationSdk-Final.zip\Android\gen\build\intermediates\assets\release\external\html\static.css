/* Feuille CSS statique, commune entre l'édition et l'exécution */

/* ****************************************************** */
/* Classes appliquées par la zone de texte
/* ****************************************************** */

/* mise en majusucule du texte */
.wb-maj{text-transform:uppercase;}

/* mise en minuscule du texte */
.wb-min{text-transform:lowercase;}

/* les balises mark dans la ZTR n'apportent pas de couleur de fond, afin d'accepter la cascade depuis un span parent */
.webdevclass-riche mark{background:transparent;color:inherit;}

/* ****************************************************** */
/* Classes appliquées par les colonnes de table
/* ****************************************************** */

/* positionne la loupe et le menu de filtre */
.wb-loupeFiltre
{
position:absolute;
bottom:1px;
right:1px;
}
.wb-loupeFiltre img
{
display:block;
}



/* ****************************************************** */
/* Classes appliquées sur tous les champs
/* ****************************************************** */

/* débordement */
.dzSpan
{
  display:block;
  position: relative;
}
/* débordement dans un texte riche, doit être dans le flux inline */
.dzSpanRiche
{
  display:inline-block;
  position: relative;
}


/* ****************************************************** */
/* jQueryUI
/* ****************************************************** */

/*! jQuery UI - v1.12.1 - 2017-03-07
* http://jqueryui.com
* Includes: draggable.css, core.css, resizable.css, autocomplete.css, menu.css, tooltip.css, theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?scope=&folderName=base&cornerRadiusShadow=8px&offsetLeftShadow=0px&offsetTopShadow=0px&thicknessShadow=5px&opacityShadow=30&bgImgOpacityShadow=0&bgTextureShadow=flat&bgColorShadow=666666&opacityOverlay=30&bgImgOpacityOverlay=0&bgTextureOverlay=flat&bgColorOverlay=aaaaaa&iconColorError=cc0000&fcError=5f3f3f&borderColorError=f1a899&bgTextureError=flat&bgColorError=fddfdf&iconColorHighlight=777620&fcHighlight=777620&borderColorHighlight=dad55e&bgTextureHighlight=flat&bgColorHighlight=fffa90&iconColorActive=ffffff&fcActive=ffffff&borderColorActive=003eff&bgTextureActive=flat&bgColorActive=007fff&iconColorHover=555555&fcHover=2b2b2b&borderColorHover=cccccc&bgTextureHover=flat&bgColorHover=ededed&iconColorDefault=777777&fcDefault=454545&borderColorDefault=c5c5c5&bgTextureDefault=flat&bgColorDefault=f6f6f6&iconColorContent=444444&fcContent=333333&borderColorContent=dddddd&bgTextureContent=flat&bgColorContent=ffffff&iconColorHeader=444444&fcHeader=333333&borderColorHeader=dddddd&bgTextureHeader=flat&bgColorHeader=e9e9e9&cornerRadius=3px&fwDefault=normal&fsDefault=1em&ffDefault=Arial%2CHelvetica%2Csans-serif
* Copyright jQuery Foundation and other contributors; Licensed MIT */

.ui-draggable-handle {
	-ms-touch-action: none;
	touch-action: none;
}
/* Layout helpers
----------------------------------*/
.ui-helper-hidden {
	display: none;
}
.ui-helper-hidden-accessible {
	border: 0;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
}
.ui-helper-reset {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	line-height: 1.3;
	text-decoration: none;
	font-size: 100%;
	list-style: none;
}
.ui-helper-clearfix:before,
.ui-helper-clearfix:after {
	content: "";
	display: table;
	border-collapse: collapse;
}
.ui-helper-clearfix:after {
	clear: both;
}
.ui-helper-zfix {
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	position: absolute;
	opacity: 0;
	filter:Alpha(Opacity=0); /* support: IE8 */
}

.ui-front {
	z-index: 100;
}


/* Interaction Cues
----------------------------------*/
.ui-state-disabled {
	cursor: default !important;
	pointer-events: none;
}


/* Icons
----------------------------------*/
.ui-icon {
	display: inline-block;
	vertical-align: middle;
	margin-top: -.25em;
	position: relative;
	text-indent: -99999px;
	overflow: hidden;
	background-repeat: no-repeat;
}

.ui-widget-icon-block {
	left: 50%;
	margin-left: -8px;
	display: block;
}

/* Misc visuals
----------------------------------*/

/* Overlays */
.ui-widget-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.ui-resizable {
	position: relative;
}
.ui-resizable-handle {
	position: absolute;
	font-size: 0.1px;
	display: block;
	-ms-touch-action: none;
	touch-action: none;
}
.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
	display: none;
}
.ui-resizable-n {
	cursor: n-resize;
	height: 7px;
	width: 100%;
	top: -5px;
	left: 0;
}
.ui-resizable-s {
	cursor: s-resize;
	height: 7px;
	width: 100%;
	bottom: -5px;
	left: 0;
}
.ui-resizable-e {
	cursor: e-resize;
	width: 7px;
	right: -5px;
	top: 0;
	height: 100%;
}
.ui-resizable-w {
	cursor: w-resize;
	width: 7px;
	left: -5px;
	top: 0;
	height: 100%;
}
.ui-resizable-se {
	cursor: se-resize;
	width: 12px;
	height: 12px;
	right: 1px;
	bottom: 1px;
}
.ui-resizable-sw {
	cursor: sw-resize;
	width: 9px;
	height: 9px;
	left: -5px;
	bottom: -5px;
}
.ui-resizable-nw {
	cursor: nw-resize;
	width: 9px;
	height: 9px;
	left: -5px;
	top: -5px;
}
.ui-resizable-ne {
	cursor: ne-resize;
	width: 9px;
	height: 9px;
	right: -5px;
	top: -5px;
}
.ui-autocomplete {
	position: absolute;
	top: 0;
	left: 0;
	cursor: default;
}
.ui-menu {
	list-style: none;
	padding: 0;
	margin: 0;
	display: block;
	outline: 0;
}
.ui-menu .ui-menu {
	position: absolute;
}
.ui-menu .ui-menu-item {
	margin: 0;
	cursor: pointer;
	/* support: IE10, see #8844 */
	list-style-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
}
.ui-menu .ui-menu-item-wrapper {
	position: relative;
	padding: 3px 1em 3px .4em;
	display: block;
}
.ui-menu .ui-menu-divider {
	margin: 5px 0;
	height: 0;
	font-size: 0;
	line-height: 0;
	border-width: 1px 0 0 0;
}
.ui-menu .ui-state-focus,
.ui-menu .ui-state-active {
	margin: -1px;
}

/* icon support */
.ui-menu-icons {
	position: relative;
}
.ui-menu-icons .ui-menu-item-wrapper {
	padding-left: 2em;
}

/* left-aligned */
.ui-menu .ui-icon {
	position: absolute;
	top: 0;
	bottom: 0;
	left: .2em;
	margin: auto 0;
}

/* right-aligned */
.ui-menu .ui-menu-icon {
	left: auto;
	right: 0;
}


/* Component containers
----------------------------------*/
.ui-widget {
	font-family: inherit;/* Arial,Helvetica,sans-serif;*/
	font-size: 1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Arial,Helvetica,sans-serif;
	font-size: 1em;
}
.ui-widget.ui-widget-content:not(.wbTooltip) {
	border: 1px solid #c5c5c5;
}
.ui-widget-content {
	border: 1px solid #dddddd;
	background: #ffffff;
	color: inherit;
}
.ui-widget-content a {
	color: inherit;
}
.ui-widget-header {
	border: 1px solid #dddddd;
	background: #e9e9e9;
	color: #333333;
	font-weight: bold;
}
.ui-widget-header a {
	color: #333333;
}

/* Interaction states
----------------------------------*/
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,

/* We use html here because we need a greater specificity to make sure disabled
works properly when clicked or hovered */
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
	border: 1px solid #c5c5c5;
	background: #f6f6f6;
	font-weight: normal;
	color: #454545;
}
.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
	color: #454545;
	text-decoration: none;
}
.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
	border: 1px solid #cccccc;
	background: #ededed;
	font-weight: normal;
	color: #2b2b2b;
}
.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited,
a.ui-button:hover,
a.ui-button:focus {
	color: #2b2b2b;
	text-decoration: none;
}

.ui-visual-focus {
	box-shadow: 0 0 3px 1px rgb(94, 158, 214);
}
.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
	border: 1px solid #003eff;
	background: #007fff;
	font-weight: normal;
	color: #ffffff;
}
.ui-icon-background,
.ui-state-active .ui-icon-background {
	border: #003eff;
	background-color: #ffffff;
}
.ui-state-active a,
.ui-state-active a:link,
.ui-state-active a:visited {
	color: #ffffff;
	text-decoration: none;
}

/* Interaction Cues
----------------------------------*/
.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
	border: 1px solid #dad55e;
	background: #fffa90;
	color: #777620;
}
.ui-state-checked {
	border: 1px solid #dad55e;
	background: #fffa90;
}
.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
	color: #777620;
}
.ui-state-error,
.ui-widget-content .ui-state-error,
.ui-widget-header .ui-state-error {
	border: 1px solid #f1a899;
	background: #fddfdf;
	color: #5f3f3f;
}
.ui-state-error a,
.ui-widget-content .ui-state-error a,
.ui-widget-header .ui-state-error a {
	color: #5f3f3f;
}
.ui-state-error-text,
.ui-widget-content .ui-state-error-text,
.ui-widget-header .ui-state-error-text {
	color: #5f3f3f;
}
.ui-priority-primary,
.ui-widget-content .ui-priority-primary,
.ui-widget-header .ui-priority-primary {
	font-weight: bold;
}
.ui-priority-secondary,
.ui-widget-content .ui-priority-secondary,
.ui-widget-header .ui-priority-secondary {
	opacity: .7;
	filter:Alpha(Opacity=70); /* support: IE8 */
	font-weight: normal;
}
.ui-state-disabled,
.ui-widget-content .ui-state-disabled,
.ui-widget-header .ui-state-disabled {
	opacity: .35;
	filter:Alpha(Opacity=35); /* support: IE8 */
	background-image: none;
}
.ui-state-disabled .ui-icon {
	filter:Alpha(Opacity=35); /* support: IE8 - See #6059 */
}


/* Misc visuals
----------------------------------*/

/* Corner radius */
.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
	border-top-left-radius: 3px;
}
.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
	border-top-right-radius: 3px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
	border-bottom-left-radius: 3px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
	border-bottom-right-radius: 3px;
}

/* Overlays */
.ui-widget-overlay {
	background: #aaaaaa;
	opacity: .3;
	filter: Alpha(Opacity=30); /* support: IE8 */
}
.ui-widget-shadow {
	-webkit-box-shadow: 0px 0px 5px #666666;
	box-shadow: 0px 0px 5px #666666;
}

.ui-tooltip {
	position: absolute;
	z-index: 9999;
	max-width: 300px;
}
.ui-tooltip {
	padding: 8px;
	box-shadow: 0 0 5px #aaa;
}
body .ui-tooltip:not(.wbTooltip) {
	border-width: 2px;
}
.ui-slider {
	position: relative;
	text-align: left;
}
.ui-slider .ui-slider-handle {
	position: absolute;
	z-index: 2;
	width: 1.2em;
	height: 1.2em;
	cursor: default;
	-ms-touch-action: none;
	touch-action: none;
}
.ui-slider .ui-slider-range {
	position: absolute;
	z-index: 1;
	font-size: .7em;
	display: block;
	border: 0;
	background-position: 0 0;
}

/* support: IE8 - See #6727 */
.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
	filter: inherit;
}

.ui-slider-horizontal {
	height: .8em;
}
.ui-slider-horizontal .ui-slider-handle {
	top: -.3em;
	margin-left: -.6em;
}
.ui-slider-horizontal .ui-slider-range {
	top: 0;
	height: 100%;
}
.ui-slider-horizontal .ui-slider-range-min {
	left: 0;
}
.ui-slider-horizontal .ui-slider-range-max {
	right: 0;
}

.ui-slider-vertical {
	width: .8em;
	height: 100px;
}
.ui-slider-vertical .ui-slider-handle {
	left: -.3em;
	margin-left: 0;
	margin-bottom: -.6em;
}
.ui-slider-vertical .ui-slider-range {
	left: 0;
	width: 100%;
}
.ui-slider-vertical .ui-slider-range-min {
	bottom: 0;
}
.ui-slider-vertical .ui-slider-range-max {
	top: 0;
}

/* ****************************************************** */
/* Saisie assistée
/* ****************************************************** */

.ui-autocomplete-loading {
  background: white url('ui-anim_basic_16x16.gif') right center no-repeat;
}

.ui-autocomplete {
  max-height: 300px;
  overflow-y: auto;
  /* prevent horizontal scrollbar */
  overflow-x: hidden;
  transition: none !important;
}

/* IE 6 doesn't support max-height
 * we use height instead, but this forces the menu to always be this tall
 */
* html .ui-autocomplete {
height: 300px;
}

/* coloration de la partie trouvée dans la saisie assistée */  
.ui-autocomplete-term  {
    color: inherit;
}

/* surcharge le fond défini par le thème jQuery UI smoothness */
.ui-widget-content {
background-image:none;
}
/* surcharge la liste du thème jQuery UI smoothness */
.ui-widget-content:not(.wbTooltip) {
    border: 1px solid #ccc;
}
.ui-widget-content {
    border-radius: 0;
}
/* surcharge l'élément survolé dans la liste du thème jQuery UI smoothness */
.ui-state-focus {
    border-radius: 0;
}
/* surcharge la taille de font dans la  liste du thème jQuery UI smoothness */
.ui-widget {
    font-size: 9pt;
}

/* la bulle perso de webdev est personnalisée via un style css à part */
/* la bulle est un ui-widget, donc attention aux priorités / conflits */
.wbTooltip
{
	background: transparent;
	border:none;
	border-radius: 0;
	box-shadow: none;
	color:initial;
	font-size: 0.75rem;
	padding:0;
	pointer-events: none;

	/*les transitions viendront du script, pas du CSS*/
	transition:none !important;

	/*évite de sortir de la bulle */
	overflow: hidden;
}
/* ****************************************************** */
/* Saisie assistée look google
/* ****************************************************** */

.ui-menu {
	padding: 2px;
}

/* liste */
.ui-autocomplete
{
    
    /*border:10px solid blue;*/
	
	/* comme google */
	border:1px solid #ccc;	
	box-shadow: 0 2px 4px rgba(0,0,0,0.2);	
	cursor:default;
	
	/* perso */
	border-top:0;
	font-family: inherit;/* Arial, Helvetica, sans-serif;*/
	/* QW#237905 pour être au dessus du GFI, des popups etc... */
	z-index:999;
}


/* élément */
.ui-menu .ui-menu-item a,.ui-menu .ui-menu-item a.ui-state-focus
{    
    /*border:10px solid green;*/
	
	/*font-weight:bold;*/
	
	
}

/* élément sélectionné */
.ui-menu .ui-menu-item a.ui-state-focus, .ui-autocomplete .ui-state-active
{
    
    /*border:10px solid red;   */
    /*pas possible d'utiliser les classes de l'ambiance pour l'état de survol de l'auto complete*/
	background:none #eee;
	color:#333;
	border:0;
	
	
    margin:0;
}

/* partie trouvée */
  .ui-autocomplete-term {
    color: inherit;
	font-weight:bold;
}

.ui-autocomplete.ui-widget-content .ui-state-active{
	color: inherit;
	text-decoration: none;
}

/* ****************************************************** */
/* Champ Table 
/* ****************************************************** */

 
/* évite la double bordure sur la dernière colonne */
.wbDerniereColonneVisible
{
border-right-width:0 !important;
}
/* les largeurs de colonne sont données via des classes */
.wbtablesep
{
width:1px;
min-width:1px;
border-left: none !important;
border-right: none !important;
}

/* texte indicatif */
*::-webkit-input-placeholder {/* webkit, chrome... */
 font-style:italic;  color:#888888;
}
*:-moz-placeholder {/* Firefox */
 font-style:italic;  color: #888888;
}
*::-moz-placeholder {/* Firefox > 19 */
 font-style:italic;  color: #888888;
}
*:-ms-input-placeholder[value=""]{ /* IE */
 font-style:italic !important;  color:#888888 !important;
}
textarea:-ms-input-placeholder{ /* IE */
 font-style:italic;  color:#888888 ;
}

/* ****************************************************** */
/* Champ Tableau de bord
/* ****************************************************** */

.wbTdbME 		{position:absolute;left:0;top:0;right:0;bottom:0;}
.wbTdbHG		{position:absolute;left:0;top:0;width:4px;height:4px;}
.wbTdbH			{position:absolute;left:4px;right:4px;top:0;height:4px;}
.wbTdbHD		{position:absolute;right:0;top:0;width:4px;height:4px;}
.wbTdbD			{position:absolute;right:0;top:4px;bottom:4px;width:4px;}
.wbTdbBD		{position:absolute;right:0;bottom:0;width:4px;height:4px;}
.wbTdbB			{position:absolute;left:4px;right:4px;bottom:0;height:4px;}
.wbTdbBG		{position:absolute;left:0;bottom:0;width:4px;height:4px;}
.wbTdbG			{position:absolute;left:0;top:4px;bottom:4px;width:4px;}

/* avec curseur de redimensionnement */
.wbTdbRedim  .wbTdbHG	{cursor:SE-resize;}
.wbTdbRedim  .wbTdbH		{cursor:N-resize;}
.wbTdbRedim  .wbTdbHD	{cursor:NE-resize;}
.wbTdbRedim  .wbTdbD		{cursor:E-resize;}
.wbTdbRedim  .wbTdbBD	{cursor:SE-resize;}
.wbTdbRedim  .wbTdbB		{cursor:N-resize;}
.wbTdbRedim  .wbTdbBG	{cursor:NE-resize;}
.wbTdbRedim  .wbTdbG		{cursor:E-resize;}

/* ****************************************************** */
/* Border box
/* ****************************************************** */
.wbBorderBox { -webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box; }



/* ****************************************************** */
/* Champs composites RWD
/* ****************************************************** */
.wbLibChamp 
{
    display:table-row;
    list-style:none;
    margin:0;
    padding:0;
	vertical-align:middle;
	position: relative;
}
.wbLibChamp > li
{    
    display:table-cell;
    vertical-align: middle;
}
.wbLibChamp > li:first-child
{    
/* le libellé passe au dessus */
   z-index: 1;
}

/*
ul.wbLibHaut
{
    display:table;
}

ul.wbLibHaut>li
{
    display:table-row;
}
*/


/* ********************************************************** 
 * Barre d'outils 
 ************************************************************ */

.WDBarreOutils {
    height: 24px;
    display: none;
    position: absolute;
    top: -23px;
    bottom: 0px;    
}

.WDBarreOutilsPopup {
    display: none;
    position: absolute;
    left: 0px;
    top: 22px;
    margin: 0px;
    padding: 0px;
}

.WDBarreOutilsPopup img {
    vertical-align: middle;
}

.WDBarreOutilsPopup span {    
    font-family: Tahoma, Arial, Helvetica, sans-serif;/*et est placé à la génération*/
    font-size: 0.8rem;    
}

.wbBarreOutilsUl .WDBarreOutilsPopup span {    
    cursor: default;
    /*padding remis à 0 par la html pour la saisie riche*/
    padding-left: 2px;
    padding-right: 4px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    /*max-width: 132px; par la html pour la saisie riche*/
    display: block;    
}

.WDBarreOutils .WDBordGauche, .WDBarreOutils .WDBordDroit
{
    border-collapse: collapse;
    empty-cells: show;
    border-spacing: 0;
}
.WDBarreOutils .WDBordGauche
{
    border-left: solid 1px #A0A0A0;
}
.WDBarreOutils .WDBordDroit
{
    border-right: solid 1px #A0A0A0;
}

.WDBarreOutils tr {
    border-right: solid 1px #A0A0A0;
    border-collapse: collapse;
    empty-cells: show;
    border-spacing: 0;
    background-color: #F5F5F5;
}

.WDBarreOutils td {
    border-top: solid 1px #A0A0A0;
    border-bottom: solid 1px #A0A0A0;
    border-collapse: collapse;
    empty-cells: show;
    border-spacing: 0;
}

.WDBarreOutils .WDBarreOutilsPopup {
    border-style: solid;
    border-width: 1px;
    border-color: #A0A0A0;
    border-collapse: collapse;
    empty-cells: show;
    border-spacing: 0;
}

.WDBarreOutils td td, .WDBarreOutils tr tr {
    color: #344963;
    background-color: #F5F5F5;
    border: none;
    background-image: none;
    padding-right: 3px;/*blanc à droite du texte*/
}

.wbColorPickerChoice
{
	border:1px solid white;	
    width:16px;
    width: 1rem;
    height:16px;
    height: 1rem;
    cursor: pointer;
    /* pour une lisibilité sur les blancs*/
    border-right-color: rgba(0, 0, 0, 0.1);
    border-bottom-color: rgba(0, 0, 0, 0.1);
}
.wbColorPickerChoice:hover
{
	outline:1px solid black;
	/*transform: scale(1.12); avis?*/
}

/*SUGG remplacer par le picto de la planche (source https://codepen.io/teeganlincoln/pen/mjjzeE) */
.wbColorPickerChoiceChecked
{
	outline:1px solid black;
}
.wbColorPickerChoiceChecked:after {
    content: '';
    display: block;         
    width: 3px;
    height: 6px;         
    border: solid #000;
    border-width: 0 2px 2px 0; 
    transform: translateX(6px) translateY(-5px) rotate(45deg);
}

.wbColorPickerChoiceChecked:before {
    content: '';
    display: block;
    width: 4px;
    height: 7px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: translateX(5px) translateY(2px) rotate(45deg);
}

.wbBarreOutilsUl, .wbBarreOutilsLi, .wbBarreOutilsComboListe, .wbBarreOutilsComboListeOption {
    display: block;
    margin: 0;
    padding: 0;
    list-style: none;
    font: 400 13px Arial;
    font: 400 0.8125rem Arial;        
}

.wbBarreOutilsLi {
    display: inline-block;
    vertical-align: middle;
    border:none !important;/*évite les bordure de WDBordDroit WDBordGauche*/    
    margin-bottom: 2px;/*espace entre les lignes de la barre FAA */
    height: 24px; height:1.5rem; /*hauteur de chaque option*/
    box-sizing: border-box;

	/* définit une largeur pour que les fils img vides  prennent cette largeur */
    min-width: 24px;
    min-width: 1.5rem;
    overflow:hidden;
	/* pour que la couleur de fond n'aille pas sur le padding qui ne sert qu'à séparer les éléments =
    background-clip: content-box;*/
}

.wbBarreOutilsImgPlancheWrap
{
    position: relative;
    overflow: hidden;
    display: block;
    height: 100%;	
} 

.wbBarreOutilsImgPlanche
{
	background-image: url('BarreSaisieRiche_WB.png');
    background-size: 500% auto;
    width: 100%;
    height: 5200%;/*//ici planche BarreSaisieRiche.svg */
    position: absolute;
    top: 0;
    left: 0;
    cursor:pointer;
    /*transform: translateY(calc(-100% * 3/ 27)); placé dans style= par la WDxxxHTML	*/
}

.wbBarreOutilsImgPlancheSVG
{
	background-image: url('BarreSaisieRiche_WB.svg');
}
.wbBarreOutilsImgPlancheSVGWDL {
    background-image: url('wdl://BarreSaisieRiche_WB.svg');
}

.wbBarreOutilsUl {
    position: absolute;
    /* overflow: hidden; pourquoi faire? à faire seulement si on peut affichier/masquer ce qui déborde*/
    box-sizing: border-box;
    border-style:solid;
    border-width:1px;
    height: auto;
    bottom:auto;
    top:auto;
    left:-99999999px;
    right:auto;
    padding: 2px;/*blanc tournant de la barre*/
    /*pas de transition sur transform pour que ça coulisse sec*/
    transition:opacity 150ms ease-out, transform 0s;/*/10ms ease-out 10ms;*/
	/* les li ont déjà une marge basse */
	padding-bottom: 0;    

	/*évite que les combos dépassent d'une barre trop étroite*/
	min-width: 132px;/*+4 de padding*/
	min-width: calc(8rem + 4px);/*+4 de padding*/


}

/*raz les styles de WDBarreOutils*/
.wbBarreOutilsUl tr {
    border:none;
    border-collapse: collapse;
    empty-cells: show;
    border-spacing: 0;
    background-color: transparent;
}


.wbBarreOutilsUl td {
    border-top: none;
    border-bottom: none;
    border-collapse: collapse;
    empty-cells: show;
    border-spacing: 0;
}

.wbBarreOutilsUl td td, .wbBarreOutilsUl tr tr {
    color: inherit;
    background-color: transparent;
    border: none;
    background-image: none;
}


.wbBarreOutilComboCheckbox
{
    opacity:0;
    position:fixed;
    left:-99999px;
}

.wbBarreOutilsComboListeOption 
{
    padding: 3px 0;
}

.wbBarreOutilsComboListeOmbre
{
    display:none;  
    border-width:1px;border-style:solid;/*couleur palette*/        
    box-shadow: 0 4px 8px 0 rgba(34,47,62,.1);
    /* border-radius: 2px; */
    top:auto;
    left:auto;
    opacity:0.2;
	transition:opacity 250ms ease;
	transition-delay: 100ms; /*avis? */
}
.wbBarreOutilsLi:hover>.wbBarreOutilsComboListeOmbre
{
	opacity:1;
	transition-delay: 0s;
}


.wbBarreOutilComboLabelReplie:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    position:absolute;
    right: 9px;
    top: 50%;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top-width: 6px; border-top-style: solid;/*couleur via palette*/
    border-bottom: none;
    margin-top: -3px;
}

.wbBarreOutilComboCheckbox:checked+.wbBarreOutilComboLabelReplie+.wbBarreOutilsComboListe
{
    display:block;
    max-height: 10rem;
    overflow: auto;
    overflow-y: auto;
    overflow-x: hidden;
    position:absolute;
    /*position: fixed;astuce pour ne pas être tronqué par l'overflow du ul de la barre complète MAIS proboque un bug de redessin sous Edge au masquage*/
    /*background-color:white; vient de la palette via #A1_COM, #A1_COM .wbBarreOutilsComboListeOmbre*/
    z-index: 1;
    width: 15rem;
    /* margin-left: 0px; *//*décale de la bordure? colle à la combo?*/
    margin-top: -4px;
    padding-bottom: 2px;
    box-sizing: border-box;
}
.wbBarreOutilComboCheckbox:checked+.wbBarreOutilComboLabelReplie+.wbBarreOutilsComboListeDroite
{
	display:inline-block;
	top:100%;
	transform: translateX(-100%);
}

.wbBarreOutilComboCheckbox:checked+.wbBarreOutilComboLabelReplie
{
    pointer-events: none;/*évite de traiter le clic sur le label et donc de redéplier la combo alors qu'on veut la replier et que le blur l'a déjà fait*/
}

.wbBarreOutilComboLabel, .wbBarreOutilComboLabelReplie
{
    display:inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font: 400 13px Arial;
    font: 400 0.8125rem Arial;
    /*évite les combos trop larges*/
    /*faire varier un peu es largeurs?    
    max-width: 15rem;
    min-width: 7rem;
    ou forcer en dur?
    */
    width: 128px;
    width: 8rem;
}


/*combo des emojis*/
.wbBarreOutilComboLabelReplie[for*='COM_EMO_CHECKBOX']
{
	width : auto;
}

.wbBarreOutilComboLabelReplie[for*='COM_EMO_CHECKBOX']+ul>li
{
	display: inline-block;
	padding:3px;
	width: calc(100% / 7);
	box-sizing: border-box;
}
.wbBarreOutilComboLabelReplie[for*='COM_EMO_CHECKBOX']+ul>li>label
{
	display: inline-block;
	padding:0;
	width:auto;
	line-height: 1.7rem;
	font-size: 1.1rem;
}

/*barre sous les pictos de couleur => FR dit non, car il faudrait passer le picto en combo flèche dans ce cas  et gare à prendre la dernière valeur et pas la courante, trop piégeux
.wbBarreOutilsLi[id*='COM_COL']>.wbBarreOutilsImgPlancheWrap:after,.wbBarreOutilsLi[id*='COM_COF']>.wbBarreOutilsImgPlancheWrap:after
{
    content: '\25A0';
    font-size: 2rem;
    line-height: 0;
    font-weight: bold;
    display: block;
    position: absolute;
    bottom: 1px;
    height: 3px;
    width: 100%;
    overflow: hidden;
    padding: 0 2px;
    box-sizing: border-box;
    text-align: center;
}*/

.wbBarreOutilComboLabelReplie
{
    position: relative;
}
.wbBarreOutilComboLabelReplie>*
{
    display:block;
    padding-right: 24px;
    padding-left: 6px;
    padding-top: 3px;
    padding-bottom: 3px;
    /*... en fin */
    text-overflow: ellipsis;
    overflow: hidden;
    box-sizing: border-box;
    /*évite que les choix des combos agrandissent la barre d'outils*/
    font-size:13px !important;
    font-size:0.8125rem !important;
}
/*les emoji sont plus gros */
.wbBarreOutilComboLabelReplie[for*='COM_EMO_CHECKBOX']>*
{
    font-size: 0.9rem !important;
    line-height: 1.1rem;
}
.wbBarreOutilComboLabel
{
    padding:3px;
    padding-left: 32px;
    position:relative;
    width: 100%;
    box-sizing: border-box;
}

.wbBarreOutilComboLabelActif
{
    padding-right:16px;
    position:relative;
}

.wbBarreOutilComboLabelActif:before
{
    content: '';
    display: inline-block;
    width: 3px;
    height: 9px;
    border: solid #000;
    border-width: 0 2px 2px 0;
    transform: translateX(-16.5px) translateY(-50%) rotate(45deg);
    position: absolute;
    top: 50%;
}

.wbBarreOutilComboLabelReplie>*
{
    border:1px solid transparent;
}


/*TODO remplacer par la couleur palette => fait par la génération WDxxxHTML dans un <style> du <head> de l'iframe*/
.wbBarreOutilsLi:hover
{
    background-color:rgba(0, 0, 0, 0.03);
}
.wbBarreOutilsLi.wbActif, .wbBarreOutilComboCheckbox:checked+.wbBarreOutilComboLabelReplie>*
{
   
    background-color:rgba(0, 0, 0, 0.05);
    
}
.wbBarreOutilsComboListeOption:hover
{
    background-color:rgba(0, 0, 0, 0.05);
}

/*la mise en forme logique de paragraphe applique un style */
.wbBarreOutilsUl h1 {
    font-size: 18px;
    font-size: 1.125rem;
    font-weight: bold;
    font-style:normal;    
}
.wbBarreOutilsUl h2 {
    font-size: 18px;
    font-size: 1.125rem;
    font-style: italic;
    font-weight:normal;
}

.wbBarreOutilsUl h3 {
    font-size: 18px;
    font-size: 1.125rem;
    font-style:normal;
    font-weight:normal;
}

.wbBarreOutilsUl h4 {
    font-size: 13px;
    font-size: 0.8125rem;
    font-style:normal;
    font-weight: bold;
}
.wbBarreOutilsUl h5 {
    font-size: 13px;
    font-size: 0.8125rem;
    font-style: italic;
    font-weight:normal;
}
.wbBarreOutilsUl h6 {
    font-size: 13px;
    font-size: 0.8125rem;
    font-style:normal;
    font-weight:normal;    
    text-decoration:underline;
}


/* même hauteur pour toutes les lignes de combo .wbBarreOutilsComboListeOption et même pour les 1ers niveaux de li*/
.wbBarreOutilsComboListeOption/*.wbBarreOutilsLi*/
{
    height: 2rem;
    height: 32px;
    /*line-height: 20px; pourquoi?
    line-height: 1.2;*/
    box-sizing: border-box;
    overflow:hidden;  
}
/*.wbBarreOutilsLi*/ .wbBarreOutilsComboListeOption>label
{
    line-height:18px;
    line-height:1.125rem;
}


.wbBarreOutilsMiniBarreUl/*.wbSaisieRicheBarreImg ,.wbSaisieRicheBarreLien*/
{
    position:absolute;
    margin: 0;
    padding: 0;
    list-style:none;
    opacity: 0.8;
    transition:opacity 150ms ease;
    margin-top:calc(-24px - 10px);
    margin-top:calc(-1.5rem - 10px);/*10px pour être bien au dessus*/
    line-height: 0;
    display: block;
    top:0;
    left:0;
    /*outline: 1px solid #1a73e8;*/
    border-width: 1px;
    border-style: solid;/*bordure via couleur de palette */
    /*box-shadow: 0 1px 3px rgba(0,0,0,.2);/*élévation comme google docs*/
    width: auto;
    white-space: nowrap;
}

/*.wbSaisieRicheBarreImg:hover,.wbSaisieRicheBarreLien:hover*/
.wbBarreOutilsMiniBarreUl:hover
{
    opacity: 1;
}
/*.wbSaisieRicheBarreImgLi, .wbSaisieRicheBarreLienLi*/
.wbBarreOutilsMiniBarreLi
{
    margin: 0;
    padding: 0;
    position: relative;
    min-width:20px;
    width:1.5rem;
    min-height:20px;
    height:1.5rem;
    display:inline-block;
    cursor:pointer !important;
}


/*pas d'image qui dépasse de la saisie riche*/
.wbSaisieRicheIframeBody img
{
	max-width:100%;
	height:auto; /* respecte les proportions */
}
.wbSaisieRicheIframeHtml
{
	position:relative;/*pour les tracker absolute*/
}
.wbSaisieRicheIframeBody
{	
	width:100%; /* le body occupe toute l'iframe */
	margin: 0; /* le body ne doit donc pas avoir de marge sinon cela se cumule au width:100%*/
}
.wbSaisieRicheIframeBodyDragImg
{
	/*sugg utiliser la planche ?
	background-image: url(BarreSaisieRiche.svg);
    background-size: 500% auto;
    background-position: 100% calc( ( (34 - 25) / -34 ) * 100%);
    */
    border-color: #4285f4 !important;
}
.wbSaisieRicheIframeBody img:not(:active)/*évite au max que le drag and drop d'img copie les styles de survol */
{
   outline:1px solid transparent;
   transition: outline-color 200ms ease;
   cursor:grab;/*évite le | sur l'image */
}
/*effet de survol comme CKEditor 5*/
.wbSaisieRicheIframeBody img:hover:not(:active)/*évite au max que le drag and drop d'img copie les styles de survol */
{
   outline-color: #ffc83d;
}

.wbSaisieRicheIframeBodyGestionTable table
{
   outline:1px solid transparent;
   transition: outline-color 200ms ease;
}
.wbSaisieRicheIframeBodyGestionTable table td
{
   outline:1px solid transparent;
   transition: outline-color 200ms ease;
}

.wbSaisieRicheIframeBodyGestionTable table[border="0"]/*évite au max que le drag and drop copie les styles de survol */
,.wbSaisieRicheIframeBodyGestionTable table:not([border])/*évite au max que le drag and drop copie les styles de survol */
{   
   border: 1px dotted #0000003b !important; 
}

.wbSaisieRicheIframeBodyGestionTable table[border="0"] td:not([style*=border])
,.wbSaisieRicheIframeBodyGestionTable table:not([border]) td:not([style*=border])
{
    border: 1px solid #0000003b !important; 
}

/*effet de survol comme CKEditor 5*/
/*évite au max que le drag and drop copie les styles de survol */
/*
.wbSaisieRicheIframeBodyGestionTable table:hover:not(:active)
{
   outline-color: #ffc83d;
}
*/
.wbSaisieRicheIframeBodyGestionTable table:hover:not(:active) td:hover/*évite au max que le drag and drop copie les styles de survol */
{
   outline-color: #ffc83d;
}
.wbSaisieRicheTdSelectionne
{
    background:rgba(158, 207, 250, 0.3);
}
.wbSaisieRicheTableSelectionne
{
    outline: 1px solid #1a73e8 !important;
}

/* barre de modif du lien */
.wbSaisieRicheIframeBody a[href]
{
	outline: 1px solid transparent;
    outline-offset: 2px;
}
.wbSaisieRicheIframeBody a[href]:hover
{
	outline-color:#ffc83d;
    transition: outline-color 150ms ease-in-out;
}
.wbSaisieRicheLienSelectionne {
    outline: 1px solid #1a73e8 !important;
}

.wbSaisieRicheImgSelectionne
{
    outline: 1px solid #1a73e8 !important;
    cursor: move;	
}

.wbSaisieRicheImgGhost
{
    outline: 1px dotted #606060 !important;
    opacity:0.5;    
}

.wbSaisieRicheTrackerCoin {
	background-color: #1a73e8;
	border-color: white;/*blanc tournant autour des coins par rapport au cadre (outline de l'img sélectionnée)*/
	border-style: solid;
	border-width: 1px;
	box-sizing: border-box;
	height: 9px;
	position: absolute;
	width: 9px;
	z-index: 10000;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;  
	margin-left:-5.5px;
	margin-top:-5.5px;/*?? 5.5?? pour centrer au lieu de 4.5?*/
}

.wbSaisieRicheImgGhost,.wbSaisieRicheImgBulle
{
	position:absolute;
	z-index: 10001;
}

.wbSaisieRicheImgBulle
{
    width: auto;
    height: auto;
    padding: 6px;
    border-radius: 5px;
    background-color: #303030;
    color: white;
    font: 400 12px Arial;
    font: 400 0.75rem Arial;
    margin: 5px 0 0 5px;
}

.wbSaisieRicheGrip 
{
    bottom: -3px;
    height: 9px;
    width: 100%;
    cursor: ns-resize;
    margin-top: -9px;
    padding: 3px 0;
    box-sizing: border-box;
    background-clip: content-box;
    position: relative;
}

.wbSaisieRicheGrip:hover
{
    background-color:rgba(128, 128, 128, 0.3);
}


#wbSaisieRicheTrackerCoinNW
{
	cursor: nw-resize;
	top:0;left:0;
}

#wbSaisieRicheTrackerCoinSW
{
	cursor: sw-resize;	
	top:100%;left:0;
}

#wbSaisieRicheTrackerCoinNE
{
	cursor: ne-resize;
	top:0;left:100%;
}

#wbSaisieRicheTrackerCoinSE
{
	cursor: se-resize;			
	top:100%;left:100%;
}


.wbSaisieRicheTrackerCoinShift
{
	border-radius: 50%;
	/*picto de rotation */
	cursor:crosshair !important;/*comme google docs*/
	/*background-color: #ffc83d;	reste de la même couleur CHA ok, et google docs pareil */
}

.wbSaisieRicheWrapperSelection
{
	position:absolute;
}

/*évite le look bleu pendant le drag n drop / déplacement d'image */
.wbSaisieRicheIframeBody img:active::selection {
    background: 0 0;
}

.wbSaisieRicheIframeBodyDragEnCours *
{
	cursor: inherit !important;
}

.wbSaisieRicheTrackerMarge {
    background-color: #f9cc9d;
    border-color: white;
    border-style: solid;
    border-width: 1px;
    box-sizing: border-box;
    height: 9px;
    position: absolute;
    width: 9px;
    z-index: 10000;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}


#wbSaisieRicheTrackerMargeT {
    cursor: ns-resize;
    top: -5.5px;
    left: calc(50% - 5.5px);
}

#wbSaisieRicheTrackerMargeR {
    cursor: ew-resize;
    top: calc(50% - 5.5px);
    left: calc(100% - 5.5px);
}

#wbSaisieRicheTrackerMargeB {
    cursor: ns-resize;
    top: calc(100% - 5.5px);
    left: calc(50% - 5.5px);
}

#wbSaisieRicheTrackerMargeL {
    cursor: ew-resize;
    top: calc(50% - 5.5px);
    left: -5.5px;
}

#wbSaisieRicheTrackerMargeWrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    outline: 1px dashed #f9cc9d;
    border-color: rgba(249, 204, 157, 0.15);
    border-style: solid;
    border-width: 0;
}

.wbSaisieRicheTrackerMargeMagnetismeT
{
	border-top-color: rgba(249, 204, 157, 0.35) !important;
}
.wbSaisieRicheTrackerMargeMagnetismeR
{
	border-right-color: rgba(249, 204, 157, 0.35) !important;
}
.wbSaisieRicheTrackerMargeMagnetismeB
{
	border-bottom-color: rgba(249, 204, 157, 0.35) !important;
}
.wbSaisieRicheTrackerMargeMagnetismeL
{
	border-left-color: rgba(249, 204, 157, 0.35) !important;
}


/* jquery.Jcrop.css v0.9.12 - MIT License */
/*
  The outer-most container in a typical Jcrop instance
  If you are having difficulty with formatting related to styles
  on a parent element, place any fixes here or in a like selector

  You can also style this element if you want to add a border, etc
  A better method for styling can be seen below with .jcrop-light
  (Add a class to the holder and style elements for that extended class)
*/
.jcrop-holder {
  direction: ltr;
  text-align: left;
}
/* Selection Border */
.jcrop-vline,
.jcrop-hline {
  background: #ffffff url('data:image/gif;base64,R0lGODlhCAAIAJEAAKqqqv///wAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQJCgAAACwAAAAACAAIAAACDZQFCadrzVRMB9FZ5SwAIfkECQoAAAAsAAAAAAgACAAAAg+ELqCYaudeW9ChyOyltQAAIfkECQoAAAAsAAAAAAgACAAAAg8EhGKXm+rQYtC0WGl9oAAAIfkECQoAAAAsAAAAAAgACAAAAg+EhWKQernaYmjCWLF7qAAAIfkECQoAAAAsAAAAAAgACAAAAg2EISmna81UTAfRWeUsACH5BAkKAAAALAAAAAAIAAgAAAIPFA6imGrnXlvQocjspbUAACH5BAkKAAAALAAAAAAIAAgAAAIPlIBgl5vq0GLQtFhpfaIAACH5BAUKAAAALAAAAAAIAAgAAAIPlIFgknq52mJowlixe6gAADs=');
  font-size: 0;  
  position: absolute;
}
.jcrop-vline {
  height: 100%;
  width: 1px !important;
}
.jcrop-vline.right {
  right: 0;
}
.jcrop-hline {
  height: 1px !important;
  width: 100%;
}
.jcrop-hline.bottom {
  bottom: 0;
}
/* Invisible click targets */
.jcrop-tracker {
  height: 100%;
  width: 100%;
  /* "turn off" link highlight */
  -webkit-tap-highlight-color: transparent;
  /* disable callout, image save panel */
  -webkit-touch-callout: none;
  /* disable cut copy paste */
  -webkit-user-select: none;
}
/* Selection Handles */
.jcrop-handle {
  background-color: #333333;
  border: 1px #eeeeee solid;
  width: 7px;
  height: 7px;
  font-size: 1px;
}
.jcrop-handle.ord-n {
  left: 50%;
  margin-left: -4px;
  margin-top: -4px;
  top: 0;
}
.jcrop-handle.ord-s {
  bottom: 0;
  left: 50%;
  margin-bottom: -4px;
  margin-left: -4px;
}
.jcrop-handle.ord-e {
  margin-right: -4px;
  margin-top: -4px;
  right: 0;
  top: 50%;
}
.jcrop-handle.ord-w {
  left: 0;
  margin-left: -4px;
  margin-top: -4px;
  top: 50%;
}
.jcrop-handle.ord-nw {
  left: 0;
  margin-left: -4px;
  margin-top: -4px;
  top: 0;
}
.jcrop-handle.ord-ne {
  margin-right: -4px;
  margin-top: -4px;
  right: 0;
  top: 0;
}
.jcrop-handle.ord-se {
  bottom: 0;
  margin-bottom: -4px;
  margin-right: -4px;
  right: 0;
}
.jcrop-handle.ord-sw {
  bottom: 0;
  left: 0;
  margin-bottom: -4px;
  margin-left: -4px;
}
/* Dragbars */
.jcrop-dragbar.ord-n,
.jcrop-dragbar.ord-s {
  height: 7px;
  width: 100%;
}
.jcrop-dragbar.ord-e,
.jcrop-dragbar.ord-w {
  height: 100%;
  width: 7px;
}
.jcrop-dragbar.ord-n {
  margin-top: -4px;
}
.jcrop-dragbar.ord-s {
  bottom: 0;
  margin-bottom: -4px;
}
.jcrop-dragbar.ord-e {
  margin-right: -4px;
  right: 0;
}
.jcrop-dragbar.ord-w {
  margin-left: -4px;
}
/* The "jcrop-light" class/extension */
.jcrop-light .jcrop-vline,
.jcrop-light .jcrop-hline {
  background: #ffffff;
  filter: alpha(opacity=70) !important;
  opacity: .70!important;
}
.jcrop-light .jcrop-handle {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  background-color: #000000;
  border-color: #ffffff;
  border-radius: 3px;
}
/* The "jcrop-dark" class/extension */
.jcrop-dark .jcrop-vline,
.jcrop-dark .jcrop-hline {
  background: #000000;
  filter: alpha(opacity=70) !important;
  opacity: 0.7 !important;
}
.jcrop-dark .jcrop-handle {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  background-color: #ffffff;
  border-color: #000000;
  border-radius: 3px;
}
/* Simple macro to turn off the antlines */
.solid-line .jcrop-vline,
.solid-line .jcrop-hline {
  background: #ffffff;
}
/* Fix for twitter bootstrap et al. */
.jcrop-holder img,
img.jcrop-preview {
  max-width: none;
}



















/* Menu du graphe */

.grfWDPaintCSSoGetInfoCSS
{
	position:fixed;visibility:hidden;white-space:nowrap;will-change:content, font;
}

.grfBurger {
    background: transparent;
    height: 2rem;
    width: 2rem;
    position: absolute;
    right:0;/*CHA demande 3 points à droite plutôt que burger à gauche*/
    margin-right: 0.5rem;
    margin-top: 0.5rem;
    cursor: pointer;
    z-index: 1;/*pour être au dessus des calques du mode édition*/
    transition: transform 1ms ease-in-out 201ms, opacity 200ms ease-in-out;/*délai sur le scale en sortie*/
}

.pr:hover>.grfBurger
{
	transition: opacity 200ms ease-in-out;/*pas de transition sur le scale au survol*/
}

.grfBurger input
{
	display: none;
}

.grfBurger>div 
{
    display: inline-block;
    background:#cfcfcf;	
    background: rgba(207, 207, 207, 0.5);
    border-radius: 50%;
    box-sizing: border-box;
    transform: rotateZ(90deg);
    padding: 0.875rem 0.525rem;
    line-height: 0;
    font-size: 0;
}
.grfBurger>div:active
{
    background: rgba(207, 207, 207, 1);
}
.grfBurger>input:checked+div
{
    background: rgba(207, 207, 207, 0.8);
}

.grfBurger>div:before,
.grfBurger>div:after,
.grfBurger>div>span {
    background: white;
    content: ""; 
    transition: 0.75s;
    width: 0.25rem;
    height: 0.25rem;
    border-radius: 50%;
    display: inline-block;
}
.grfBurger>div>span
{
    margin: 0 0.1rem;
}


.grfBurgerMenu .grfType input ,
.grfBurgerMenu .grfBulle input ,
.grfBurgerMenu .grfLegende input ,
.grfBurgerMenu .grfOptions input ,
.grfBurgerMenu .grfQuadrillage input ,
.grfBurgerMenu .grfBouton input 
{
   position: absolute;
   top: -9999px;
   left: -9999px;
   opacity: 0;
   display:none; /* il faut le display none car le clic sur l'input fait scroller la vue pour tenter d'afficher cet input */   
}

.grfBurgerMenuPosition ul,
.grfBurgerMenuPosition li {
    margin: 0;
    padding: 0;
    list-style: none;
}

.grfBurgerMenuPosition ul,
.grfBurgerMenuPosition li {
    font-family: inherit; /*laisse hériter la police par défaut de la page */
    font-size: 0.7rem;
}

.grfBurgerMenuPosition {
    position: fixed;
    z-index: 799;
    will-change: transform; /* pour être sur un calque supérieur même pendant le zoom */
}

.grfBurgerMenu {
    max-width: 70vw;
    width: 20rem;
    height: auto;
    max-height: 85vh;
    overflow: auto;
    overflow-y: scroll;
    overflow-x: hidden;
    box-sizing: border-box;
}

.grfBurgerMenu {
    border: 1px solid #CFCFCF;
    color:black;/*texte noir sur fond blanc semi opaque*/
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.grfBurgerMenu .ui-slider-horizontal .ui-slider-handle {
	top: -0.5em;
	width: 0.8em;
}
.grfBurgerMenu .ui-slider-horizontal {
	height: 0.4em;
}

.grfBurgerMenu>li {
    padding-bottom: 0.5rem; /* padding  et pas margin afin que le li prenne toute la largeur et donc que le draggable sur le li soit efficace */
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.5rem;    
    /*border-top: 0.1rem solid #CFCFCF;  sans bord*/
    box-sizing: border-box;
}

.grfBurgerMenu>li:first-child /* pas de fond de titre donc il faut un séparateur , .grfTitreDeplacable .grfBurgerMenu>li:nth-child(2)*/ {
    border-top: none;
}

.grfTitreDeplacable .grfBurgerMenu>li.grfTitre {
    margin: 0;
    margin: 0;
    padding: 0.5rem;
    box-sizing: border-box;
    /* pas de couleur de fond
    background-color: rgb(66, 129, 244);
    color: white;
    background-image: url("data:image/png;base64,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");
    */
}

.grfTitre h1 {
    font-size: 0.95rem;
}

@media print 
{
	.grfBurgerMenuPosition
	{
		display:none !important;
	}
}

.grfEnreg,.grfEnregSAV,.grfEnregPRI
.grfBulle,
.grfLegende,
.grfOpacite,
.grfZoomHorizontal,.grfZoomVertical
.grfOptions,
.grfOptionDegrade,
.grfOptionLissage,
.grfOptionInteractif,
.grfOptionAntiAliasing,
.grfOptionRelief,
.grfOptionAnimation,
.grfQuadrillage,
.grfQuadrillageHorizontal,
.grfQuadrillageVertical,
.grfType,.grfTypePIE,.grfTypeCOL,.grfTypeCUR,.grfTypeSTO
{
  display: none;
}

.grfTypePIE .grfTypePIE,.grfTypeCOL .grfTypeCOL,.grfTypeCUR .grfTypeCUR,.grfTypeSTO .grfTypeSTO
{
  display: inline;  
}

.grfQuadrillageVertical .grfQuadrillageVertical,.grfQuadrillageHorizontal .grfQuadrillageHorizontal
{
  display: inline;
}

.grfEnregSAV .grfEnregSAV, .grfEnregPRI .grfEnregPRI
{
 display: inline-block; 
}

.grfOptionDegrade .grfOptionDegrade,
.grfOptionLissage .grfOptionLissage,
.grfOptionInteractif .grfOptionInteractif,
.grfOptionAntiAliasing .grfOptionAntiAliasing,
.grfOptionRelief .grfOptionRelief,
.grfOptionAnimation .grfOptionAnimation
{
  display: block;
}

.grfEnregSAV .grfEnreg,.grfEnregPRI .grfEnreg,
.grfBulle .grfBulle,
.grfLegendeAucune .grfLegende,.grfLegendeHaut .grfLegende,.grfLegendeGauche .grfLegende,.grfLegendeBas .grfLegende,.grfLegendeDroite .grfLegende,
.grfOpacite .grfOpacite,
.grfZoomVertical .grfZoomVertical,
.grfZoomHorizontal .grfZoomHorizontal,
.grfOptions .grfOptions,
.grfQuadrillageVertical .grfQuadrillage,.grfQuadrillageHorizontal .grfQuadrillage,
.grfTypePIE .grfType,.grfTypeCOL .grfType,.grfTypeCUR .grfType,.grfTypeSTO .grfType,
.grfType .grfType,
.grfOptionDegrade .grfOptions,
.grfOptionLissage .grfOptions,
.grfOptionInteractif .grfOptions,
.grfOptionAntiAliasing .grfOptions,
.grfOptionRelief .grfOptions,
.grfOptionAnimation .grfOptions
{
  display: block;
}

.grfBurgerMenu
{
  display:block;
}

.grfBurgerMenuPosition
{
  display:block;
  /*transition:300ms opacity, 300ms transform; via fadeOut*/
  transition:500ms transform;
  transform-origin: top left;

}

.grfBurgerMenuPosition.grfMenuMasque
{
    /*opacity: 0; via fadeOut() */
    /*transform: translateY(-1rem) scale(0.8);*/	
    /*transform: translateY(-2rem) scale(0.1,0.1);     */
    transform: scale(0.1,0.1);    
    transition:transform 1ms 401ms; 
}

/* bouton */

.grfBurgerMenu .grfBouton>*:hover {
    background-color: rgb(66, 129, 244);
    color: white;
}

.grfBurgerMenu .grfBouton input+span {
    /*border: 3px solid transparent;*/
    padding: 0.5rem 0.25rem;
    text-align: center;
}

.grfBurgerMenu .grfBouton input:checked+span {
    /*border: 1px solid #CFCFCF;*/
    /*box-shadow:-1px  -1px  0 0   #CFCFCF, 1px  1px   0 0   #CFCFCF;*/
    outline: 1px solid #CFCFCF;
}


/* quadrillage */

.grfBurgerMenu .grfQuadrillage li {
    float:left;
    width: 50%;
    padding: 0.125rem;
    box-sizing: border-box;
}


.grfBurgerMenu .grfQuadrillage ul {
    padding: 0.25rem 0;
}

.grfBurgerMenu .grfQuadrillage li label span {
    display: block;
}

/* options */


.grfBurgerMenu .grfOptions>label {
    width: 30%;
    float: left;
    padding: 0;
    box-sizing: border-box;
    margin: 0.25rem;
}

.grfBurgerMenu .grfOptions>label span {
    padding: 0.5rem 0.125rem;/*anticrénelage ne rentre pas à cause de l'ascenseur vertical*/
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
}


/* opacité */

.grfBurgerMenu .grfOpacite li {
    display: table-cell;
    width: 60%;
    box-sizing: border-box;
}

.grfBurgerMenu .grfOpacite li:first-child {
    width: 40%;
    padding: 1rem 0;
}

.grfBurgerMenu .grfOpacite .grfSaisie {
    position: relative;
    
    /* pas de bordure à gauche du portar, trop de bordure depuis l'ajout du zoom 
    border-left: 1px solid #CFCFCF;
    */

	/* pas de bordure de droite du potentiommètre, mieux vaut qu'il aille jusqu'au bout du li et ait un blanc gauche/droite */
    /* border-right: 1px solid #CFCFCF; */
}

/* transparence via input 
.grfBurgerMenu .grfOpacite .grfSaisie:hover,
.grfBurgerMenu .grfOpacite .grfSaisie:hover * {
    background-color: rgb(66, 129, 244);
    color: white;
}
*/

.grfBurgerMenu .grfOpacite .grfSaisie .ui-slider-range { background: #e9e9e9; }

/* masque à côté du slider */
.grfBurgerMenu .grfOpacite .grfOpaciteSaisie+*,.grfBurgerMenu .grfOpacite .grfOpaciteSaisie+*+*
{
	display: none;
}

.grfBurgerMenu .grfOpacite {
	/* pas de bordure de droite du potentiommètre, mieux vaut qu'il aille jusqu'au bout du li et ait un blanc gauche/droite */
	margin: 0;
	padding: 0 0 0 0.5rem;
	display: table;
	width: 100%;
}

.grfBurgerMenu .grfOpacite li {
	padding: 0.5rem 1.5rem;
	vertical-align: middle;
}

.grfBurgerMenu .grfOpacite .grfSaisie input {
    padding: 0.125rem 0;
    border: none;
    text-align: center;
    font-size: 0.7rem;
    outline: none;
    width: 100%;
}
.grfBurgerMenu .grfOpacite .grfSaisie input[type=number]+span {
	margin-left: -1rem;
}
/* masque le % si c'est un slider */
.grfBurgerMenu .grfOpacite .grfSaisie input[type=range]+span {
	display:none;
}
.grfBurgerMenuPosition .grfOpacite>ul {
    display: table-row;
    width: 100%;
}
.grfBurgerMenuPosition .grfOpacite>ul>li {
    display: table-cell;
}

.grfBurgerMenu .grfOpacite .grfSaisie:hover span {
    background-color: rgb(66, 129, 244);
    color: white;
}
/* zoom */

.grfBurgerMenu .grfZoom li {
    display: table-cell;
    width: 60%;
    box-sizing: border-box;
}

.grfBurgerMenu .grfZoom li:first-child {
    width: 40%;
    padding: 0.5rem;
}

.grfBurgerMenu .grfZoom .grfSaisie {
    position: relative;

    /* pas de bordure à gauche du portar, trop de bordure depuis l'ajout du zoom 
    border-left: 1px solid #CFCFCF;
    */

	/* pas de bordure de droite du potentiommètre, mieux vaut qu'il aille jusqu'au bout du li et ait un blanc gauche/droite */
    /* border-right: 1px solid #CFCFCF; */
}

.grfBurgerMenu .grfZoom {
	/* pas de bordure de droite du potentiommètre, mieux vaut qu'il aille jusqu'au bout du li et ait un blanc gauche/droite */
	margin: 0;
	padding:0;
	display: table;
	width: 100%;
}

.grfBurgerMenu .grfZoom li {
	padding: 0.5rem 1.5rem;
	vertical-align: middle;
}

.grfBurgerMenu .grfZoom .grfSaisie input {
    padding: 0.125rem 0;
    border: none;
    text-align: center;
    font-size: 0.7rem;
    outline: none;
    width: auto;
}
.grfBurgerMenuPosition .grfZoom>ul {
    display: table-row;
    width: 100%;
}
.grfBurgerMenuPosition .grfZoom>ul>li {
    display: table-cell;
}
.grfBurgerMenu .grfZoom+.grfZoom {
    border-top:none;
}
.grfBurgerMenu .grfZoom .grfSaisie:hover span {
    background-color: rgb(66, 129, 244);
    color: white;
}
/* bouton pleine largeur*/
.grfBurgerMenu .grfZoom span 
{
	display: block;
}

/* légende */

.grfBurgerMenu .grfLegende li {
    float: left;
}

.grfBurgerMenu .grfLegende li:first-child {
    width: 27%;
    padding-top: 0.25rem;
    margin-top: 0.25rem;
}


.grfBurgerMenu .grfLegende label {
    float: left;
    padding: 0.5rem;
    margin: 0.5rem;
    position: relative;
}

.grfBurgerMenu .grfLegende label span {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    /*background-image: url(/MON_PROJET_WEB/Planche-images-pictos.png);*/
    background-size: 6.25rem 58.125rem;
    background-clip: content-box;
    /*text-indent: -999px; non car le outline essaye d'englober le décalage */
	font-size: 1px;
	line-height: 0;
	color: transparent;    
}

.grfBurgerMenu .grfLegende label:hover span,
.grfBurgerMenu .grfLegende label input:checked+span {
    margin-top: -0.25rem;
    margin-left: -0.25rem;
    margin-right: -0.25rem;/*pour ne pas décaler les champs suivants*/
    border: 0.25rem solid transparent;
    /*box-shadow:-1px  -1px  1px  1px   #CFCFCF, 1px  1px  1px  1px   #CFCFCF;*/
    /*background-color:transparent;*/
}

.grfBurgerMenu .grfLegende label input:checked+span {
    outline: 1px solid #CFCFCF;
    /*background-position-x: 0;*/
}

.grfBurgerMenu .grfLegende label:hover span,
.grfBurgerMenu .grfLegende label:hover input:checked+span {
    /*background-position-x: -1rem;*/
    border-color: rgb(66, 129, 244);
    background-color: rgb(66, 129, 244);
}
.grfBurgerMenu .grfLegende .grfLegendeAucune span {
    background-position: -0.2rem 4.5rem;
}

.grfBurgerMenu .grfLegende .grfLegendeHaut span {
    background-position: -0.2rem 3.6rem;
}

.grfBurgerMenu .grfLegende .grfLegendeBas span {
    background-position: -0.2rem 2.8rem;
}

.grfBurgerMenu .grfLegende .grfLegendeGauche span {
    background-position: -0.2rem 1.84rem;
}

.grfBurgerMenu .grfLegende .grfLegendeDroite span {
    background-position: -0.2rem 0.95rem;
}

.grfBurgerMenu .grfLegende .grfLegendeAucune:hover span,
.grfBurgerMenu .grfLegende .grfLegendeAucune:hover input:checked+span {
    background-position:-1.1rem 4.5rem;
}

.grfBurgerMenu .grfLegende .grfLegendeHaut:hover span,
.grfBurgerMenu .grfLegende .grfLegendeHaut:hover input:checked+span {
    background-position:-1.1rem 3.6rem;
}

.grfBurgerMenu .grfLegende .grfLegendeBas:hover span,
.grfBurgerMenu .grfLegende .grfLegendeBas:hover input:checked+span {
    background-position:-1.1rem 2.8rem;
}

.grfBurgerMenu .grfLegende .grfLegendeGauche:hover span,
.grfBurgerMenu .grfLegende .grfLegendeGauche:hover input:checked+span {
    background-position:-1.1rem 1.84rem;
}

.grfBurgerMenu .grfLegende .grfLegendeDroite:hover span,
.grfBurgerMenu .grfLegende .grfLegendeDroite:hover input:checked+span {
    background-position:-1.1rem 0.95rem;
}


/* bulle */

.grfBurgerMenu .grfBulle li {
    float: left;
}

.grfBurgerMenu .grfBulle li:first-child {
    width: 27%;
    padding-top: 0.25rem;
    margin-top: 0.25rem;
}


.grfBurgerMenu .grfBulle label {
    float: right;
    padding: 0.25rem;
}

.grfBurgerMenu .grfBulle label {
    float: left;
    padding: 0.5rem;
    margin: 0.5rem;
    position: relative;
}

.grfBurgerMenu .grfBulle label span {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    /*background-image: url(/MON_PROJET_WEB/Planche-images-pictos.png);*/
    background-size: 6.25rem 58.125rem;
    background-clip: content-box;
    /*text-indent: -999px; non car le outline essaye d'englober le décalage */
	font-size: 1px;
	line-height: 0;
	color: transparent;       
    background-position-x: -2rem;
}

.grfBurgerMenu .grfBulle label:hover span,
.grfBurgerMenu .grfBulle label input:checked+span {
    margin-top: -0.25rem;
    margin-left: -0.25rem;
    margin-right: -0.25rem;
    border: 0.25rem solid transparent;
    /*box-shadow:-1px  -1px  1px  1px   #CFCFCF, 1px  1px  1px  1px   #CFCFCF;*/
    /*background-color:transparent;*/
}

.grfBurgerMenu .grfBulle label input:checked+span {
    outline: 1px solid #CFCFCF;
}

.grfBurgerMenu .grfBulle label:hover span,
.grfBurgerMenu .grfBulle label:hover input:checked+span {
   /* background-position-x: -3rem;*/
    border-color: rgb(66, 129, 244);
    background-color: rgb(66, 129, 244);
}

.grfBurgerMenu .grfBulle .grfBulleSimple span {
    background-position: -2rem 3.7rem;
}

.grfBurgerMenu .grfBulle .grfMire span {
    background-position: -2rem 2.7rem;
}

.grfBurgerMenu .grfBulle .grfMireMultiple span {
    background-position: -2rem 1.8rem;
}

.grfBurgerMenu .grfBulle .grfSansBulle span {
    background-position: -0.2rem 4.5rem;/* manque le picto sans bulle ? on prend le picto sans légende*/
}

.grfBurgerMenu .grfBulle .grfSansBulle:hover span,
.grfBurgerMenu .grfBulle .grfSansBulle:hover input:checked+span {
    background-position:-1.1rem 4.5rem;/* manque le picto sans bulle ? on prend le picto sans légende*/
}

.grfBurgerMenu .grfBulle .grfBulleSimple:hover span,
.grfBurgerMenu .grfBulle .grfBulleSimple:hover input:checked+span {
    background-position:-2.95rem 3.7rem;
}
.grfBurgerMenu .grfBulle .grfMire:hover span,
.grfBurgerMenu .grfBulle .grfMire:hover input:checked+span {
    background-position:-2.95rem 2.7rem;
}
.grfBurgerMenu .grfBulle .grfMireMultiple:hover span,
.grfBurgerMenu .grfBulle .grfMireMultiple:hover input:checked+span {
    background-position:-2.95rem 1.8rem;
}

/* type */

.grfBurgerMenu .grfType {
    padding-top: 0;
	/* +0.5rem  de margin qui passe en padding pour que le li prenne toute la place et donc prenne le draggable */
    padding-bottom: 1.5rem;/*1rem pour les noms de graphes sur plusieurs lignes */
}

.grfBurgerMenu .grfType li {
    float: left;
    width: 0;
    padding: 16.6666666666%;
    height: 0;
    position: relative;
    margin-top: 0.25rem;
}

.grfBurgerMenu .grfType label {
    border: 1px solid transparent;
}

.grfBurgerMenu .grfType li:hover>label {
    /*border:1px solid #CFCFCF;*/
    border-radius: 2px;
    /*background-position-x: 100%;*/
}

.grfBurgerMenu .grfType li input:checked+span {
    border: 1px solid #CFCFCF;
}

.grfBurgerMenu .grfType li:hover>label>span {
    color: rgb(66, 129, 244);
}


/* types autres */

.grfBurgerMenu .grfTypeAutres>a {
    
    cursor: pointer;
    /*    
    color: #CFCFCF;
    display: block;
    text-align: right;
    position: absolute;
    top: -1rem;
    right: -0.25rem;
	*/

	/*sugg fleche*/;
    display: inline-block;
    width: 100%;
    text-align: center;
    line-height: 1;
    position: absolute;
    margin-top: 0.75rem;
}
.grfBurgerMenu .grfTypeAutres>a>i {
    border: solid #CFCFCF;
    border-width: 0 0.0625rem 0.0625rem 0;
    display: inline-block;
    padding: 0.1875rem;
    transform: translateY(-0.15rem) rotate(45deg);
    transition:transform 400ms ease;
}
.grfBurgerMenu .grfTypeAutres>a:hover>i {
    border-color: rgb(66, 129, 244);
}

.grfBurgerMenu .grfTypeAutres>ul[style*='block']+a>i {
    transform: translateY(-0.15rem) rotate(225deg);
}

.grfBurgerMenu .grfTypeAutres ul {
    display: none;
}

.grfBurgerMenu .grfType .grfTypeAutres {
    width: 100%;
    position: relative;
    height: auto;
    box-sizing: border-box;
    padding: 0;
    margin-top: 0;
    background: none;
}

.grfBurgerMenu .grfType li label span {
    position: absolute;
    left: -20%;
    width: 150%;
    text-align: center;
    top: 105%;
    padding: 0.125rem 0;
    text-overflow: ellipsis;
    overflow: hidden;
}

.grfBurgerMenu .grfType li label {
    width: 60%;
    height: 60%;
    position: absolute;
    top: 20%;
    left: 10%;
    /*background-image: url(/MON_PROJET_WEB/Planche-images-pictos.png);*/
    background-repeat: no-repeat;
    background-size: 200% auto;
}

/*
.grfBurgerMenu .grfType 
{
--wbGrfNbType:18;
}
.grfBurgerMenu .grfType li>label {
    background-position: 0 calc( (100% + 5.86rem) - ( (100% + 2.271rem) * var(--wbGrfIndiceType) / var(--wbGrfNbType) ) - 2px );
}
.grfBurgerMenu .grfType li:hover>label {
    background-position: 100% calc( (100% + 5.86rem) - ( (100% + 2.271rem) * var(--wbGrfIndiceType) / var(--wbGrfNbType) ) - 2px );
}
.grfTypeSecteur { --wbGrfIndiceType:0; }
.grfTypeHemicycle { --wbGrfIndiceType:1; }
.grfTypeDonut { --wbGrfIndiceType:2; }
.grfTypeEntonnoir { --wbGrfIndiceType:3; }
.grfTypeHistoGroupe { --wbGrfIndiceType:4; }
.grfTypeHistoGroupeHoriz { --wbGrfIndiceType:5; }
.grfTypeHistoEmpile { --wbGrfIndiceType:6; }
.grfTypeHistoEmpileHoriz { --wbGrfIndiceType:7; }
.grfTypeWaterfall { --wbGrfIndiceType:8; }
.grfTypeCourbe { --wbGrfIndiceType:9; }
.grfTypeNuage { --wbGrfIndiceType:10; }
.grfTypeAire { --wbGrfIndiceType:11; }
.grfTypeRadar { --wbGrfIndiceType:12; }
.grfTypeBulles { --wbGrfIndiceType:13; }
.grfTypeChandelier { --wbGrfIndiceType:14; }
.grfTypeBarChart { --wbGrfIndiceType:15; }
.grfTypeMinMax { --wbGrfIndiceType:16; }
.grfTypeHeatMap { --wbGrfIndiceType:17; }
*/
.grfBurgerMenu .grfType>ul>li.grfTypeHistoGroupeHoriz,.grfBurgerMenu .grfType>ul>li.grfTypeHistoEmpileHoriz
{
	margin-bottom: 1rem;/*car sur 3 lignes*/
}


.grfBurgerMenu .grfType li.grfTypeSunburst label {
    background-position: 0 91.8%;
}
.grfBurgerMenu .grfType li.grfTypeSecteur label {
    background-position: 0 86.7%;
}
.grfBurgerMenu .grfType li.grfTypeHemicycle label {
    background-position: 0 81.6%;
}
.grfBurgerMenu .grfType li.grfTypeDonut label {
    background-position: 0 76.5%;
}
.grfBurgerMenu .grfType li.grfTypeEntonnoir label {
    background-position: 0 71.4%;
}
.grfBurgerMenu .grfType li.grfTypeHistoGroupe label {
    background-position: 0 66.3%;
}
.grfBurgerMenu .grfType li.grfTypeHistoGroupeHoriz label {
    background-position: 0 61.15%;
}
.grfBurgerMenu .grfType li.grfTypeHistoEmpile label {
    background-position: 0 56.1%;
}
.grfBurgerMenu .grfType li.grfTypeHistoEmpileHoriz label {
    background-position: 0 50.9%;
}
.grfBurgerMenu .grfType li.grfTypeWaterfall label {
    background-position: 0 45.8%;
}
.grfBurgerMenu .grfType li.grfTypeCourbe label {
    background-position: 0 40.7%;
}
.grfBurgerMenu .grfType li.grfTypeNuage label {
    background-position: 0 35.6%;
}
.grfBurgerMenu .grfType li.grfTypeAire label {
    background-position: 0 30.6%;
}
.grfBurgerMenu .grfType li.grfTypeRadar label {
    background-position: 0 25.5%;
}
.grfBurgerMenu .grfType li.grfTypeBulles label {
    background-position: 0 20.4%;
}
.grfBurgerMenu .grfType li.grfTypeChandelier label {
    background-position: 0 15.3%;
}
.grfBurgerMenu .grfType li.grfTypeBarChart label {
    background-position: 0 10.3%;
}
.grfBurgerMenu .grfType li.grfTypeMinMax label {
    background-position: 0 5.2%;
}
.grfBurgerMenu .grfType li.grfTypeHeatMap label {
    background-position: 0 0%;
}



.grfBurgerMenu .grfType li:hover.grfTypeSunburst label {
    background-position: 100% 91.8%;
}
.grfBurgerMenu .grfType li:hover.grfTypeSecteur label {
    background-position: 100% 86.7%;
}
.grfBurgerMenu .grfType li:hover.grfTypeHemicycle label {
    background-position: 100% 81.6%;
}
.grfBurgerMenu .grfType li:hover.grfTypeDonut label {
    background-position: 100% 76.5%;
}
.grfBurgerMenu .grfType li:hover.grfTypeEntonnoir label {
    background-position: 100% 71.4%;
}
.grfBurgerMenu .grfType li:hover.grfTypeHistoGroupe label {
    background-position: 100% 66.3%;
}
.grfBurgerMenu .grfType li:hover.grfTypeHistoGroupeHoriz label {
    background-position: 100% 61.15%;
}
.grfBurgerMenu .grfType li:hover.grfTypeHistoEmpile label {
    background-position: 100% 56.1%;
}
.grfBurgerMenu .grfType li:hover.grfTypeHistoEmpileHoriz label {
    background-position: 100% 50.9%;
}
.grfBurgerMenu .grfType li:hover.grfTypeWaterfall label {
    background-position: 100% 45.8%;
}
.grfBurgerMenu .grfType li:hover.grfTypeCourbe label {
    background-position: 100% 40.7%;
}
.grfBurgerMenu .grfType li:hover.grfTypeNuage label {
    background-position: 100% 35.6%;
}
.grfBurgerMenu .grfType li:hover.grfTypeAire label {
    background-position: 100% 30.6%;
}
.grfBurgerMenu .grfType li:hover.grfTypeRadar label {
    background-position: 100% 25.5%;
}
.grfBurgerMenu .grfType li:hover.grfTypeBulles label {
    background-position: 100% 20.4%;
}
.grfBurgerMenu .grfType li:hover.grfTypeChandelier label {
    background-position: 100% 15.3%;
}
.grfBurgerMenu .grfType li:hover.grfTypeBarChart label {
    background-position: 100% 10.3%;
}
.grfBurgerMenu .grfType li:hover.grfTypeMinMax label {
    background-position: 100% 5.2%;
}
.grfBurgerMenu .grfType li:hover.grfTypeHeatMap label {
    background-position: 100% 0%;
}




/* enreg et imprimer */

.grfBurgerMenu .grfEnreg {
    margin: 0;
    padding: 0.5rem 0;
    border-top: 0.1rem solid #CFCFCF;
    box-sizing: border-box;
    background-color: #F5F5F5;
}

.grfBurgerMenu .grfEnreg button {
    -webkit-appearance: none;
    background-color: #F5F5F5;
    border: 1px solid #CFCFCF;
    border: 1px solid transparent;
    padding: 0.25rem;
    font-size: 0.7rem;
    margin: 0.25rem;
    vertical-align: middle;
}

.grfBurgerMenu .grfEnreg button:hover {
    border: 1px solid #CFCFCF;
    background-color: white;
}

.grfBurgerMenu .grfEnreg button.wbActif,.grfBurgerMenu .grfEnreg button:active {
    border: 1px solid white;
    color: white;
    background-color: rgb(66, 129, 244);
}

.grfBurgerMenu .grfBouton span {
    transition: 150ms border;
}

/*masque les autres éléments fixed de la page*/
.grfFullScreenHTML [id^="dww"]
{
	z-index:-1 !important;
}

.grfFullScreen /*c'est la table*/
{
	position:fixed;
	top:0;
	left:0;
	width:100% !important;
	height:100% !important;
	background-color:white;
	box-sizing: border-box;
}
.grfFullScreen td
{
	padding:1% !important;
}

.grfFullScreen svg, .grfFullScreen img
{
	width:100% !important;
	height:100% !important;	
}

.grfFullScreenHTML
{
	position:absolute;
	top:-999999999px;
}

/* permet de relativiser le fixed du graphe dans le body et pas à la dernière balises transformée */
.grfFullScreenT0 *  {
    overflow:visible !important;
    will-change:initial !important;
    transform:none !important;
}


/*place le burger de graphe en haut de l'écran en mode plein écran */
.grfFullScreenBurger
{
	position:fixed;
	right:2rem;
	top:2rem;
}

/*rgb(66,129,244);*/


/* Ripple Out */

@-webkit-keyframes hvr-ripple-out {
    100% {
        top: -12px;
        right: -12px;
        bottom: -12px;
        left: -12px;
        opacity: 0;
    }
}

@keyframes hvr-ripple-out {
    100% {
        top: -12px;
        right: -12px;
        bottom: -12px;
        left: -12px;
        opacity: 0;
    }
}

/* ripple bizarre
.grfBouton>span {
    vertical-align: middle;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
}

.grfBouton>span:before {
    content: "";
    position: absolute;
    border: transparent solid 6px;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    -webkit-animation-duration: 300ms;
    animation-duration: 300ms;
    animation-fill-mode: forwards;
}

.grfBouton>span:hover:before {
    border: rgba(225, 225, 225, 0.5) solid 0.8rem;
}

.grfBouton>span:hover:before,
.grfBouton>span:focus:before,
.grfBouton>span:active:before {
    -webkit-animation-name: hvr-ripple-out;
    animation-name: hvr-ripple-out;
}
*/

.rating td { height:100%; }

.rating ul {
    height: 100%;
    margin: 0 auto;
    display: block;
    list-style: none;
    padding: 0;
}
.rating li {
    height: 100%;
    margin: 0 auto;    
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-position: 0 center;
    list-style: none;
    padding: 0;
    cursor: pointer;
}

/* affichage mobile de la barre d'outils */
/*car la largeur max est de 70vw et que la largeur utile est de 20rem*/
@media only all and (max-width:30rem) 
{

html .grfBurgerMenuPosition ul, html .grfBurgerMenuPosition li
{
    font-size: 1rem;
}
html .grfBurgerMenu .grfType .grfTypeAutres>ul
{
	padding-top:1rem;	
}

html .grfBurgerMenuPosition
{
    position:fixed !important;
    top:2rem !important;
    left:0.5rem !important;
    right:0.5rem !important;
    bottom:0.5rem !important;
}
html .grfBurger input:checked+div
{
    position: fixed;
    top: 0.5rem;
    left: 0.75rem;
}

html .grfBurgerMenu
{
    width:100%;
    max-width:100%;
    max-height: 100%;
    overflow: auto;
    box-sizing: border-box;
}

html .grfBurgerMenu .grfType li {
    padding: 16%;
    margin-top: 0;
}
html .grfBurgerMenu .grfType .grfTypeAutres
{
	padding: 0;
	padding-top: 1rem;
}

html .grfBurgerMenu .grfType li span {
    display: none;
}

html .grfBurgerMenu .grfLegende li,html .grfBurgerMenu .grfBulle li, html .grfBurgerMenu .grfOpacite li
{
    max-width:75%;
}
html .grfBurgerMenu .grfLegende li:first-child,html .grfBurgerMenu .grfBulle li:first-child, html .grfBurgerMenu .grfOpacite li:first-child
{
    width: 25%;
	text-overflow: ellipsis;
    overflow: hidden;
}

html .grfBurgerMenu input+span
{
    background-image:none  !important;
    
}
html .grfBurgerMenu .grfQuadrillage li,html .grfBurgerMenu .grfOptions>label
{
    width: 100%;
    margin: 0.25rem 0;
    padding: 0;
}
html .grfBurgerMenu .grfLegende label span, html .grfBurgerMenu .grfBulle label span {
    position: static;
    width: auto;
    height: auto;
    font-size: 1rem;
    line-height: normal;
    color: inherit;
}
html .grfBurgerMenu .grfLegende label, html .grfBurgerMenu .grfBulle label  {
    float: left;
    padding: 0.5rem;
    margin: 0;
    min-width: 50%;
    position: relative;
    box-sizing: border-box;
}

html .grfBurgerMenu .grfEnreg button {
    width:100%;
    font-size: 1rem;
    margin: 0;
}

html .grfBurgerMenu .grfTypeAutres>a {
    right: 1rem;
    font-size: 2rem;
    right: 0;
    height: 1rem;
    border: 1px solid #CFCFCF;
    text-align: center;
    padding: 0;
    margin: 0;
    line-height: 0;
}

html .grfBurgerMenu .grfType li label {
    width: 90%;
    height: 90%;  
    left: 5%;
    top: 5%;
}

html .grfBurgerMenu .grfType>ul>li.grfTypeHistoGroupeHoriz,html .grfBurgerMenu .grfType>ul>li.grfTypeHistoEmpileHoriz
{
	margin-bottom:0;/*car pas de texte*/
}

html .grfTitreDeplacable .grfBurgerMenu>li.grfTitre {
    left: 0;
    right: 0;
    margin: 0;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    width: 100%;
}

html .grfBurgerMenu .grfZoom li {
    display:none;
}
html .grfBurgerMenu .grfZoom li:first-child {
	display:block;
	width: 100%;
	padding: 0;
}

html .grfBurgerMenu .grfZoom+.grfZoom {
	padding-top:0;
}
html .grfBurgerMenu .grfZoom {
	padding: 0.75rem 0.5rem;	
}

}

/* titre épinglé */

.grfTitreDeplacable .grfBurgerMenu>li.grfTitre {
    position: absolute;
    background-color: rgba(255,255,255,0.8);
    z-index: 799;
    box-sizing: border-box;
    margin: 0;
    width: 90%;/*permet de ne pas aller jusqu'au dessus de l'ascenseur sans pour autant connaître vraiment sa taille*/

	/*QW#265253 dégradé blanc vers transparent de gauche à droite plutôt que uniquement blanc */
	background: rgba(255,255,255,1);
	background: -moz-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
/*	
[Deprecation] -webkit-gradient is deprecated. Please use linear-gradient or radial-gradient instead.
[Deprecation] -webkit-linear-gradient is deprecated. Please use linear-gradient instead.

	background: -webkit-gradient(left top, right top, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(255,255,255,0)));
	background: -webkit-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
*/
	background: -o-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
	background: -ms-linear-gradient(left, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
	background: linear-gradient(to right, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ffffff', GradientType=1 );
}

.grfTitreDeplacable .grfBurgerMenu>li.grfTitre+li {
    margin-top: 2rem;
    border-top: 0;
}

/* mode édition */
.grfTracker
{
	cursor : move;
	position : absolute;

	outline: 2px dotted rgba(0, 0, 128, 0);
	transition: 300ms outline, 300ms outline-offset;
	outline-offset: -5px;
	border: 1px dotted navy;	
}

.grfTracker:hover {
    outline-color: navy;
    outline-offset: 0;
    border-color: transparent;
}

.grfTracker.grfTrackerLegende
{
	background-color:rgba(173, 216, 230, 0.23);
}
.grfTracker.grfTrackerTitre
{
	background-color:rgba(173, 230, 216, 0.23);
}
.grfTracker.grfTrackerGraphe
{
	background-color:rgba(230, 216, 173, 0.23);
}
.grfTracker .grfFermer
{
    float: right;
    margin-top: -1rem;
    margin-right: -1rem;
    cursor: pointer;
    color: #fff;
    border: 1px solid #AEAEAE;
    border-radius: 30px;
    background: #605F61;
    font-size: 1rem;
    font-weight: bold;
    display: inline-block;
    line-height: 0px;
    padding: 0.55rem 0.125rem;   
}

.grfTracker .grfFermer {
    opacity: 0.5;
    transition:300ms opacity;
}
.grfTracker:hover .grfFermer {
    opacity: 1;
}
.grfTracker .grfFermer:before
{
	content: "\D7";
}.grfTracker .grfFermer.grfMasque:before
{
	content: "\21B6";
}
/* ignore la souris qui passe dessus pendant le drag */
.ui-draggable-dragging li * {
	pointer-events:none;
}

/* For image replacement */
.ir { display: block; border: 0; text-indent: -999em; overflow: hidden; background-color: transparent; background-repeat: no-repeat; text-align: left; direction: ltr; *line-height: 0; }
.ir br { display: none; }

/* Hide from both screenreaders and browsers: h5bp.com/u */
.hidden { display: none !important; visibility: hidden; }

/* Hide only visually, but have it available for screenreaders: h5bp.com/v */
.visuallyhidden { border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; }

/* Extends the .visuallyhidden class to allow the element to be focusable when navigated to via the keyboard: h5bp.com/p */
.visuallyhidden.focusable:active, .visuallyhidden.focusable:focus { clip: auto; height: auto; margin: 0; overflow: visible; position: static; width: auto; }

/* Hide visually and from screenreaders, but maintain layout */
.invisible { visibility: hidden; }

/* Contain floats: h5bp.com/q */
.clearfix:before, .clearfix:after { content: ""; display: table; }
.clearfix:after { clear: both; }
.clearfix { *zoom: 1; }

.wbMaxWidthIeTdImg { width:100%; }
@media screen { @media (min-width: 0px) { .wbMaxWidthIeTdImg { width:auto; } } }
@-moz-document url-prefix() {.wbMaxWidthIeTdImg { width:100%; }}

input.wbCoche+i 
{
    background-repeat: repeat;
    display: block;
}
input.wbCoche {
    opacity:0;
}
input.wbCoche+i {
    background-position: -400% 0;
}
label:hover>input.wbCoche+i,input.wbCoche:hover+i {
    background-position: -700% 0;
}
input.wbCoche:active+i {
    background-position: -500% 0;
}
input.wbCoche.wbgrise+i, input[disabled]+i {
    background-position: -600% 0;
}
input.wbCoche:checked+i {
    background-position: 0 0;
}
label:hover>input.wbCoche:checked+i,input.wbCoche:checked:hover+i {
    background-position: -300% 0;
}
input.wbCoche:checked:active+i {
    background-position: -100% 0;
}
input.wbCoche:checked.wbgrise+i, input[disabled]:checked+i {
    background-position: -200% 0;
}
.wbCoche+i
{
    -webkit-animation-fill-mode: forwards !important;
    -moz-animation-fill-mode: forwards !important;
    -o-animation-fill-mode: forwards !important;
    -ms-animation-fill-mode: forwards !important;
    animation-fill-mode: forwards !important;
}
.wbSelecteurInterrupteur *
{
	text-decoration: inherit;
}

ol.wbPlanPuces
{
    list-style: none;
    padding: 0;
    margin: 0;
    position: absolute;
    left: 0;
    width: 100%;	
	text-align: center;
	line-height: 0;
	display:block;
	z-index:99;	/*passe au dessus des plans pendant l'anim */
	/* à personnaliser */
    bottom: 10px;
    transition: opacity 400ms;    
}
ol.wbPlanPuces>li
{
	padding: 0;
    margin: 0;
    list-style: none;
    display: inline;
    display: inline-block;    
}
ol.wbPlanPuces>li>label
{
	display: inline-block;	
	/*cursor: pointer; implicite*/
}
ol.wbPlanPuces>li>label>input
{
	display:none;
}
ol.wbPlanPuces>li>label>input+i
{
	-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;
}

/* à personnaliser */
.wbPlanPuces>li+li
{
    margin-left: 1rem;
}

/* évite la dernière marge droite */
.wbPlanPuces>li.wbPlanMasque
{
    display:none !important;
}
.wbPlanPuces.wbPlanMasque,.wbPlanBoutonPrecedent.wbPlanMasque,.wbPlanBoutonSuivant.wbPlanMasque
{
    opacity:0;
}

.wbPlanBoutonPrecedent,.wbPlanBoutonSuivant,.wbGalerieBoutonFermer,.wbGalerieBoutonPrecedent,.wbGalerieBoutonSuivant
{
	position:absolute;	
	display:block;
	-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;
	transition: 300ms transform;
	text-indent: -9999px !important;/* Edge casse complètement l'affichage en cas de clic rapide si text-indent: 9999px; */
	padding:0 !important;/*pas de padding car la taille est donnée par le width/height de tracker et qu'il n'y a pas de contenu*/
	/* centrage vertical, attend un margin-top négatif à personnaliser */
	top:50%;	
	cursor: pointer;
	z-index:99;	/*passe au dessus des plans pendant l'anim */	
	/*transition: opacity 400ms;  l'opacité est faite via jquery.fadeto */
}
.wbGalerieBoutonFermer,.wbGalerieBoutonPrecedent,.wbGalerieBoutonSuivant
{
	/*demande de CHA : passe au dessus  de la répétition de galerie */
	position:fixed;
	z-index: 991;
}
.wbGalerieBoutonPrecedent.wbgrise,.wbGalerieBoutonSuivant.wbgrise
{
	pointer-events: auto !important;/*plus fort que le none par défaut*/
	cursor: not-allowed;	
}

/* permet de ne pas décaler le planche en case de bordures */
.wbPlanBoutonPrecedent.wbplanche,.wbPlanBoutonSuivant.wbplanche,.wbPlanPuces .wbCoche+i,.wbGalerieBoutonFermer.wbplanche,.wbGalerieBoutonPrecedent.wbplanche,.wbGalerieBoutonSuivant.wbplanche
{
	background-origin: border-box;
}

.wbPlanBoutonPrecedent,.wbGalerieBoutonPrecedent
{
	/* à personnaliser */
	left:0;
}
.wbPlanBoutonSuivant,.wbGalerieBoutonFermer,.wbGalerieBoutonSuivant
{
	/* à personnaliser */
	right:0;
}
.wbGalerieBoutonFermer
{
	/* à personnaliser */
	top:0;
}

/* planche png pour IE car il ne supporte pas le scale de SVG */
/* planche pour MS Edge qui supporte mal le scale SVG TB#96998 => le 1er affichage est correct mais pas en changement de présentation cf. @media max-width */
.grfBurgerMenu .grfLegende label span, .grfBurgerMenu .grfBulle label span, .grfBurgerMenu .grfType li label
{
 background-image:url('grf_sprites.png');

}

/* planche svg   */
.grfBurgerMenu.grfPlancheSVG .grfLegende label span, .grfBurgerMenu.grfPlancheSVG .grfBulle label span, .grfBurgerMenu.grfPlancheSVG .grfType li label
{	
background-image:url('grf_sprites.svg');
}

/*!
 * animate.css -http://daneden.me/animate
 * Version - 3.5.1
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2016 Daniel Eden
 */

.animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.animated.infinite {
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

.animated.hinge {
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
}

.animated.flipOutX,
.animated.flipOutY,
.animated.bounceIn,
.animated.bounceOut {
    -webkit-animation-duration: .75s;
    animation-duration: .75s;
}

@-webkit-keyframes bounce {
    from, 20%, 53%, 80%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        -webkit-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0);
    }

    40%, 43% {
        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        -webkit-transform: translate3d(0, -30px, 0);
        transform: translate3d(0, -30px, 0);
    }

    70% {
        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        -webkit-transform: translate3d(0, -15px, 0);
        transform: translate3d(0, -15px, 0);
    }

    90% {
        -webkit-transform: translate3d(0,-4px,0);
        transform: translate3d(0,-4px,0);
    }
}

@keyframes bounce {
    from, 20%, 53%, 80%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        -webkit-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0);
    }

    40%, 43% {
        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        -webkit-transform: translate3d(0, -30px, 0);
        transform: translate3d(0, -30px, 0);
    }

    70% {
        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        -webkit-transform: translate3d(0, -15px, 0);
        transform: translate3d(0, -15px, 0);
    }

    90% {
        -webkit-transform: translate3d(0,-4px,0);
        transform: translate3d(0,-4px,0);
    }
}

.bounce {
    -webkit-animation-name: bounce;
    animation-name: bounce;
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
}

@-webkit-keyframes flash {
    from, 50%, to {
        opacity: 1;
    }

    25%, 75% {
        opacity: 0;
    }
}

@keyframes flash {
    from, 50%, to {
        opacity: 1;
    }

    25%, 75% {
        opacity: 0;
    }
}

.flash {
    -webkit-animation-name: flash;
    animation-name: flash;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes pulse {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    50% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05);
        transform: scale3d(1.05, 1.05, 1.05);
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

@keyframes pulse {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    50% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05);
        transform: scale3d(1.05, 1.05, 1.05);
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

.pulse {
    -webkit-animation-name: pulse;
    animation-name: pulse;
}

@-webkit-keyframes rubberBand {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    30% {
        -webkit-transform: scale3d(1.25, 0.75, 1);
        transform: scale3d(1.25, 0.75, 1);
    }

    40% {
        -webkit-transform: scale3d(0.75, 1.25, 1);
        transform: scale3d(0.75, 1.25, 1);
    }

    50% {
        -webkit-transform: scale3d(1.15, 0.85, 1);
        transform: scale3d(1.15, 0.85, 1);
    }

    65% {
        -webkit-transform: scale3d(.95, 1.05, 1);
        transform: scale3d(.95, 1.05, 1);
    }

    75% {
        -webkit-transform: scale3d(1.05, .95, 1);
        transform: scale3d(1.05, .95, 1);
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

@keyframes rubberBand {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    30% {
        -webkit-transform: scale3d(1.25, 0.75, 1);
        transform: scale3d(1.25, 0.75, 1);
    }

    40% {
        -webkit-transform: scale3d(0.75, 1.25, 1);
        transform: scale3d(0.75, 1.25, 1);
    }

    50% {
        -webkit-transform: scale3d(1.15, 0.85, 1);
        transform: scale3d(1.15, 0.85, 1);
    }

    65% {
        -webkit-transform: scale3d(.95, 1.05, 1);
        transform: scale3d(.95, 1.05, 1);
    }

    75% {
        -webkit-transform: scale3d(1.05, .95, 1);
        transform: scale3d(1.05, .95, 1);
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

.rubberBand {
    -webkit-animation-name: rubberBand;
    animation-name: rubberBand;
}

@-webkit-keyframes shake {
    from, to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
    }

    20%, 40%, 60%, 80% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }
}

@keyframes shake {
    from, to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
    }

    20%, 40%, 60%, 80% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }
}

@keyframes shakeLight {
    from, to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    25%, 75% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
    }

    50% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }
}

@keyframes shakeLightLittle {
    from, to {
        transform: translate3d(0, 0, 0);
    }

    25%, 75% {
        transform: translate3d(-5px, 0, 0);
    }

    50% {
        transform: translate3d(5px, 0, 0);
    }
}

.shake {
    -webkit-animation-name: shake;
    animation-name: shake;
}

@-webkit-keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }

    6.5% {
        -webkit-transform: translateX(-6px) rotateY(-9deg);
        transform: translateX(-6px) rotateY(-9deg);
    }

    18.5% {
        -webkit-transform: translateX(5px) rotateY(7deg);
        transform: translateX(5px) rotateY(7deg);
    }

    31.5% {
        -webkit-transform: translateX(-3px) rotateY(-5deg);
        transform: translateX(-3px) rotateY(-5deg);
    }

    43.5% {
        -webkit-transform: translateX(2px) rotateY(3deg);
        transform: translateX(2px) rotateY(3deg);
    }

    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}

@keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }

    6.5% {
        -webkit-transform: translateX(-6px) rotateY(-9deg);
        transform: translateX(-6px) rotateY(-9deg);
    }

    18.5% {
        -webkit-transform: translateX(5px) rotateY(7deg);
        transform: translateX(5px) rotateY(7deg);
    }

    31.5% {
        -webkit-transform: translateX(-3px) rotateY(-5deg);
        transform: translateX(-3px) rotateY(-5deg);
    }

    43.5% {
        -webkit-transform: translateX(2px) rotateY(3deg);
        transform: translateX(2px) rotateY(3deg);
    }

    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}

.headShake {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-name: headShake;
    animation-name: headShake;
}

@-webkit-keyframes swing {
    20% {
        -webkit-transform: rotate3d(0, 0, 1, 15deg);
        transform: rotate3d(0, 0, 1, 15deg);
    }

    40% {
        -webkit-transform: rotate3d(0, 0, 1, -10deg);
        transform: rotate3d(0, 0, 1, -10deg);
    }

    60% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
        transform: rotate3d(0, 0, 1, 5deg);
    }

    80% {
        -webkit-transform: rotate3d(0, 0, 1, -5deg);
        transform: rotate3d(0, 0, 1, -5deg);
    }

    to {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg);
    }
}

@keyframes swing {
    20% {
        -webkit-transform: rotate3d(0, 0, 1, 15deg);
        transform: rotate3d(0, 0, 1, 15deg);
    }

    40% {
        -webkit-transform: rotate3d(0, 0, 1, -10deg);
        transform: rotate3d(0, 0, 1, -10deg);
    }

    60% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
        transform: rotate3d(0, 0, 1, 5deg);
    }

    80% {
        -webkit-transform: rotate3d(0, 0, 1, -5deg);
        transform: rotate3d(0, 0, 1, -5deg);
    }

    to {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg);
    }
}

.swing {
    -webkit-transform-origin: top center;
    transform-origin: top center;
    -webkit-animation-name: swing;
    animation-name: swing;
}

@-webkit-keyframes tada {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    10%, 20% {
        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
    }

    30%, 50%, 70%, 90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }

    40%, 60%, 80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

@keyframes tada {
    from {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    10%, 20% {
        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
    }

    30%, 50%, 70%, 90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }

    40%, 60%, 80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }

    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

.tada {
    -webkit-animation-name: tada;
    animation-name: tada;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes wobble {
    from {
        -webkit-transform: none;
        transform: none;
    }

    15% {
        -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
        transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    }

    30% {
        -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
        transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    }

    45% {
        -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
        transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    }

    60% {
        -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
        transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    }

    75% {
        -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
        transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes wobble {
    from {
        -webkit-transform: none;
        transform: none;
    }

    15% {
        -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
        transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    }

    30% {
        -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
        transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    }

    45% {
        -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
        transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    }

    60% {
        -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
        transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    }

    75% {
        -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
        transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

.wobble {
    -webkit-animation-name: wobble;
    animation-name: wobble;
}

@-webkit-keyframes jello {
    from, 11.1%, to {
        -webkit-transform: none;
        transform: none;
    }

    22.2% {
        -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
        transform: skewX(-12.5deg) skewY(-12.5deg);
    }

    33.3% {
        -webkit-transform: skewX(6.25deg) skewY(6.25deg);
        transform: skewX(6.25deg) skewY(6.25deg);
    }

    44.4% {
        -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
        transform: skewX(-3.125deg) skewY(-3.125deg);
    }

    55.5% {
        -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
        transform: skewX(1.5625deg) skewY(1.5625deg);
    }

    66.6% {
        -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
        transform: skewX(-0.78125deg) skewY(-0.78125deg);
    }

    77.7% {
        -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
        transform: skewX(0.390625deg) skewY(0.390625deg);
    }

    88.8% {
        -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
        transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    }
}

@keyframes jello {
    from, 11.1%, to {
        -webkit-transform: none;
        transform: none;
    }

    22.2% {
        -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
        transform: skewX(-12.5deg) skewY(-12.5deg);
    }

    33.3% {
        -webkit-transform: skewX(6.25deg) skewY(6.25deg);
        transform: skewX(6.25deg) skewY(6.25deg);
    }

    44.4% {
        -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
        transform: skewX(-3.125deg) skewY(-3.125deg);
    }

    55.5% {
        -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
        transform: skewX(1.5625deg) skewY(1.5625deg);
    }

    66.6% {
        -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
        transform: skewX(-0.78125deg) skewY(-0.78125deg);
    }

    77.7% {
        -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
        transform: skewX(0.390625deg) skewY(0.390625deg);
    }

    88.8% {
        -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
        transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    }
}

.jello {
    -webkit-animation-name: jello;
    animation-name: jello;
    -webkit-transform-origin: center;
    transform-origin: center;
}

@-webkit-keyframes bounceIn {
    from, 20%, 40%, 60%, 80%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }

    20% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1);
    }

    40% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03, 1.03, 1.03);
        transform: scale3d(1.03, 1.03, 1.03);
    }

    80% {
        -webkit-transform: scale3d(.97, .97, .97);
        transform: scale3d(.97, .97, .97);
    }

    to {
        opacity: 1;
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

@keyframes bounceIn {
    from, 20%, 40%, 60%, 80%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }

    20% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1);
    }

    40% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03, 1.03, 1.03);
        transform: scale3d(1.03, 1.03, 1.03);
    }

    80% {
        -webkit-transform: scale3d(.97, .97, .97);
        transform: scale3d(.97, .97, .97);
    }

    to {
        opacity: 1;
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}

.bounceIn {
    -webkit-animation-name: bounceIn;
    animation-name: bounceIn;
}

@-webkit-keyframes bounceInDown {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -3000px, 0);
        transform: translate3d(0, -3000px, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, 25px, 0);
        transform: translate3d(0, 25px, 0);
    }

    75% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0);
    }

    90% {
        -webkit-transform: translate3d(0, 5px, 0);
        transform: translate3d(0, 5px, 0);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes bounceInDown {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -3000px, 0);
        transform: translate3d(0, -3000px, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, 25px, 0);
        transform: translate3d(0, 25px, 0);
    }

    75% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0);
    }

    90% {
        -webkit-transform: translate3d(0, 5px, 0);
        transform: translate3d(0, 5px, 0);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

.bounceInDown {
    -webkit-animation-name: bounceInDown;
    animation-name: bounceInDown;
}

@-webkit-keyframes bounceInLeft {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(-3000px, 0, 0);
        transform: translate3d(-3000px, 0, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(25px, 0, 0);
        transform: translate3d(25px, 0, 0);
    }

    75% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
    }

    90% {
        -webkit-transform: translate3d(5px, 0, 0);
        transform: translate3d(5px, 0, 0);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes bounceInLeft {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    0% {
        opacity: 0;
        -webkit-transform: translate3d(-3000px, 0, 0);
        transform: translate3d(-3000px, 0, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(25px, 0, 0);
        transform: translate3d(25px, 0, 0);
    }

    75% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
    }

    90% {
        -webkit-transform: translate3d(5px, 0, 0);
        transform: translate3d(5px, 0, 0);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

.bounceInLeft {
    -webkit-animation-name: bounceInLeft;
    animation-name: bounceInLeft;
}

@-webkit-keyframes bounceInRight {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(3000px, 0, 0);
        transform: translate3d(3000px, 0, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(-25px, 0, 0);
        transform: translate3d(-25px, 0, 0);
    }

    75% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }

    90% {
        -webkit-transform: translate3d(-5px, 0, 0);
        transform: translate3d(-5px, 0, 0);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes bounceInRight {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(3000px, 0, 0);
        transform: translate3d(3000px, 0, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(-25px, 0, 0);
        transform: translate3d(-25px, 0, 0);
    }

    75% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }

    90% {
        -webkit-transform: translate3d(-5px, 0, 0);
        transform: translate3d(-5px, 0, 0);
    }

    to {
        -webkit-transform: none;
        transform: none;
    }
}

.bounceInRight {
    -webkit-animation-name: bounceInRight;
    animation-name: bounceInRight;
}

@-webkit-keyframes bounceInUp {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 3000px, 0);
        transform: translate3d(0, 3000px, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);
    }

    75% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0);
    }

    90% {
        -webkit-transform: translate3d(0, -5px, 0);
        transform: translate3d(0, -5px, 0);
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes bounceInUp {
    from, 60%, 75%, 90%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }

    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 3000px, 0);
        transform: translate3d(0, 3000px, 0);
    }

    60% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);
    }

    75% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0);
    }

    90% {
        -webkit-transform: translate3d(0, -5px, 0);
        transform: translate3d(0, -5px, 0);
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.bounceInUp {
    -webkit-animation-name: bounceInUp;
    animation-name: bounceInUp;
}

@-webkit-keyframes bounceOut {
    20% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9);
    }

    50%, 55% {
        opacity: 1;
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }
}

@keyframes bounceOut {
    20% {
        -webkit-transform: scale3d(.9, .9, .9);
        transform: scale3d(.9, .9, .9);
    }

    50%, 55% {
        opacity: 1;
        -webkit-transform: scale3d(1.1, 1.1, 1.1);
        transform: scale3d(1.1, 1.1, 1.1);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }
}

.bounceOut {
    -webkit-animation-name: bounceOut;
    animation-name: bounceOut;
}

@-webkit-keyframes bounceOutDown {
    20% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0);
    }

    40%, 45% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0);
    }
}

@keyframes bounceOutDown {
    20% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0);
    }

    40%, 45% {
        opacity: 1;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0);
    }
}

.bounceOutDown {
    -webkit-animation-name: bounceOutDown;
    animation-name: bounceOutDown;
}

@-webkit-keyframes bounceOutLeft {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0);
    }
}

@keyframes bounceOutLeft {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0);
    }
}

.bounceOutLeft {
    -webkit-animation-name: bounceOutLeft;
    animation-name: bounceOutLeft;
}

@-webkit-keyframes bounceOutRight {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0);
    }
}

@keyframes bounceOutRight {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0);
    }
}

.bounceOutRight {
    -webkit-animation-name: bounceOutRight;
    animation-name: bounceOutRight;
}

@-webkit-keyframes bounceOutUp {
    20% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0);
    }

    40%, 45% {
        opacity: 1;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0);
    }
}

@keyframes bounceOutUp {
    20% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0);
    }

    40%, 45% {
        opacity: 1;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0);
    }
}

.bounceOutUp {
    -webkit-animation-name: bounceOutUp;
    animation-name: bounceOutUp;
}

@-webkit-keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.fadeIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
}

@-webkit-keyframes fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInDown {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
}

@-webkit-keyframes fadeInDownBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInDownBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInDownBig {
    -webkit-animation-name: fadeInDownBig;
    animation-name: fadeInDownBig;
}

@-webkit-keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInLeftBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInLeftBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInLeftBig {
    -webkit-animation-name: fadeInLeftBig;
    animation-name: fadeInLeftBig;
}

@-webkit-keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInRight {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight;
}

@-webkit-keyframes fadeInRightBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInRightBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInRightBig {
    -webkit-animation-name: fadeInRightBig;
    animation-name: fadeInRightBig;
}

@-webkit-keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInUp {
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
}

@-webkit-keyframes fadeInUpBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInUpBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInUpBig {
    -webkit-animation-name: fadeInUpBig;
    animation-name: fadeInUpBig;
}

@-webkit-keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

.fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut;
}

@-webkit-keyframes fadeOutDown {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

.fadeOutDown {
    -webkit-animation-name: fadeOutDown;
    animation-name: fadeOutDown;
}

@-webkit-keyframes fadeOutDownBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0);
    }
}

@keyframes fadeOutDownBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 2000px, 0);
        transform: translate3d(0, 2000px, 0);
    }
}

.fadeOutDownBig {
    -webkit-animation-name: fadeOutDownBig;
    animation-name: fadeOutDownBig;
}

@-webkit-keyframes fadeOutLeft {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

@keyframes fadeOutLeft {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

.fadeOutLeft {
    -webkit-animation-name: fadeOutLeft;
    animation-name: fadeOutLeft;
}

@-webkit-keyframes fadeOutLeftBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0);
    }
}

@keyframes fadeOutLeftBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0);
    }
}

.fadeOutLeftBig {
    -webkit-animation-name: fadeOutLeftBig;
    animation-name: fadeOutLeftBig;
}

@-webkit-keyframes fadeOutRight {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

@keyframes fadeOutRight {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

.fadeOutRight {
    -webkit-animation-name: fadeOutRight;
    animation-name: fadeOutRight;
}

@-webkit-keyframes fadeOutRightBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0);
    }
}

@keyframes fadeOutRightBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0);
    }
}

.fadeOutRightBig {
    -webkit-animation-name: fadeOutRightBig;
    animation-name: fadeOutRightBig;
}

@-webkit-keyframes fadeOutUp {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@keyframes fadeOutUp {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

.fadeOutUp {
    -webkit-animation-name: fadeOutUp;
    animation-name: fadeOutUp;
}

@-webkit-keyframes fadeOutUpBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0);
    }
}

@keyframes fadeOutUpBig {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -2000px, 0);
        transform: translate3d(0, -2000px, 0);
    }
}

.fadeOutUpBig {
    -webkit-animation-name: fadeOutUpBig;
    animation-name: fadeOutUpBig;
}

@-webkit-keyframes flip {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
    }

    40% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
    }

    50% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    80% {
        -webkit-transform: perspective(400px) scale3d(.95, .95, .95);
        transform: perspective(400px) scale3d(.95, .95, .95);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }
}

@keyframes flip {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
    }

    40% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out;
    }

    50% {
        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    80% {
        -webkit-transform: perspective(400px) scale3d(.95, .95, .95);
        transform: perspective(400px) scale3d(.95, .95, .95);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }
}

.animated.flip {
    -webkit-backface-visibility: visible;
    backface-visibility: visible;
    -webkit-animation-name: flip;
    animation-name: flip;
}

@-webkit-keyframes flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

@keyframes flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

.flipInX {
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation-name: flipInX;
    animation-name: flipInX;
}

@-webkit-keyframes flipInY {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        opacity: 1;
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

@keyframes flipInY {
    from {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
    }

    40% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
    }

    60% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        opacity: 1;
    }

    80% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    }

    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

.flipInY {
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation-name: flipInY;
    animation-name: flipInY;
}

@-webkit-keyframes flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1;
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
}

@keyframes flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1;
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
}

.flipOutX {
    -webkit-animation-name: flipOutX;
    animation-name: flipOutX;
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
}

@-webkit-keyframes flipOutY {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        opacity: 1;
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        opacity: 0;
    }
}

@keyframes flipOutY {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }

    30% {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
        opacity: 1;
    }

    to {
        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        opacity: 0;
    }
}

.flipOutY {
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation-name: flipOutY;
    animation-name: flipOutY;
}

@-webkit-keyframes lightSpeedIn {
    from {
        -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
        transform: translate3d(100%, 0, 0) skewX(-30deg);
        opacity: 0;
    }

    60% {
        -webkit-transform: skewX(20deg);
        transform: skewX(20deg);
        opacity: 1;
    }

    80% {
        -webkit-transform: skewX(-5deg);
        transform: skewX(-5deg);
        opacity: 1;
    }

    to {
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

@keyframes lightSpeedIn {
    from {
        -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
        transform: translate3d(100%, 0, 0) skewX(-30deg);
        opacity: 0;
    }

    60% {
        -webkit-transform: skewX(20deg);
        transform: skewX(20deg);
        opacity: 1;
    }

    80% {
        -webkit-transform: skewX(-5deg);
        transform: skewX(-5deg);
        opacity: 1;
    }

    to {
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

.lightSpeedIn {
    -webkit-animation-name: lightSpeedIn;
    animation-name: lightSpeedIn;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
}

@-webkit-keyframes lightSpeedOut {
    from {
        opacity: 1;
    }

    to {
        -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
        transform: translate3d(100%, 0, 0) skewX(30deg);
        opacity: 0;
    }
}

@keyframes lightSpeedOut {
    from {
        opacity: 1;
    }

    to {
        -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
        transform: translate3d(100%, 0, 0) skewX(30deg);
        opacity: 0;
    }
}

.lightSpeedOut {
    -webkit-animation-name: lightSpeedOut;
    animation-name: lightSpeedOut;
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
}

@-webkit-keyframes rotateIn {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, -200deg);
        transform: rotate3d(0, 0, 1, -200deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

@keyframes rotateIn {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, -200deg);
        transform: rotate3d(0, 0, 1, -200deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

.rotateIn {
    -webkit-animation-name: rotateIn;
    animation-name: rotateIn;
}

@-webkit-keyframes rotateInDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

@keyframes rotateInDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

.rotateInDownLeft {
    -webkit-animation-name: rotateInDownLeft;
    animation-name: rotateInDownLeft;
}

@-webkit-keyframes rotateInDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

@keyframes rotateInDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

.rotateInDownRight {
    -webkit-animation-name: rotateInDownRight;
    animation-name: rotateInDownRight;
}

@-webkit-keyframes rotateInUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

@keyframes rotateInUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

.rotateInUpLeft {
    -webkit-animation-name: rotateInUpLeft;
    animation-name: rotateInUpLeft;
}

@-webkit-keyframes rotateInUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -90deg);
        transform: rotate3d(0, 0, 1, -90deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

@keyframes rotateInUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -90deg);
        transform: rotate3d(0, 0, 1, -90deg);
        opacity: 0;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: none;
        transform: none;
        opacity: 1;
    }
}

.rotateInUpRight {
    -webkit-animation-name: rotateInUpRight;
    animation-name: rotateInUpRight;
}

@-webkit-keyframes rotateOut {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, 200deg);
        transform: rotate3d(0, 0, 1, 200deg);
        opacity: 0;
    }
}

@keyframes rotateOut {
    from {
        -webkit-transform-origin: center;
        transform-origin: center;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: center;
        transform-origin: center;
        -webkit-transform: rotate3d(0, 0, 1, 200deg);
        transform: rotate3d(0, 0, 1, 200deg);
        opacity: 0;
    }
}

.rotateOut {
    -webkit-animation-name: rotateOut;
    animation-name: rotateOut;
}

@-webkit-keyframes rotateOutDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0;
    }
}

@keyframes rotateOutDownLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, 45deg);
        transform: rotate3d(0, 0, 1, 45deg);
        opacity: 0;
    }
}

.rotateOutDownLeft {
    -webkit-animation-name: rotateOutDownLeft;
    animation-name: rotateOutDownLeft;
}

@-webkit-keyframes rotateOutDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0;
    }
}

@keyframes rotateOutDownRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0;
    }
}

.rotateOutDownRight {
    -webkit-animation-name: rotateOutDownRight;
    animation-name: rotateOutDownRight;
}

@-webkit-keyframes rotateOutUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0;
    }
}

@keyframes rotateOutUpLeft {
    from {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: left bottom;
        transform-origin: left bottom;
        -webkit-transform: rotate3d(0, 0, 1, -45deg);
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0;
    }
}

.rotateOutUpLeft {
    -webkit-animation-name: rotateOutUpLeft;
    animation-name: rotateOutUpLeft;
}

@-webkit-keyframes rotateOutUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 90deg);
        transform: rotate3d(0, 0, 1, 90deg);
        opacity: 0;
    }
}

@keyframes rotateOutUpRight {
    from {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        opacity: 1;
    }

    to {
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
        -webkit-transform: rotate3d(0, 0, 1, 90deg);
        transform: rotate3d(0, 0, 1, 90deg);
        opacity: 0;
    }
}

.rotateOutUpRight {
    -webkit-animation-name: rotateOutUpRight;
    animation-name: rotateOutUpRight;
}

@-webkit-keyframes hinge {
    0% {
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    20%, 60% {
        -webkit-transform: rotate3d(0, 0, 1, 80deg);
        transform: rotate3d(0, 0, 1, 80deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    40%, 80% {
        -webkit-transform: rotate3d(0, 0, 1, 60deg);
        transform: rotate3d(0, 0, 1, 60deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
        opacity: 1;
    }

    to {
        -webkit-transform: translate3d(0, 700px, 0);
        transform: translate3d(0, 700px, 0);
        opacity: 0;
    }
}

@keyframes hinge {
    0% {
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    20%, 60% {
        -webkit-transform: rotate3d(0, 0, 1, 80deg);
        transform: rotate3d(0, 0, 1, 80deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    40%, 80% {
        -webkit-transform: rotate3d(0, 0, 1, 60deg);
        transform: rotate3d(0, 0, 1, 60deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
        opacity: 1;
    }

    to {
        -webkit-transform: translate3d(0, 700px, 0);
        transform: translate3d(0, 700px, 0);
        opacity: 0;
    }
}

.hinge {
    -webkit-animation-name: hinge;
    animation-name: hinge;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes rollIn {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
        transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes rollIn {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
        transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.rollIn {
    -webkit-animation-name: rollIn;
    animation-name: rollIn;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes rollOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
        transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    }
}

@keyframes rollOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
        transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    }
}

.rollOut {
    -webkit-animation-name: rollOut;
    animation-name: rollOut;
}

@-webkit-keyframes zoomIn {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }

    50% {
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }

    50% {
        opacity: 1;
    }
}

.zoomIn {
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn;
}

@-webkit-keyframes zoomInDown {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

@keyframes zoomInDown {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

.zoomInDown {
    -webkit-animation-name: zoomInDown;
    animation-name: zoomInDown;
}

@-webkit-keyframes zoomInLeft {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

@keyframes zoomInLeft {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

.zoomInLeft {
    -webkit-animation-name: zoomInLeft;
    animation-name: zoomInLeft;
}

@-webkit-keyframes zoomInRight {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

@keyframes zoomInRight {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

.zoomInRight {
    -webkit-animation-name: zoomInRight;
    animation-name: zoomInRight;
}

@-webkit-keyframes zoomInUp {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

@keyframes zoomInUp {
    from {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

.zoomInUp {
    -webkit-animation-name: zoomInUp;
    animation-name: zoomInUp;
}

@-webkit-keyframes zoomOut {
    from {
        opacity: 1;
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }

    to {
        opacity: 0;
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3);
    }

    to {
        opacity: 0;
    }
}

.zoomOut {
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut;
}

@-webkit-keyframes zoomOutDown {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

@keyframes zoomOutDown {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

.zoomOutDown {
    -webkit-animation-name: zoomOutDown;
    animation-name: zoomOutDown;
}

@-webkit-keyframes zoomOutLeft {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(-2000px, 0, 0);
        transform: scale(.1) translate3d(-2000px, 0, 0);
        -webkit-transform-origin: left center;
        transform-origin: left center;
    }
}

@keyframes zoomOutLeft {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(-2000px, 0, 0);
        transform: scale(.1) translate3d(-2000px, 0, 0);
        -webkit-transform-origin: left center;
        transform-origin: left center;
    }
}

.zoomOutLeft {
    -webkit-animation-name: zoomOutLeft;
    animation-name: zoomOutLeft;
}

@-webkit-keyframes zoomOutRight {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(2000px, 0, 0);
        transform: scale(.1) translate3d(2000px, 0, 0);
        -webkit-transform-origin: right center;
        transform-origin: right center;
    }
}

@keyframes zoomOutRight {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);
        transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);
    }

    to {
        opacity: 0;
        -webkit-transform: scale(.1) translate3d(2000px, 0, 0);
        transform: scale(.1) translate3d(2000px, 0, 0);
        -webkit-transform-origin: right center;
        transform-origin: right center;
    }
}

.zoomOutRight {
    -webkit-animation-name: zoomOutRight;
    animation-name: zoomOutRight;
}

@-webkit-keyframes zoomOutUp {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

@keyframes zoomOutUp {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

.zoomOutUp {
    -webkit-animation-name: zoomOutUp;
    animation-name: zoomOutUp;
}

@-webkit-keyframes slideInDown {
    from {
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInDown {
    from {
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.slideInDown {
    -webkit-animation-name: slideInDown;
    animation-name: slideInDown;
}

@-webkit-keyframes slideInLeft {
    from {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInLeft {
    from {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.slideInLeft {
    -webkit-animation-name: slideInLeft;
    animation-name: slideInLeft;
}

@-webkit-keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInRight {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.slideInRight {
    -webkit-animation-name: slideInRight;
    animation-name: slideInRight;
}

@-webkit-keyframes slideInUp {
    from {
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInUp {
    from {
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        visibility: visible;
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.slideInUp {
    -webkit-animation-name: slideInUp;
    animation-name: slideInUp;
}

@-webkit-keyframes slideOutDown {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@keyframes slideOutDown {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

.slideOutDown {
    -webkit-animation-name: slideOutDown;
    animation-name: slideOutDown;
}

@-webkit-keyframes slideOutLeft {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

@keyframes slideOutLeft {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

.slideOutLeft {
    -webkit-animation-name: slideOutLeft;
    animation-name: slideOutLeft;
}

@-webkit-keyframes slideOutRight {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

@keyframes slideOutRight {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

.slideOutRight {
    -webkit-animation-name: slideOutRight;
    animation-name: slideOutRight;
}

@-webkit-keyframes slideOutUp {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@keyframes slideOutUp {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        visibility: hidden;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

.slideOutUp {
    -webkit-animation-name: slideOutUp;
    animation-name: slideOutUp;
}


/* gestion des plans */
.wbPlanConteneur
{
	position: relative; /*utile pendant les anims*/
}
.wbPlanConteneur table.wbPlan,.wbPlanConteneur div.wbPlan,.wbPlanConteneur section.wbPlan
{
	display: none;
}
.wbPlanConteneur table.wbPlan.wbActif,.wbPlanConteneur table.wbPlan.wbPlanPrecedent,.wbPlanConteneur table.wbPlan.wbPlanSuivant
{
	display: table;
}
 .wbPlanConteneur div.wbPlan.wbActif
,.wbPlanConteneur div.wbPlan.wbPlanPrecedent
,.wbPlanConteneur div.wbPlan.wbPlanSuivant
,.wbPlanConteneur section.wbPlan.wbActif
,.wbPlanConteneur section.wbPlan.wbPlanPrecedent
,.wbPlanConteneur section.wbPlan.wbPlanSuivant
{
	display: block;/*inline ? car float*/
}
/*cas des bordures dans le styles des plans de bandeau défilant alors que la taille est aussi fixée sur cette balise */
.wbPlanDefilementUtilisateur.padding,.wbPlanSimple.padding
{
	box-sizing: border-box;
}
.wbPlan.wbActif {
    animation-duration: 0.75s;
}
.wbPlanEffet
{
	position: relative;
	z-index:2;
	backface-visibility: hidden;	
}
.wbPlanSimple
{
	/* améliore le rendu pour le blur */
	will-change:animation, transform, opacity, display, filter, -webkit-filter;
}
.wbPlanSimple.wbPlanEffet
{
    position: absolute !important;/*pour passer au dessus d'un relative éventuellement dans l'inline*/
    top: 0;
    left: 0;
}
.wbPlanMultiple.wbPlanEffet,.wbPlanSimple.wbPlanEffet
{
    transition-property:transform;
    transition-duration: 0s;
}

/* masque le temps que le 1er affichage se fasse */
[data-wbPlanEffet1erAffichage]
{
	opacity: 0;
}
@keyframes wbElargissement
{
    from
    {    
    	
    	opacity:0.25;
        transform:scale(0,1);  
    }
    to    
    {
    	opacity:0.5;
        transform:scale(1,1);
    }
}
.wbPlanJauge
{
    display:block;
    position:absolute;
    background-color:#b91f1f;
    bottom:1px;
    left:0;    
	transform: scale(0,1);
    will-change: transform,opacity;
    border-radius: 1px;    
    height:2px;
    width:100%;
    transform-origin:left center;

}
.wbPlanConteneur
{
    position:relative;
}

.wbPlanSimple.wbActif:not(.animated)~.wbPlanJauge
{
    animation-name:wbElargissement;
    animation-timing-function:linear;
    animation-iteration-count: 1;
    animation-fill-mode: both;
}

.wbPlanDefilementPauseSurvol:hover .wbPlanJauge
{
	animation:none !important;
}

.wbPlanJauge:before,.wbPlanJauge:after {
    position: absolute;
    top: 0;
    height: 2px;
    box-shadow: #b91f1f 1px 0 6px 1px;
    border-radius: 100%;
    content:'';
}

.wbPlanJauge:before {
    opacity: .6;
    width: 20px;
    right: 0;
    clip: rect(-6px,22px,14px,10px)
}

.wbPlanJauge:after {
    opacity: .6;
    width: 180px;
    right: -80px;
    clip: rect(-6px,90px,14px,-6px)
}

/* autres animations perso inspirées de imagehover.css */


 /* imghvr-fold-up
   ----------------------------- */
@keyframes wbFoldOutUp {
    from {
        transform:rotateX(0deg) scale(1) translateY(0%);
        transform-origin:50% 0;
    }

    to {        
		transform:rotateX(90deg) scale(0.6) translateY(50%);
		transform-origin:50% 0;
    }
}

@keyframes wbFoldInUp {
    from {
	    transform: rotateX(-90deg) translate3d(0%, -50%, 0) scale(0.6);
	    transform-origin: 50% 100%;
    }

    to {        
	    transform: rotateX(0deg) translate3d(0%, 0%, 0) scale(1);
	    transform-origin: 50% 100%;
    }
}
 

 /* imghvr-fold-down
   ----------------------------- */
@keyframes wbFoldOutDown {
    from {
        transform:rotateX(0deg) scale(1) translateY(0%);
        transform-origin: 50% 100%;
    }

    to {        
		transform: rotateX(-90deg) scale(0.6) translateY(-50%);
		transform-origin: 50% 100%;	
    }
}

@keyframes wbFoldInDown {
    from {
	    transform: rotateX(90deg) translate3d(0%, 50%, 0) scale(0.6);
	    transform-origin: 50% 0;
    }

    to {        
	    transform: rotateX(0deg) translate3d(0%, 0, 0) scale(1);
	    transform-origin: 50% 0;
    }
}

/* imghvr-fold-left
   ----------------------------- */

@keyframes wbFoldOutLeft {
    from {
        transform:rotateY(0deg) scale(1) translateX(0%);
        transform-origin: 0 50%;
    }

    to {        
		transform: rotateY(-90deg) scale(0.6) translateX(50%);
		transform-origin: 0 50%;
    }
}

@keyframes wbFoldInLeft {
    from {
	    transform: rotateY(90deg) translate3d(-50%, 0, 0) scale(0.6);
	    transform-origin: 100% 50%;
    }

    to {        
	    transform: rotateY(0deg) translate3d(0%, 0, 0) scale(1);
	    transform-origin: 100% 50%;
	}
}

/* imghvr-fold-right
   ----------------------------- */

@keyframes wbFoldOutRight {
    from {
        transform: rotateY(0deg) scale(1) translateX(0%);
        transform-origin: 100% 50%;
    }

    to {        
		transform: rotateY(90deg) scale(0.6) translateX(-50%);
		transform-origin: 100% 50%;
    }
}

@keyframes wbFoldInRight {
    from {
	    transform: rotateY(-90deg) translate3d(50%, 0, 0) scale(0.6);
	    transform-origin: 0 50%;
    }

    to {        
	    transform: rotateY(0deg) translate3d(0%, 0, 0) scale(1);
	    transform-origin: 0 50%;
	}
}

/* imghvr-blur
   ----------------------------- */

@keyframes wbBlurOut {
    from {

    }

    to {        
		  -webkit-filter: blur(30px);
		  filter: blur(30px);
		  transform: scale(1.2);
		  opacity: 0;
    }
}

@keyframes wbBlurIn {

   from {        
		  -webkit-filter: blur(30px);
		  filter: blur(30px);
		  transform: scale(1.2);
		  opacity: 0;
    }
    to {

    }
}

.wbParallaxFond
{
	background-attachment: fixed !important;
	background-size: cover;/*!important non car c'est modifié par prog pendant le défilement*/
	background-repeat: no-repeat !important;
	background-position: center center;/*!important non car c'est modifié par prog pendant le défilement*/
}

[data-wbParallaxeFond]
{
	opacity: 0;	
}

[data-wbEffetApparition]
{
	opacity: 0;
}

/*applique un overflow hidden pour ne pas voir les champs au delà du conteneur de l'animation*/
.wbEffetEnCours, table.wbEffetEnCours>tr, table.wbEffetEnCours>tr>td, table.wbEffetEnCours>tbody, table.wbEffetEnCours>tbody>tr>td, table.wbEffetEnCours>tbody>tr, table.wbEffetEnCours>tbody>tr>td,
.wbPlanDefilementUtilisateur, table.wbPlanDefilementUtilisateur>tr, table.wbPlanDefilementUtilisateur>tr>td, table.wbPlanDefilementUtilisateur>tbody, table.wbPlanDefilementUtilisateur>tbody>tr>td, table.wbPlanDefilementUtilisateur>tbody>tr, table.wbPlanDefilementUtilisateur>tbody>tr>td
{
	overflow:hidden;
	display: block;
	height:100%;/*propage l'ancrage du bandeau vers les plans*/
}

/*permet d'appliquer cette position sans utiliser style= qui est en conflit avec les ancrages sous IE */
.pr
{
	position:relative;
}

/*curseur de main fermé pendant le drag */
.wbGlisserEnCours
{
	cursor:-moz-grabbing;
	cursor:-webkit-grabbing;
	cursor:grabbing;
}

.wbPlanSuivant
{
	position:absolute !important;/*il peut y avoir un relative inline*/	
	left:100%;
	top:0;
}

.wbPlanPrecedent
{
	position:absolute !important;/*il peut y avoir un relative inline*/	
	right:100%;
	top:0;
}

/* pour une perspective de 500px */
@keyframes wbFlipDroitAncien
{

0% {transform: translateZ(-200px) rotateY(0deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateY(0deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateY(180deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateY(180deg) scale(1.4,1.4); }

}

@keyframes wbFlipDroitNouveau
{

0% {transform: translateZ(-200px) rotateY(-180deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateY(-180deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateY(0deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateY(0deg) scale(1.4,1.4); }

}

/* pour une perspective de 500px */
@keyframes wbFlipGaucheAncien
{

0% {transform: translateZ(-200px) rotateY(0deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateY(0deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateY(-180deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateY(-180deg) scale(1.4,1.4); }

}

@keyframes wbFlipGaucheNouveau
{

0% {transform: translateZ(-200px) rotateY(-180deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateY(-180deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateY(0deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateY(0deg) scale(1.4,1.4); }

}


/* pour une perspective de 500px */
@keyframes wbFlipHautAncien
{

0% {transform: translateZ(-200px) rotateX(0deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateX(0deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateX(180deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateX(180deg) scale(1.4,1.4); }

}

@keyframes wbFlipHautNouveau
{

0% {transform: translateZ(-200px) rotateX(180deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateX(180deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateX(0deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateX(0deg) scale(1.4,1.4); }

}


/* pour une perspective de 500px */
@keyframes wbFlipBasAncien
{

0% {transform: translateZ(-200px) rotateX(0deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateX(0deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateX(-180deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateX(-180deg) scale(1.4,1.4); }

}

@keyframes wbFlipBasNouveau
{

0% {transform: translateZ(-200px) rotateX(-180deg) scale(1.4,1.4); }
15% {transform: translateZ(-100px) rotateX(-180deg) scale(1.4,1.4); }
65%  {transform: translateZ(-100px) rotateX(0deg) scale(1.4,1.4); }
100% {transform: translateZ(-200px) rotateX(0deg) scale(1.4,1.4); }

}
.wbPlanDiffereChargementEnCours.wbHeureTourne 
{
    position: absolute;
    top: 0;
    left:0;
    display: block;
    vertical-align:middle;
    width: 100%;
    text-align: center;
    height:100%;
    background:rgba(125,125,125,0.15);
    opacity: 1;
    overflow:hidden;
    transition:300ms all;
}

.wbPlanDiffereChargementEnCours.wbHeureTourne:after
{
    content:'\1F550';
    animation:1s wbHeureTourne infinite, 1000ms rubberBand  infinite 500ms;
    animation-timing-function:ease;
    display: block;
    position:relative;
    top:50%;
    margin-top:-5rem;
    font-size:5rem;
    font-family: 'Arial';
    text-transform: none;
    font-style: normal;    
    color:white;
    z-index: 999;
}

@keyframes wbHeureTourne
{
    0%  { content:'\1F550'; }
    9%  { content:'\1F551'; }
    18% { content:'\1F552'; }
    27% { content:'\1F553'; }
    36% { content:'\1F554'; }
    45% { content:'\1F555'; }
    54% { content:'\1F556'; }
    63% { content:'\1F557'; }
    72% { content:'\1F558'; }
    81% { content:'\1F559'; }
    90% { content:'\1F55A'; }
    100%{ content:'\1F55B'; }
}


.wbPlanDiffereChargementEnCours.wbRayure 
{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;    
    background-image: linear-gradient( -45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%, transparent 75%, transparent );
    z-index: 99999;
    background-size: 150px 150px;
    animation: wbBgOutRight 2s linear infinite;    
    will-change:background-position;
    background-color: #555;
    opacity:0.125;
    display: block !important;
}

@keyframes wbBgOutRight {
0% {
   background-position: 0 0;
}
100% {
   background-position: 150px 150px;
}
}

.wbPlanConteneurDiffereChargementEnCours .wbPlanSimple.wbActif
{	    
    filter: blur(10px);
    opacity:0.8;
    transition: 300ms filter;
}
.wbPlanConteneurDiffereChargementEnCours:after
{
	content:'';
    position: absolute;
	left:0;
	top:0;
	width:100%;
	height:100%;
	background-position: center center;
	background-repeat: no-repeat;
}
.wbPlanConteneurDiffereChargementDefaut.wbPlanConteneurDiffereChargementEnCours:after
{
    top: 47.5%;
    left: 50%;
    bottom: 47.5%;
    height: auto;
    margin-left:-75px;
    width:150px;
    border-radius:20px;
    background-image: linear-gradient( -45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%, transparent 75%, transparent );
    z-index: 99999;
    background-size: 25px 25px;
    animation: wbBgOutRight 4s linear infinite;
    will-change:background-position;
    background-color: #555;
    opacity: 0.25;
    display: block;
    background-repeat: repeat;
}
/* évite de prendre les clics pendant un chargement <=> GFI*/
/*.wbEffetEnCours, => NON le parent ne doit pas retirer les events, vu avec FR au moment ou le survol déclenchait un affiche et du coup le pointer-events: none; retirait le survol */
.wbPlanConteneurDiffereChargementEnCours {
    pointer-events: none;
}

/* rwd flexbox */
.posfbox
{
    flex-wrap: nowrap;   
    display:flex; 
    flex-grow:1;
    flex-shrink:1;
    box-sizing: border-box;
}

.posfboxpx, .posfboxauto
{
    flex-grow:0;
    flex-shrink:0;
}

.posfboxauto
{
    flex-basis:auto;/*utile?*/
    flex-grow:0;
}

.posfboxgv/*le problème est que le gv peut se retrouver horizontal dans une autre tranche*/
{
	flex-direction: column;
}

html[dir=rtl]  .posfbox
{
	flex-direction: row-reverse;
}
html[dir=rtl]  .posfboxgv
{
	flex-direction: column-reverse;
}

[disabled],.wbgrise{
/*évite l'effet de survol sur les boutons grisés 
NON => si le survol ne doit rien faire alors c'est que l'état grisé du style doit préciser les valeurs qui sont surchargées dans hover
    pointer-events: none;
*/
	cursor: default;
}

/* wrapper d'image animée */
.wbImgAnim
{        
    height:0;
    overflow: hidden;
    position:relative;
    display: block;
}
.wbImgAnim>img
{
    width:100%;
    margin-top:-100%;
}

.wbHidden .wbImgAnim>img,.wbHidden.wbImgAnim>img, .wbDisplayNone .wbImgAnim>img,.wbDisplayNone.wbImgAnim>img
{
	/*insuffisant en cas de masquage affichage puis RE masquage ré affichage*/
    /*animation-play-state: paused;*/
    -ms-animation:none;
	-o-animation:none;
	-moz-animation:none;
	-webkit-animation:none;
    animation:none;
}

.wbDisplayNone
{
	display: none !important;
}
.wbHidden
{
	visibility: hidden !important;
}
/* retire l'oeil sous ie edge */
input[type=password]::-ms-reveal,
input[type=password]::-ms-clear
{
    display: none;
}

.wbPaire>td.wbtablesep, .wbImpaire>td.wbtablesep
{
	position: relative;/*utile pour le position absolute du séparateur de colonne redimensionnable */
	_position:static;
}

.wbOverflowHidden
{
	overflow: hidden !important;
}
.wbOverflowHiddenX
{
	overflow-x: hidden !important;
	overflow-y: auto !important;
}

.wbHnImg
{
	overflow:hidden;
	display: block;
	font-size: 0;
	line-height:0;
}

.wbHnImg>img
{
	opacity:0;
}

/* galerie */

.wbGalerie
{
	opacity:0;
	/*will-change:opacity; NON car provoque au GFI de passer au dessus...dommage*/	
	transform:translateY(25px);
}

.wbGalerieComplete 
{
	opacity:1;
	transform:none;
	transition:opacity 450ms ease-out 150ms,transform 300ms ease-out;
}

.wbGaleriePleinEcran
{
	/*box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22); non car met en avant la transparence des blancs de répétition */
	position: fixed !important;
	z-index:991;
	transition: none !important;
}

.wbGalerieColonne .wbGalerieChpRef
{
	height:auto !important;
}

.wbGalerieLigne .wbGalerieTd
{
	width:100% !important;
}
.wbRwd, .wbGalerie:not(.wbGalerieComplete)
{
	/*permet au 1er affichage de ne pas déformer le viewport le temps que le script soit exécuté*/
	/*car les champs superposables dans les répétitions sont en taille défaut => peut être desktop, donc grands*/
	/*et que le code qui prend leur width depuis le data-media de la tranche ne sera fait qu'une fois le JS ready*/
	overflow:hidden;
}
.wbGalerieTd>canvas
{
	position:absolute;
	display:none;
}
.wbGaleriePleinEcranEnCours>canvas
{
	display:block;
}

.wbGaleriePleinEcranClicFond .wbGalerieTd>table:not(.wbGaleriePleinEcran)
{
	cursor:pointer;
}

/* Ripple magic */
.wbGalerieTd>table.wbGalerieEffetRipple:active{ 
  overflow: hidden;
}

.wbGalerieTd>table.wbGalerieEffetRipple:after {
	content: '';
	position: absolute;
	opacity: 0;
  	background: rgba(255, 255, 255, 0.5);
	border-radius: 100%;
	transform: scale(0,0);
	transform-origin: 50% 50%;
	pointer-events: none;
	will-change: transform,opacity;
	

	top: 50%;
	left: 50%;
	width: 10%;/*valeur dépendante de scale(40) */
  	/*padding-top: 4%;*/  height:100%;
}

.wbGalerieTd>table.wbGalerieEffetRippleCssVars:after
{
	top: var(--wb-galerie-ripple-top,50%);
	left: var(--wb-galerie-ripple-left,50%);
	width:0;
	padding:var(--wb-galerie-ripple-padding,16px);/*vs? 1rem*/
	height:0;	
}

@keyframes rippleGalerie {
  from 	{ opacity: 1;  	transform: scale(0);	}  
  20% 	{ opacity: 1;  	transform: scale(10);	}  
  to 	{ opacity: 0; 	transform: scale(30);  	}
}
@keyframes rippleGalerieCssVars {
  from 	{ opacity: 1;  	transform: scale(0);	}  
  to 	{ opacity: 0; 	transform: scale(2.5);  	}
}

/*masque le ripple  => mais le ripple est désactivé => voir GALERIE_ZOOM_RIPPLE
.wbGaleriePleinEcranClicFond
{
	overflow: hidden;
}
*/

.wbGaleriePleinEcranClicFond .wbGalerieTd>table.wbGalerieEffetRipple:active:not(.wbGaleriePleinEcran)::after {
  /* se déclenche lorsque de le clic est maintenu pendant 300ms */
  animation: rippleGalerie 300ms ease-out/*sans délai 300ms;*/
}
.wbGaleriePleinEcranClicFond .wbGalerieTd>table.wbGalerieEffetRippleCssVars:active:not(.wbGaleriePleinEcran)::after {
  /* se déclenche lorsque de le clic est maintenu pendant 300ms */
  animation: rippleGalerieCssVars 300ms ease-out/*sans délai 300ms;*/

	/*
	Seront définis dans .wbGalerieTd>table par JS
	--wb-galerie-ripple-width:0;
	--wb-galerie-ripple-height:0;
	--wb-galerie-ripple-left: 75%;
	--wb-galerie-ripple-top: 25%;
	--wb-galerie-ripple-scale: 40;
	*/
}

/* flou d'arrière plan */
.wbGaleriePleinEcranEnCours.wbGalerieFondFlou>:not(.wbGaleriePleinEcran) 
{	
	filter: blur(5px);
}

/*	sugg picto de loading via transform?
.wbGalerieChargementVignetteImage
{
	pointer-events: none;
	opacity:0.5;
}*/

.wbGalerieActif {
    /*transform: scale(0.975) !important;
    transform-origin: center center !important;*/
    opacity:0.7 !important;
    -webkit-tap-highlight-color:transparent;/*évite le halo bleu en cas de clic long*/
}

/* table rwd compact classique */

  
.wbTableRwd
{
    /*car en passant en block le border collapse ne fonctionne plus*/
    border:0 !important;
}

.wbTableRwd.wbTableRwdTitre>tbody>tr:nth-child(2) {
    display:none;
}
.wbTableRwd.wbTableRwdTitre>tbody>tr:nth-child(3) {
    display:none;
}

.wbTableRwd.wbTableRwdSansTitre>tbody>tr:nth-child(1) {
    display:none;
}
.wbTableRwd.wbTableRwdSansTitre>tbody>tr:nth-child(2) {
    display:none;
}

.wbTableRwd>tbody>tr.wbPaire,.wbTableRwd>tbody>tr.wbImpaire
{
    display:table;
    width:100% !important;
}

.wbTableRwd>tbody>tr.wbPaire:not(:first-child)>td, .wbTableRwd>tbody>tr.wbImpaire:not(:first-child)>td
{
    width:100% !important;
    display:block;
    position:relative;


	height:auto !important; /*pour que le min height généré ne soit pas limité */

    /* perd l'alignement horizontal au profit du vertical...
    display: flex;
    align-items:center;
    */
   
   /* pas de séparateur de colonne dans le mode compact*/
   border-right-width: 0 !important;   
}

.wbTableRwd>tbody>tr.wbPaire:not(:first-child)>td:before,.wbTableRwd>tbody>tr.wbImpaire:not(:first-child)>td:before
{
    display:inline-block;
    content:attr(data-wbTableTitre);
    vertical-align: middle;

    /*vu avec SYC il faut prendre toute la hauteur et rester en mode 2 colonnes */
    height:100%;
    position:absolute;
    top:0;
    left:0;
    /* perd l'alignement horizontal au profit du vertical...
    display: flex;
    align-items:center;
    */
}

/* table rwd compacte AJAX ? prototype :

.wbTableRwd>tbody>tr:nth-child(2) {
    display:none !important;
}

.wbTableRwd>tbody>tr:nth-child(3)>td>div
{
    overflow-x:hidden !important;
}

.wbTableRwd>tbody>tr:nth-child(3)>td>div>div>table>tbody>tr
{
    display:table !important;   
    width:100% !important;   
}
.wbTableRwd>tbody>tr:nth-child(3)>td>div>div>table>tbody>tr>td
{
    display:table-row !important;   
    width:100% !important; 
}

.wbTableRwd>tbody>tr:nth-child(3)>td>div>div>table>tbody>tr>td:before
{
    display:table-cell !important;
    width:50% !important;
    content:attr(data-titre);
    vertical-align: middle;
}
.wbTableRwd>tbody>tr:nth-child(3)>td>div>div>table>tbody>tr>td>div:first-child
{
    display:table-cell !important;   
    width:50% !important;
    vertical-align: middle;
}
.wbTableRwd>tbody>tr:nth-child(3)>td>div>div>table>tbody>tr>td>div
{
    display:table-cell !important;   
    width:50% !important;
}
.wbTableRwd .wbtablesep,
.wbTableRwd>tbody>tr:nth-child(3)>td>div>div>table>tbody>tr>td.wbtablesep
{
    display:none !important;
}
.wbTableRwd [class^=wbcol]
{
    min-width:0 !important;
}
*/

/* évite d'afficher la table avant que le script ne le dessine dans son bon afficage pour la tranche en cours */
.wbRwd table[data-media][data-classRemove] { opacity: 0;     width: 0;height: 0;overflow: hidden;display: block;}


/*
    Saisie à jetons
 */

.wbSaisieJetonsWrap 
{
  display: inline-block;
  max-width: 100%;
  box-sizing: border-box;
  
  cursor: text;/*car la saisie est forcément avec curseur de texte*/
  overflow:hidden;/*car rien ne sort d'un champ de saisie normalement*/
  
  position:relative;/*pour l'éventuel absolute fils*/

  min-height: 1.3rem;/*pour le cas du champ ancré en hauteur*/
}

/*défilement horizontal des jetons dans la saisie*/
.wbSaisieJetonsWrapHorizontal
{
	white-space: nowrap;
	overflow: hidden;
	overflow-y: hidden;
	overflow-x: hidden;
}

.wbSaisieJetonsWrapVertical
{
	overflow: hidden;
	overflow-y: auto;
	overflow-x: hidden;
}

/*défaut, doit être redéfini par le style du champ*/
.wbSaisieJetonsWrapDefautStyle
{
    
	padding: 1px 0px;
    -webkit-appearance: textfield;
    background-color: white;
    -webkit-rtl-ordering: logical;
    cursor: text;
    
    border-width: 2px;
    border-style: inset;
    border-color: initial;
    border-image: initial;
    text-rendering: auto;
    color: initial;
    letter-spacing: normal;
    word-spacing: normal;
    text-transform: none;
    text-indent: 0px;
    text-shadow: none;
    display: inline-block;
    text-align: start;
    margin: 0em;
    font: 400 0.83333333125rem Arial;	

	padding: 4px 6px;/*défaut, doit être redéfini par le style du champ*/
	/*line-height: 1.375em; utile ? */
}

.wbJeton {
    display: inline-block !important;
    box-sizing: border-box;
    cursor:pointer;
    position: relative;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
    vertical-align: baseline;
    line-height: 1;
    padding: .2em 1.3em .3em 0.4em;
}

.wbJetonDefaut {
    /* font-size: 75%; */
    font-weight: 700;
    text-align: center;
    border-radius: .25em;
    color: white;
    background-color: #5bc0de;
}

.wbJeton.wbActif {
  background-color: blue;
  color: white;
}

.wbSaisieJetonsInput {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background-color: transparent !important;
  padding: 0 6px !important;
  margin: 0 !important;
  width: auto !important;
  max-width: 100% !important;
  height: 1.375em !important;
  min-height: auto !important;
  /*vu avec AV MH, on force 48px*/
  min-width:48px !important;
  box-sizing:border-box !important; 
  
  /* pour éviter la règle venant sur input directement par la feuille de navigateur et reprendre les styles de font du div wrap*/
  font: inherit;
  color: inherit;
}


.wbSaisieJetonsInput:focus {
  border: none;
  box-shadow: none;
}

.wbJeton {
  margin-right:0.4166666666666667rem;/*comme WM*/
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  padding:0.3333333333333333rem 0.5rem 0.3333333333333333rem 0.5rem;

}
.wbJeton [data-role="remove"] {
  cursor: pointer;
margin-left: 0.6666666666666666rem;
margin-right: 0.333333333333rem;  
text-align: center;
display: inline-block;
width: 0.75rem;
height: 0.75rem;
}

.wbSaisieJetonsWrapDefautCroix .wbJeton [data-role="remove"]:after {
  content: "\2715";
}

.wbSaisieJetonsSansCroix .wbJeton [data-role="remove"]
{
	display: none;
}

.wbSaisieJetonsInnerWrap
{
	width:100%;
}

.wbSaisieJetonsWrap:not(.wbFocus)>.wbSaisieJetonsInnerWrap::after {
	/*vu avec AV MH, on force 48px*/
    display: inline-block;
    content: '';
    width: 48px;
}

/*astuce pour masquer le caret au focus d'un jeton */
.wbJeton.wbFocus~input 
{
	/*valide sous chrome uniquement
    text-indent: -1px;
    margin-left: 1px !important;
    */

    color:transparent !important;
    text-shadow: 0 0 0 black !important;
}

.wbNoTransition
{
	transition: all 0s !important;
}

/* interrupteur à bascule */


.wbSelecteurInterrupteur {
  /*position: relative; gêne l'alignement vertical sous IE11, on laisse passer le relative de la table */
  -webkit-tap-highlight-color: transparent;
}

.wbInterrupteurBascule>:not(.wbgrise)/*.wbInterrupteurBascule pour éviter le curseur si grisé*/
{
	cursor: pointer;
}


.wbInterrupteurBascule-Libelle1, .wbInterrupteurBascule-Libelle2 {
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 3;
}

.wbInterrupteurBascule-Libelle2 {
  left: 50%;
}

.wbInterrupteurBascule-Input1, .wbInterrupteurBascule-Input2 {
	/*toujours displya pour choper les touches flèches*/
  opacity: 0;
  position: fixed;
  top: -99999px;
}

.wbInterrupteurBascule-Libelle1--BordChamp {
  text-align: left;
}

.wbInterrupteurBascule-Libelle2--BordChamp {
  text-align: right;
}

.wbInterrupteurBascule-Libelle1--ProcheGlissiere {
  text-align: right;
}

.wbInterrupteurBascule-Libelle2--ProcheGlissiere {
  text-align: left;
}

.wbInterrupteurBascule-Libelle1--Centre, .wbInterrupteurBascule-Libelle2--Centre {
  text-align: center;
}

.wbInterrupteurBascule-Glissiere {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;  
}

.wbInterrupteurBascule-Libelle-WrapTable {
  height: 100%;
  display: table;
  width: 100%;
}

.wbInterrupteurBascule-Libelle-WrapTableCell {
  vertical-align: middle;
  display: table-cell;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.wbInterrupteurBascule-Curseur {
  height: 100%;
  position: absolute;
  top: 0;
  left:0;
  /*au dessus des libellés */
  z-index: 4;
  width: 50%;
  transition: background-position 0s, left 250ms cubic-bezier(0.4, 0, 0.2, 1) !important;/*ease-in-out; pas de transition de background à cause de la planche*/
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;  
}

.wbInterrupteurBascule-Curseur.wbActif {
  /*cursor: grabbing;*/
  transition: all 0s !important;
}
.wbInterrupteurBascule-Libelle1,.wbInterrupteurBascule-Libelle2 {
	/*permet de jouer l'interpolation de couleur pendant clic*/
  transition: color 250ms cubic-bezier(0.4, 0, 0.2, 1);/*ease-in-out;*/
}
.wbInterrupteurBascule-Curseur.wbActif~.wbInterrupteurBascule-Libelle1,.wbInterrupteurBascule-Curseur.wbActif~.wbInterrupteurBascule-Libelle2 {
	/*permet de jouer l'interpolation de couleur pendant le drag et le masquage instantané à 50% si les libellés sont à l'extérieur l'un sur l'autre*/
  transition: color 0s, opacity 0s;
}

.wbInterrupteurBascule-Input2:checked ~ .wbInterrupteurBascule-Libelle2 {
  pointer-events: none;
}

.wbInterrupteurBascule-Input1:checked ~ .wbInterrupteurBascule-Libelle1 {
  pointer-events: none;
}

.wbInterrupteurBascule-Libelle--Draggable {  
  /*conflit avec swipe sinon*/
  pointer-events: none;
}

.t-0 {
	transition:none !important;
}

.wbVoletFlex
{
	display:flex;
}
.wbVoletFlex>td
{
	display:flex;
}
.wbVoletFlex>td.wbLargeurAdapteeVoletLibelle {
    width: auto !important;
}
.wbVoletFlex>td:last-child,.wbVoletFlex>td:last-child>div
{
	width:100% !important; /*plus que le width px inline*/
	flex-grow:1;
}

/*centrage des libellés de volets horzitaux*/
.wbVoletFlex:first-child>td>div {
    display: flex;
    align-items: center;
    justify-content: center;
    /*toujours sur une ligne?!*/
	width: auto !important;
    white-space: pre !important;    /* pre pour respecter les espaces pour se positionner */ 
    padding: 0 6px;/* toujours 6px de marge par l'éditeur de page*/
}

/* coin de rencontre des ascenseurs */
::-webkit-scrollbar-corner { background:transparent; }

.wbConteneurInfiniJauge
{
	display:none;
	width:100%;
	height:auto;
}

.wbConteneurInfiniJaugeLoader 
{
  position: relative;
  margin: 0 auto;
  width: 3rem;
}

.wbConteneurInfiniJaugeLoader:before 
{
  content: '';
  display: block;
  padding-top: 100%;
}

.wbConteneurInfiniJaugeCircular 
{  
  animation: wbConteneurInfiniJaugeRotate 2s linear infinite;
  height: 100%;
  transform-origin: center center;
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

.wbConteneurInfiniJaugePath 
{
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  animation: wbConteneurInfiniJaugeDash 1.5s ease-in-out infinite, wbConteneurInfiniJaugeColor 6s ease-in-out infinite;
  stroke-linecap: round;
}


@keyframes wbConteneurInfiniJaugeRotate {
  100% {
            transform: rotate(360deg);
  }
}

@keyframes wbConteneurInfiniJaugeDash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}

@keyframes wbConteneurInfiniJaugeColor {
  100%,
  0% {
    stroke: #d62d20;
  }
  40% {
    stroke: #0057e7;
  }
  66% {
    stroke: #008744;
  }
  80%,
  90% {
    stroke: #ffa700;
  }
}


/* rc auto et ellipse de texte */

.wbNoWrap, .wbNoWrap>*
{
    white-space: nowrap;
}
td.wbTextEllipsis
{    
    max-width: 0;
}
.wbTextEllipsis, .wbTextEllipsis>*
{
    overflow: hidden;
    text-overflow: ellipsis;
}

@media print {
	/* masque la barre d'outils pour l'impression car elle est souvent mal placée*/
	.WDBarreOutils
	{
		display:none !important;
	}

	iframe#print
	{
		position:fixed;
		top:0;
		left:0;
		width:100%;
		height:100%;
		z-index:99999;
		display: block !important;
	}

	/* par défaut tous les éléments s'impriment avec leur fond */
	* {
		/* experimental et chromium safari uniquement */
	    -webkit-print-color-adjust:exact;	
	    /* working draft */
	    color-adjust: exact;	
	}	
}

/* saisie riche edition de tableau html */

.wbSaisieRicheModalGFI {
    position: fixed;
    background: rgba(0,0,0,0.5);
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
}

.wbSaisieRicheModal {
    max-width: 500px;
    margin: 0 auto;
    transform: translateY(calc(50vh - 50%));
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
}

.wbSaisieRicheModalContenu {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: .3rem;
    outline: 0;
}

.wbSaisieRicheModalContenuPied {
    margin: 0;
    padding:1rem 0;
    border-top: 0.1rem solid #CFCFCF;
    box-sizing: border-box;
    background-color: #F5F5F5;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid #e9ecef;
}

.wbSaisieRicheModalContenuPied button {
    margin-left:1rem;
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    cursor: pointer;
}

.wbSaisieRicheModalContenuTitre {
    margin: 0;
    margin-bottom: 0;
    line-height: 1.5;
    font-size: 1.25rem;
    margin-bottom: .5rem;
    font-family: inherit;
    font-weight: 500;
    line-height: 1.2;
    color: inherit;
}

button.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
    padding: 1rem;
    margin: -1rem -1rem -1rem auto;
    appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
}

.wbSaisieRicheModalContenuEntete {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem;
}

button.wbSaisieRicheModalBoutonAnnuler {
    color: #fff;
    background-color: hsl(208 7% 62% / 1);
    border-color: rgb(151 159 165);
}
button.wbSaisieRicheModalBoutonAnnuler:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}
button.wbSaisieRicheModalBoutonOK {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    background-color: #6c757d;
    border-color: #6c757d;
}
button.wbSaisieRicheModalBoutonOK:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
    background-color: #5a6268;
    border-color: #5a6268;
}
.wbSaisieRicheModalContenuCorps {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1rem;
    max-height: 70vh;
    overflow: auto;
}

.wbSaisieRicheModal dd {
    float:left;
    display:inline-block;
    margin: 0;
    margin-top: 1rem;
    width: 75%;
}
.wbSaisieRicheModal dd {
    float:left;
    display: block;
    margin: 0;
    margin-top: 1rem;
    width: 50%;
}
.wbSaisieRicheModal dt {
    clear:left;
    float:left;
    display: block;
    margin: 0;
    margin-top: 1rem;
    padding-right: 1rem;
    box-sizing: border-box;
    max-width: 50%;
    width: 50%;
}

.wbSaisieRicheModal input[type="number"] {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    border: 1px solid #6c757d;
}