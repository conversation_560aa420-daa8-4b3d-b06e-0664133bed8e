/**
 * Code generated by WINDEV Mobile - DO NOT MODIFY!
 * WINDEV Mobile object: Fenêtre
 * Android class: tt
 * Version of wdjava64.dll: 30.0.302.8
 */


package com.amlacameroon.sandbox.wdgen;


import com.amlacameroon.sandbox.*;
import fr.pcsoft.wdjava.core.types.*;
import fr.pcsoft.wdjava.core.*;
import fr.pcsoft.wdjava.ui.champs.fenetre.*;
import fr.pcsoft.wdjava.ui.champs.bouton.*;
import fr.pcsoft.wdjava.ui.cadre.*;
import fr.pcsoft.wdjava.api.*;
import fr.pcsoft.wdjava.core.erreur.*;
import fr.pcsoft.wdjava.ui.champs.libelle.*;
import fr.pcsoft.wdjava.core.context.*;
import fr.pcsoft.wdjava.ui.actionbar.*;
import fr.pcsoft.wdjava.core.application.*;
import fr.pcsoft.wdjava.ui.activite.*;
/*Imports trouvés dans le code WL*/
/*Fin Imports trouvés dans le code WL*/



public class GWDFtt extends WDFenetre
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs de tt
////////////////////////////////////////////////////////////////////////////

/**
 * Button1
 */
class GWDButton1 extends WDBouton
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de tt.Button1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905245304202257919l);

super.setChecksum("1319792213");

super.setNom("Button1");

super.setType(4);

super.setBulle("");

super.setLibelle("Bouton");

super.setMenuContextuelSysteme();

super.setNote("", "");

super.setCurseurSouris(0);

super.setNavigable(true);

super.setEtatInitial(0);

super.setPositionInitiale(101, 287);

super.setTailleInitiale(160, 48);

super.setPlan(0);

super.setImageEtat(1);

super.setImageFondEtat(5);

super.setTailleMin(0, 0);

super.setTailleMax(2147483647, 2147483647);

super.setVisibleInitial(true);

super.setAltitude(1);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setNumTab(1);

super.setLettreAppel(65535);

super.setTypeBouton(0);

super.setTypeActionPredefinie(0);

super.setBoutonOnOff(false);

super.setTauxParallaxe(0, 0, false);

super.setLibelleVAlign(1);

super.setLibelleHAlign(5);

super.setPresenceLibelle(true);

super.setImage("", 0, 2, 1, null, null, null);

super.setOpacite(100);

super.setStyleLibelleRepos(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleSurvol(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setStyleLibelleEnfonce(getCouleur_GEN(0xffffffff), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 0, getCouleur_GEN(0xffffffff));

super.setCadreRepos(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreSurvol(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreEnfonce(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setCadreGrise(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xff000000, true), getCouleur_GEN(0xffffffff, true), 2.000000, 2.000000, 0, 1, 0, null));

super.setImageFond9Images(new int[] {1,2,1,2,2,2,1,2,1}, 10, 10, 10, 10);

super.setImageFond("C:\\Users\\<USER>\\Documents\\integrationSdk-Copy\\Gabarits\\WM\\250 Phoenix\\Phoenix_Btn_Menu@dpi1_5x.png?E5_3NP_10_10_10_10", 1, 0, 1, 1);

super.setParamAnimationChamp(41, 32, 300);
super.setParamAnimationChamp(42, 1, 200);

super.setMarges(3, 3, 2);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Click Button1
 */
public void clicSurBoutonGauche()
// chai est un buffer
// chai = "********************************************************************************************************************************************************************************"
// Caption1 = Decode(,encodeBASE64NoCR)
// Caption1 = chai.Décode(encodeBASE64)
// //chai est un buffer
{
super.clicSurBoutonGauche();

// //chai est un buffer

try
{
// Caption1 = Décrypte("********************************************************************************************************************************************************************************", "", cryptNone, encodeBASE64NoCR)
mWD_Caption1.setValeur(WDAPICrypt.decrypte(new WDChaineU("********************************************************************************************************************************************************************************"),"",0,true));

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
super.activerEcouteurClic();
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDButton1 mWD_Button1;

/**
 * Caption
 */
class GWDCaption extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°2 de tt.Caption
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905245304202388991l);

super.setChecksum("1319922829");

super.setNom("Caption");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Libellé");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(31, 76);

super.setTailleInitiale(296, 70);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(2);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

/**
 * Traitement: Initialization of Caption
 */
public void init()
// Version 1
// Description
// Simple static
// // Version 1
{
super.init();

// // Version 1

try
{
// MoiMême = "********************************************************************************************************************************************************************************"
WDContexte.getMoiMeme().setValeur("********************************************************************************************************************************************************************************");

}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCaption mWD_Caption;

/**
 * Caption1
 */
class GWDCaption1 extends WDLibelle
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°3 de tt.Caption1
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setQuid(2905245304202520063l);

super.setChecksum("1320053901");

super.setNom("Caption1");

super.setType(3);

super.setBulle("");

super.setTypeSaisie(0);

super.setMasqueSaisie(new WDChaineU("0"));

super.setLibelle("Libellé");

super.setNote("", "");

super.setCurseurSouris(0);

super.setEtatInitial(0);

super.setPositionInitiale(33, 160);

super.setTailleInitiale(296, 70);

super.setPlan(0);

super.setCadrageHorizontal(0);

super.setCadrageVertical(0);

super.setTailleMin(0, 0);

super.setTailleMax(134217727, 134217727);

super.setVisibleInitial(true);

super.setAltitude(3);

super.setAncrageInitial(0, 1000, 1000, 1000, 1000, 0, false);

super.setParamAccessibilite(false, "", "");

super.setEllipse(0);

super.setTauxParallaxe(0, 0, false);

super.setPresenceLibelle(true);

super.setStyleLibelle(getCouleur_GEN(0xff68635f), getCouleur_GEN(0xff000000, true), creerPolice_GEN("Roboto", -8.000000f, 2, 0, 1.000000f, 0.000000f), 3, 0, getCouleur_GEN(0xff000000), 0);

super.setCadreExterieur(WDCadreFactory.creerCadre_GEN(1, getCouleur_GEN(0xffe0dcda), getCouleur_GEN(0xff605c5a), getCouleur_GEN(0xff000000, true), 2.000000, 2.000000, 0, 1, 0, null), 0, 0, 0, 0);

super.setParamAnimationChamp(42, 1, 200);

super.setMiseABlancSiZero(true);

super.setOpacite(100);

super.setParamAnimationClignotementLibelle(300, getCouleur_GEN(0xffffffff, true), false);

activerEcoute();
super.terminerInitialisation();
}

// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDCaption1 mWD_Caption1;

/**
 * ActionBar
 */
class GWDActionBar extends WDActionBar
{

////////////////////////////////////////////////////////////////////////////
// Déclaration des champs du fils n°1 de tt.ActionBar
////////////////////////////////////////////////////////////////////////////

public  void initialiserObjet()
{
super.initialiserObjet();
super.setFenetre( getWDFenetreThis() );
super.setNom("ActionBar");

super.setNote("", "");

super.setParamBoutonGauche(true, 1, "", "");

super.setParamBoutonDroit(false, 0, "", "");

super.setStyleActionBar(getCouleur_GEN(0xffffffff), getCouleur_GEN(0xfff39621), true, true);

super.setImageFond("");

super.setStyleBarreNavigation(getCouleur_GEN(0xff000001, true), getCouleur_GEN(0xff808080));

super.setHauteurBarre(56);

super.terminerInitialisation();
}

////////////////////////////////////////////////////////////////////////////
public boolean isBarrePersonnalisee()
{
return false;
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
}
public GWDActionBar mWD_ActionBar;

/**
 * Traitement: Global declarations of tt
 */
// PROCEDURE MyWindow()
public void declarerGlobale(WDObjet[] WD_tabParam)
{
// PROCEDURE MyWindow()
super.declarerGlobale(WD_tabParam, 0, 0);
int WD_ntabParamLen = 0;
if(WD_tabParam!=null) WD_ntabParamLen = WD_tabParam.length;





try
{
}
catch(WDErreurNonFatale | WDException eCatch)
{
eCatch.catch_GEN();
return;
}
}




// Activation des écouteurs: 
public void activerEcoute()
{
}

////////////////////////////////////////////////////////////////////////////
// Déclaration des variables globales
////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////
// Création des champs de la fenêtre tt
////////////////////////////////////////////////////////////////////////////
protected void creerChamps()
{
mWD_Button1 = new GWDButton1();
mWD_Caption = new GWDCaption();
mWD_Caption1 = new GWDCaption1();
mWD_ActionBar = new GWDActionBar();

}
////////////////////////////////////////////////////////////////////////////
// Initialisation de la fenêtre tt
////////////////////////////////////////////////////////////////////////////
public  void initialiserObjet()
{
super.setQuid(2905245261249166509l);

super.setChecksum("1322421938");

super.setNom("tt");

super.setType(1);

super.setBulle("");

super.setMenuContextuelSysteme();

super.setCurseurSouris(0);

super.setNote("", "");

super.setCouleur(getCouleur_GEN(0xff000000));

super.setCouleurFond(getCouleur_GEN(0xffffffff));

super.setPositionInitiale(0, 0);

super.setTailleInitiale(360, 569);

super.setTitre("tt");

super.setTailleMin(-1, -1);

super.setTailleMax(20000, 20000);

super.setVisibleInitial(true);

super.setPositionFenetre(1);

super.setPersistant(true);

super.setGFI(true);

super.setAnimationFenetre(0);

super.setImageFond("", 1, 0, 1);

super.setCouleurTexteAutomatique(getCouleur_GEN(0xf4000000, true));

super.setCouleurBarreSysteme(getCouleur_GEN(0xff000001, true));

super.setCopieEcranAutorisee(true);

super.setAncrageAuContenu(1);


activerEcoute();

////////////////////////////////////////////////////////////////////////////
// Initialisation des champs de tt
////////////////////////////////////////////////////////////////////////////
mWD_Button1.initialiserObjet();
super.ajouter("Button1", mWD_Button1);
mWD_Caption.initialiserObjet();
super.ajouter("Caption", mWD_Caption);
mWD_Caption1.initialiserObjet();
super.ajouter("Caption1", mWD_Caption1);
mWD_ActionBar.initialiserObjet();
super.ajouterActionBar(mWD_ActionBar);

super.terminerInitialisation();
}

////////////////////////////////////////////////////////////////////////////
public boolean isUniteAffichageLogique()
{
return false;
}

public WDProjet getProjet()
{
return GWDPintegrationSdk.getInstance();
}

public IWDEnsembleElement getEnsemble()
{
return GWDPintegrationSdk.getInstance();
}
public int getModeContexteHF()
{
return 1;
}
/**
* Retourne le mode d'affichage de l'ActionBar de la fenêtre.
*/
public int getModeActionBar()
{
return 1;
}
/**
* Retourne vrai si la fenêtre est maximisée, faux sinon.
*/
public boolean isMaximisee()
{
return true;
}
/**
* Retourne vrai si la fenêtre a une barre de titre, faux sinon.
*/
public boolean isAvecBarreDeTitre()
{
return true;
}
/**
* Retourne le mode d'affichage de la barre système de la fenêtre.
*/
public int getModeBarreSysteme()
{
return 1;
}
/**
* Retourne vrai si la fenêtre est munie d'ascenseurs automatique, faux sinon.
*/
public boolean isAvecAscenseurAuto()
{
return true;
}
/**
* Retourne Vrai si on doit appliquer un theme "dark" (sombre) ou Faux si on doit appliquer "light" (clair) à la fenêtre.
* Ce choix se base sur la couleur du libellé par défaut dans le gabarit de la fenêtre.
*/
public boolean isGabaritSombre()
{
return false;
}
public boolean isIgnoreModeNuit()
{
return false;
}
/**
* Retourne vrai si l'option de masquage automatique de l'ActionBar lorsqu'on scrolle dans un champ de la fenêtre a été activée.
*/
public boolean isMasquageAutomatiqueActionBar()
{
return false;
}
public static class WDActiviteFenetre extends WDActivite
{
protected WDFenetre getFenetre()
{
return GWDPintegrationSdk.getInstance().mWD_tt;
}
}
/**
* Retourne le nom du gabarit associée à la fenêtre.
*/
public String getNomGabarit()
{
return "210 MATERIAL DESIGN BLUE#WM";
}
/**
* Retourne le nom de la palette associe a la fenetre.
*/
public String getNomPalette()
{
return "";
}
}
