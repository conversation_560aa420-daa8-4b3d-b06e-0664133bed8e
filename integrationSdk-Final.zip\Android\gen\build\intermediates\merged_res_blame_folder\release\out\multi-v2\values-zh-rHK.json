{"logs": [{"outputFile": "com.amlacameroon.sandbox-mergeReleaseResources-42:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b88e4f1102d8b08c17e772a2c7829ea1\\transformed\\appcompat-1.5.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,7132", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,7206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "7211", "endColumns": "100", "endOffsets": "7307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e58339227eea27976199f5cf8fe7c88\\transformed\\material-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,916,994,1053,1111,1189,1250,1307,1363,1422,1480,1534,1620,1676,1734,1788,1853,1946,2020,2098,2218,2281,2344,2421,2471,2522,2588,2652,2721,2796,2857,2928,2995,3055,3135,3198,3281,3366,3440,3505,3581,3629,3693,3769,3847,3909,3973,4036,4116,4191,4267,4343,4397,4452", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,76,49,50,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,53,54,68", "endOffsets": "242,305,366,433,502,579,669,776,849,911,989,1048,1106,1184,1245,1302,1358,1417,1475,1529,1615,1671,1729,1783,1848,1941,2015,2093,2213,2276,2339,2416,2466,2517,2583,2647,2716,2791,2852,2923,2990,3050,3130,3193,3276,3361,3435,3500,3576,3624,3688,3764,3842,3904,3968,4031,4111,4186,4262,4338,4392,4447,4516"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3118,3195,3285,3392,3465,3527,3605,3664,3722,3800,3861,3918,3974,4033,4091,4145,4231,4287,4345,4399,4464,4557,4631,4709,4829,4892,4955,5032,5082,5133,5199,5263,5332,5407,5468,5539,5606,5666,5746,5809,5892,5977,6051,6116,6192,6240,6304,6380,6458,6520,6584,6647,6727,6802,6878,6954,7008,7063", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,76,49,50,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,53,54,68", "endOffsets": "292,2916,2977,3044,3113,3190,3280,3387,3460,3522,3600,3659,3717,3795,3856,3913,3969,4028,4086,4140,4226,4282,4340,4394,4459,4552,4626,4704,4824,4887,4950,5027,5077,5128,5194,5258,5327,5402,5463,5534,5601,5661,5741,5804,5887,5972,6046,6111,6187,6235,6299,6375,6453,6515,6579,6642,6722,6797,6873,6949,7003,7058,7127"}}]}]}